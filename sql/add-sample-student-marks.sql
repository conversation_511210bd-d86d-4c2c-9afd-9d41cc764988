-- =====================================================
-- ADD SAMPLE STUDENT MARKS FOR SCORE DISTRIBUTION
-- =====================================================
-- This script adds realistic sample marks data for existing students

-- First, let's check existing students
SELECT 'Current Students:' as info;
SELECT id, student_id, name, class, section, trade, academic_session FROM students WHERE academic_session = '2023-2024' LIMIT 10;

-- Add sample subject marks for existing students
-- We'll create realistic score distributions across different performance levels

-- Insert sample marks for Student ID 1 (High Performer - 90%+)
INSERT IGNORE INTO student_subject_marks (student_id, exam_id, subject_id, theory_marks, practical_marks, internal_marks, total_marks, max_marks, percentage, grade, is_pass, academic_session) VALUES
(1, 1, 1, 85, 8, 7, 100, 100, 100.0, 'A+', TRUE, '2023-2024'),  -- English
(1, 1, 2, 82, 8, 8, 98, 100, 98.0, 'A+', TRUE, '2023-2024'),   -- Punjabi
(1, 1, 28, 88, 9, 8, 105, 100, 105.0, 'A+', TRUE, '2023-2024'), -- Physics (bonus marks)
(1, 1, 52, 85, 8, 7, 100, 100, 100.0, 'A+', TRUE, '2023-2024'), -- Chemistry
(1, 1, 53, 90, 8, 7, 105, 100, 105.0, 'A+', TRUE, '2023-2024'); -- Mathematics

-- Insert sample marks for Student ID 2 (Good Performer - 80-89%)
INSERT IGNORE INTO student_subject_marks (student_id, exam_id, subject_id, theory_marks, practical_marks, internal_marks, total_marks, max_marks, percentage, grade, is_pass, academic_session) VALUES
(2, 1, 1, 75, 7, 6, 88, 100, 88.0, 'A', TRUE, '2023-2024'),    -- English
(2, 1, 2, 78, 7, 7, 92, 100, 92.0, 'A+', TRUE, '2023-2024'),   -- Punjabi
(2, 1, 28, 72, 6, 6, 84, 100, 84.0, 'A', TRUE, '2023-2024'),   -- Physics
(2, 1, 52, 76, 7, 6, 89, 100, 89.0, 'A', TRUE, '2023-2024'),   -- Chemistry
(2, 1, 53, 70, 6, 6, 82, 100, 82.0, 'A', TRUE, '2023-2024');   -- Mathematics

-- Insert sample marks for Student ID 3 (Average Performer - 70-79%)
INSERT IGNORE INTO student_subject_marks (student_id, exam_id, subject_id, theory_marks, practical_marks, internal_marks, total_marks, max_marks, percentage, grade, is_pass, academic_session) VALUES
(3, 1, 1, 65, 6, 5, 76, 100, 76.0, 'B+', TRUE, '2023-2024'),   -- English
(3, 1, 2, 68, 6, 6, 80, 100, 80.0, 'A', TRUE, '2023-2024'),    -- Punjabi
(3, 1, 28, 62, 5, 5, 72, 100, 72.0, 'B+', TRUE, '2023-2024'),  -- Physics
(3, 1, 52, 66, 6, 5, 77, 100, 77.0, 'B+', TRUE, '2023-2024'),  -- Chemistry
(3, 1, 53, 60, 5, 5, 70, 100, 70.0, 'B+', TRUE, '2023-2024');  -- Mathematics

-- Insert sample marks for Student ID 4 (Below Average - 60-69%)
INSERT IGNORE INTO student_subject_marks (student_id, exam_id, subject_id, theory_marks, practical_marks, internal_marks, total_marks, max_marks, percentage, grade, is_pass, academic_session) VALUES
(4, 1, 1, 55, 5, 4, 64, 100, 64.0, 'B', TRUE, '2023-2024'),    -- English
(4, 1, 2, 58, 5, 5, 68, 100, 68.0, 'B', TRUE, '2023-2024'),    -- Punjabi
(4, 1, 28, 52, 4, 4, 60, 100, 60.0, 'B', TRUE, '2023-2024'),   -- Physics
(4, 1, 52, 56, 5, 4, 65, 100, 65.0, 'B', TRUE, '2023-2024'),   -- Chemistry
(4, 1, 53, 50, 4, 4, 58, 100, 58.0, 'C+', TRUE, '2023-2024');  -- Mathematics

-- Insert sample marks for Student ID 5 (Poor Performer - 50-59%)
INSERT IGNORE INTO student_subject_marks (student_id, exam_id, subject_id, theory_marks, practical_marks, internal_marks, total_marks, max_marks, percentage, grade, is_pass, academic_session) VALUES
(5, 1, 1, 45, 4, 3, 52, 100, 52.0, 'C+', TRUE, '2023-2024'),   -- English
(5, 1, 2, 48, 4, 4, 56, 100, 56.0, 'C+', TRUE, '2023-2024'),   -- Punjabi
(5, 1, 28, 42, 3, 3, 48, 100, 48.0, 'C', TRUE, '2023-2024'),   -- Physics
(5, 1, 52, 46, 4, 3, 53, 100, 53.0, 'C+', TRUE, '2023-2024'),  -- Chemistry
(5, 1, 53, 40, 3, 3, 46, 100, 46.0, 'C', TRUE, '2023-2024');   -- Mathematics

-- Insert sample marks for Student ID 6 (Failing - Below 40%)
INSERT IGNORE INTO student_subject_marks (student_id, exam_id, subject_id, theory_marks, practical_marks, internal_marks, total_marks, max_marks, percentage, grade, is_pass, academic_session) VALUES
(6, 1, 1, 25, 2, 2, 29, 100, 29.0, 'F', FALSE, '2023-2024'),   -- English
(6, 1, 2, 28, 2, 3, 33, 100, 33.0, 'D', TRUE, '2023-2024'),    -- Punjabi
(6, 1, 28, 22, 2, 1, 25, 100, 25.0, 'F', FALSE, '2023-2024'),  -- Physics
(6, 1, 52, 26, 2, 2, 30, 100, 30.0, 'F', FALSE, '2023-2024'),  -- Chemistry
(6, 1, 53, 20, 1, 1, 22, 100, 22.0, 'F', FALSE, '2023-2024');  -- Mathematics

-- Insert sample marks for Student ID 7 (Mixed Performance - 40-49%)
INSERT IGNORE INTO student_subject_marks (student_id, exam_id, subject_id, theory_marks, practical_marks, internal_marks, total_marks, max_marks, percentage, grade, is_pass, academic_session) VALUES
(7, 1, 1, 35, 3, 3, 41, 100, 41.0, 'C', TRUE, '2023-2024'),    -- English
(7, 1, 2, 38, 3, 4, 45, 100, 45.0, 'C', TRUE, '2023-2024'),    -- Punjabi
(7, 1, 28, 32, 2, 2, 36, 100, 36.0, 'D', TRUE, '2023-2024'),   -- Physics
(7, 1, 52, 36, 3, 3, 42, 100, 42.0, 'C', TRUE, '2023-2024'),   -- Chemistry
(7, 1, 53, 30, 2, 2, 34, 100, 34.0, 'D', TRUE, '2023-2024');   -- Mathematics

-- Insert sample marks for Student ID 8 (Excellent Performer - 95%+)
INSERT IGNORE INTO student_subject_marks (student_id, exam_id, subject_id, theory_marks, practical_marks, internal_marks, total_marks, max_marks, percentage, grade, is_pass, academic_session) VALUES
(8, 1, 1, 92, 9, 9, 110, 100, 110.0, 'A+', TRUE, '2023-2024'), -- English (bonus)
(8, 1, 2, 90, 9, 8, 107, 100, 107.0, 'A+', TRUE, '2023-2024'), -- Punjabi (bonus)
(8, 1, 28, 95, 10, 9, 114, 100, 114.0, 'A+', TRUE, '2023-2024'), -- Physics (bonus)
(8, 1, 52, 88, 9, 8, 105, 100, 105.0, 'A+', TRUE, '2023-2024'), -- Chemistry
(8, 1, 53, 93, 9, 8, 110, 100, 110.0, 'A+', TRUE, '2023-2024'); -- Mathematics

-- Add more diverse marks for additional students if they exist
INSERT IGNORE INTO student_subject_marks (student_id, exam_id, subject_id, theory_marks, practical_marks, internal_marks, total_marks, max_marks, percentage, grade, is_pass, academic_session)
SELECT 
    s.id,
    1,
    sub.id,
    -- Generate varied marks based on student ID for realistic distribution
    CASE 
        WHEN s.id % 8 = 1 THEN FLOOR(85 + RAND() * 15)  -- High performers (85-100)
        WHEN s.id % 8 = 2 THEN FLOOR(75 + RAND() * 15)  -- Good performers (75-90)
        WHEN s.id % 8 = 3 THEN FLOOR(65 + RAND() * 15)  -- Average performers (65-80)
        WHEN s.id % 8 = 4 THEN FLOOR(55 + RAND() * 15)  -- Below average (55-70)
        WHEN s.id % 8 = 5 THEN FLOOR(45 + RAND() * 15)  -- Poor performers (45-60)
        WHEN s.id % 8 = 6 THEN FLOOR(25 + RAND() * 15)  -- Failing (25-40)
        WHEN s.id % 8 = 7 THEN FLOOR(35 + RAND() * 15)  -- Mixed (35-50)
        ELSE FLOOR(80 + RAND() * 20)                     -- Excellent (80-100)
    END as theory_marks,
    FLOOR(3 + RAND() * 7) as practical_marks,  -- 3-10 practical marks
    FLOOR(3 + RAND() * 7) as internal_marks,   -- 3-10 internal marks
    0, -- Will be calculated
    100,
    0, -- Will be calculated
    'A+',
    TRUE,
    '2023-2024'
FROM students s
CROSS JOIN subjects sub
WHERE s.academic_session = '2023-2024' 
AND s.id > 8 
AND s.id <= 20
AND sub.id IN (1, 2, 28, 52, 53)  -- Core subjects
LIMIT 60;

-- Update total marks and percentage for the generated records
UPDATE student_subject_marks 
SET total_marks = theory_marks + practical_marks + internal_marks,
    percentage = ROUND((theory_marks + practical_marks + internal_marks), 2),
    grade = CASE 
        WHEN (theory_marks + practical_marks + internal_marks) >= 90 THEN 'A+'
        WHEN (theory_marks + practical_marks + internal_marks) >= 80 THEN 'A'
        WHEN (theory_marks + practical_marks + internal_marks) >= 70 THEN 'B+'
        WHEN (theory_marks + practical_marks + internal_marks) >= 60 THEN 'B'
        WHEN (theory_marks + practical_marks + internal_marks) >= 50 THEN 'C+'
        WHEN (theory_marks + practical_marks + internal_marks) >= 40 THEN 'C'
        WHEN (theory_marks + practical_marks + internal_marks) >= 33 THEN 'D'
        ELSE 'F'
    END,
    is_pass = CASE 
        WHEN (theory_marks + practical_marks + internal_marks) >= 33 THEN TRUE
        ELSE FALSE
    END
WHERE academic_session = '2023-2024';

-- Show summary of added data
SELECT 'Sample Data Summary:' as info;
SELECT 
    grade,
    COUNT(*) as count,
    ROUND(AVG(percentage), 2) as avg_percentage,
    MIN(percentage) as min_percentage,
    MAX(percentage) as max_percentage
FROM student_subject_marks 
WHERE academic_session = '2023-2024'
GROUP BY grade
ORDER BY avg_percentage DESC;

SELECT 'Students with Marks:' as info;
SELECT COUNT(DISTINCT student_id) as students_with_marks 
FROM student_subject_marks 
WHERE academic_session = '2023-2024';

SELECT 'Total Mark Records:' as info;
SELECT COUNT(*) as total_records 
FROM student_subject_marks 
WHERE academic_session = '2023-2024';
