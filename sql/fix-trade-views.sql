-- =====================================================================================
-- FIX TRADE VIEWS - CORRECT SUBJECT VS TRADE CONFUSION
-- =====================================================================================
-- This script fixes views that incorrectly use subjects as trades
-- Proper trades should be: Medical, Non-Medical, Commerce (not Physics, Chemistry, etc.)
-- =====================================================================================

-- First, let's check the current student table structure to understand the mapping
SELECT 'CURRENT STUDENT TRADE MAPPING ISSUE:' AS info;
SELECT DISTINCT trade, COUNT(*) as student_count 
FROM students 
WHERE trade IS NOT NULL 
GROUP BY trade 
ORDER BY trade;

-- =====================================================================================
-- SECTION 1: CREATE PROPER TRADE MAPPING
-- =====================================================================================

-- Create a mapping table to convert subject-based "trades" to actual trades
CREATE TABLE IF NOT EXISTS trade_mapping (
    id INT AUTO_INCREMENT PRIMARY KEY,
    old_trade_value VARCHAR(100) NOT NULL, -- Current incorrect values (subjects)
    correct_trade_code VARCHAR(20) NOT NULL, -- Correct trade codes
    correct_trade_name VARCHAR(100) NOT NULL, -- Correct trade names
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_old_trade (old_trade_value)
);

-- Insert the correct mappings
INSERT IGNORE INTO trade_mapping (old_trade_value, correct_trade_code, correct_trade_name) VALUES
-- Science subjects should map to appropriate trades
('Physics', 'NON_MED', 'Non-Medical'),
('Chemistry', 'MED', 'Medical'), -- Could be either, but let's default to Medical
('Biology', 'MED', 'Medical'),
('Mathematics', 'NON_MED', 'Non-Medical'),
('Computer Science', 'NON_MED', 'Non-Medical'),
-- If there are any actual trade names, keep them
('Medical', 'MED', 'Medical'),
('Non-Medical', 'NON_MED', 'Non-Medical'),
('Commerce', 'COMM', 'Commerce'),
-- Handle any other subjects that might be used as trades
('Accountancy', 'COMM', 'Commerce'),
('Business Studies', 'COMM', 'Commerce'),
('Economics', 'COMM', 'Commerce'),
('English', 'COMM', 'Commerce'), -- Default for language subjects
('Punjabi', 'COMM', 'Commerce');

-- =====================================================================================
-- SECTION 2: CREATE CORRECTED VIEWS
-- =====================================================================================

-- Drop and recreate the problematic views with correct trade logic

-- Fix v_trade_performance view
DROP VIEW IF EXISTS v_trade_performance;
CREATE VIEW v_trade_performance AS
SELECT 
    ssm.academic_session,
    COALESCE(tm.correct_trade_name, s.trade, 'Unknown') AS trade,
    tm.correct_trade_code AS trade_code,
    
    -- Student statistics
    COUNT(DISTINCT s.id) AS total_students,
    COUNT(ssm.exam_id) AS total_exam_instances,
    COUNT(DISTINCT ssm.subject_id) AS subjects_covered,
    COUNT(DISTINCT s.class) AS classes_involved,
    
    -- Performance metrics
    ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) AS trade_average_percentage,
    MAX((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) AS highest_score,
    MIN((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) AS lowest_score,
    ROUND(STDDEV((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) AS score_std_deviation,
    
    -- Component averages
    ROUND(AVG(ssm.theory_marks), 2) AS avg_theory_performance,
    ROUND(AVG(ssm.practical_marks), 2) AS avg_practical_performance,
    ROUND(AVG(ssm.internal_marks), 2) AS avg_cce_performance,
    
    -- Performance categories
    SUM(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 90 THEN 1 ELSE 0 END) AS excellent_performers,
    SUM(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 BETWEEN 70 AND 89.99 THEN 1 ELSE 0 END) AS good_performers,
    SUM(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 BETWEEN 50 AND 69.99 THEN 1 ELSE 0 END) AS average_performers,
    SUM(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 < 50 THEN 1 ELSE 0 END) AS poor_performers,
    
    -- Pass rate
    ROUND(SUM(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 33 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS pass_rate,
    
    -- Performance rating
    CASE 
        WHEN AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) >= 80 THEN 'EXCELLENT'
        WHEN AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) >= 70 THEN 'VERY GOOD'
        WHEN AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) >= 60 THEN 'GOOD'
        WHEN AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) >= 50 THEN 'AVERAGE'
        ELSE 'BELOW AVERAGE'
    END AS trade_performance_rating
    
FROM students s
JOIN student_subject_marks ssm ON s.id = ssm.student_id
LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
WHERE s.trade IS NOT NULL
GROUP BY ssm.academic_session, COALESCE(tm.correct_trade_name, s.trade), tm.correct_trade_code
ORDER BY ssm.academic_session, ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) DESC;

-- Fix v_trade_toppers view
DROP VIEW IF EXISTS v_trade_toppers;
CREATE VIEW v_trade_toppers AS
SELECT 
    ranked_students.academic_session,
    ranked_students.trade,
    ranked_students.trade_code,
    ranked_students.roll_number,
    ranked_students.student_name,
    ranked_students.class,
    ranked_students.section,
    ranked_students.session_average_percentage,
    ranked_students.total_subjects,
    ranked_students.exams_passed,
    ranked_students.session_result,
    ranked_students.trade_rank
FROM (
    SELECT 
        sss.roll_number,
        sss.student_name,
        sss.class,
        sss.section,
        COALESCE(tm.correct_trade_name, sss.trade, 'Unknown') AS trade,
        tm.correct_trade_code AS trade_code,
        sss.academic_session,
        sss.total_subjects,
        sss.total_exams_taken,
        sss.session_average_percentage,
        sss.highest_percentage,
        sss.lowest_percentage,
        sss.excellent_grades,
        sss.good_grades,
        sss.average_grades,
        sss.failed_grades,
        sss.exams_passed,
        sss.exams_failed,
        sss.pass_rate,
        sss.session_result,
        RANK() OVER (
            PARTITION BY sss.academic_session, COALESCE(tm.correct_trade_name, sss.trade)
            ORDER BY sss.session_average_percentage DESC, sss.exams_passed DESC
        ) AS trade_rank
    FROM v_student_session_summary sss
    LEFT JOIN trade_mapping tm ON sss.trade = tm.old_trade_value
    WHERE sss.session_result IN ('PROMOTED', 'CONDITIONAL') 
    AND sss.trade IS NOT NULL
) ranked_students
WHERE ranked_students.trade_rank <= 5
ORDER BY ranked_students.academic_session, ranked_students.trade, ranked_students.trade_rank;

-- =====================================================================================
-- SECTION 3: CREATE ENHANCED TRADE CURRICULUM VIEW
-- =====================================================================================

-- Create a comprehensive trade curriculum view that shows proper trade-subject relationships
CREATE OR REPLACE VIEW v_corrected_trade_curriculum AS
SELECT 
    at.trade_code,
    at.trade_name,
    s.name AS subject_name,
    s.code AS subject_code,
    stc.is_compulsory,
    stc.is_elective,
    CASE 
        WHEN stc.is_compulsory = 1 THEN 'Compulsory'
        WHEN stc.is_elective = 1 THEN 'Elective'
        ELSE 'Optional'
    END AS subject_type,
    s.max_theory_lectures,
    s.max_practical_lectures,
    s.total_lectures_per_week
FROM academic_trades at
JOIN subject_trade_combinations stc ON at.id = stc.trade_id
JOIN subjects s ON stc.subject_id = s.id
ORDER BY at.trade_code, stc.is_compulsory DESC, s.name;

-- =====================================================================================
-- SECTION 4: VERIFICATION QUERIES
-- =====================================================================================

-- Show the corrected trade performance
SELECT 'CORRECTED TRADE PERFORMANCE:' AS info;
SELECT trade, trade_code, total_students, trade_average_percentage, pass_rate, trade_performance_rating
FROM v_trade_performance
ORDER BY trade_average_percentage DESC;

-- Show the corrected trade toppers
SELECT 'CORRECTED TRADE TOPPERS:' AS info;
SELECT academic_session, trade, trade_code, student_name, class, session_average_percentage, trade_rank
FROM v_trade_toppers
WHERE academic_session = '2023-2024'
ORDER BY trade, trade_rank;

-- Show the proper trade curriculum
SELECT 'PROPER TRADE CURRICULUM:' AS info;
SELECT trade_code, trade_name, subject_name, subject_type, is_compulsory, is_elective
FROM v_corrected_trade_curriculum
ORDER BY trade_code, is_compulsory DESC, subject_name;
