-- =====================================================================================
-- STUDENT COMPLETE RECORD SYSTEM - FIXED VERSION
-- =====================================================================================
-- Create comprehensive student record views with all exams, teachers, and attendance
-- Fixed to work with existing table structure
-- =====================================================================================

-- =====================================================================================
-- SECTION 1: ENHANCE EXISTING TABLES FOR COMPLETE RECORDS
-- =====================================================================================

-- Add subject teacher assignment table if not exists
CREATE TABLE IF NOT EXISTS subject_teachers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_id INT NOT NULL,
    teacher_id INT NOT NULL,
    class_trade_section_id INT,
    academic_session VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    assigned_date DATE DEFAULT (CURRENT_DATE),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (class_trade_section_id) REFERENCES class_trade_sections(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_subject_teacher_class (subject_id, teacher_id, class_trade_section_id, academic_session),
    INDEX idx_teacher_subject (teacher_id, subject_id),
    INDEX idx_class_session (class_trade_section_id, academic_session)
);

-- Add class incharge assignment table if not exists
CREATE TABLE IF NOT EXISTS class_incharge (
    id INT AUTO_INCREMENT PRIMARY KEY,
    teacher_id INT NOT NULL,
    class_trade_section_id INT NOT NULL,
    academic_session VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    assigned_date DATE DEFAULT (CURRENT_DATE),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (class_trade_section_id) REFERENCES class_trade_sections(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_class_incharge (class_trade_section_id, academic_session),
    INDEX idx_teacher_class (teacher_id, class_trade_section_id),
    INDEX idx_session_class (academic_session, class_trade_section_id)
);

-- Add student attendance table if not exists
CREATE TABLE IF NOT EXISTS student_attendance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    attendance_date DATE NOT NULL,
    is_present BOOLEAN DEFAULT TRUE,
    attendance_type ENUM('present', 'absent', 'late', 'excused') DEFAULT 'present',
    marked_by INT,
    academic_session VARCHAR(20) NOT NULL,
    remarks TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (marked_by) REFERENCES users(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_student_subject_date (student_id, subject_id, attendance_date),
    INDEX idx_student_session (student_id, academic_session),
    INDEX idx_subject_date (subject_id, attendance_date)
);

-- =====================================================================================
-- SECTION 2: INSERT SAMPLE DATA FOR DEMONSTRATION
-- =====================================================================================

-- Insert sample subject teachers (using class_trade_section_id)
INSERT IGNORE INTO subject_teachers (subject_id, teacher_id, class_trade_section_id, academic_session) VALUES
-- For existing class_trade_sections
(1, 1, 1, '2023-2024'), -- Subject 1, Teacher 1, Class-Trade-Section 1
(2, 2, 1, '2023-2024'), -- Subject 2, Teacher 2, Class-Trade-Section 1
(3, 3, 1, '2023-2024'), -- Subject 3, Teacher 3, Class-Trade-Section 1
(4, 4, 1, '2023-2024'), -- Subject 4, Teacher 4, Class-Trade-Section 1
(5, 5, 1, '2023-2024'), -- Subject 5, Teacher 5, Class-Trade-Section 1
(1, 1, 2, '2023-2024'), -- Subject 1, Teacher 1, Class-Trade-Section 2
(2, 2, 2, '2023-2024'), -- Subject 2, Teacher 2, Class-Trade-Section 2
(3, 3, 2, '2023-2024'), -- Subject 3, Teacher 3, Class-Trade-Section 2
(4, 4, 2, '2023-2024'), -- Subject 4, Teacher 4, Class-Trade-Section 2
(5, 5, 2, '2023-2024'); -- Subject 5, Teacher 5, Class-Trade-Section 2

-- Insert sample class incharge
INSERT IGNORE INTO class_incharge (teacher_id, class_trade_section_id, academic_session) VALUES
(1, 1, '2023-2024'), -- Teacher 1 as incharge for Class-Trade-Section 1
(2, 2, '2023-2024'), -- Teacher 2 as incharge for Class-Trade-Section 2
(3, 3, '2023-2024'), -- Teacher 3 as incharge for Class-Trade-Section 3
(4, 4, '2023-2024'), -- Teacher 4 as incharge for Class-Trade-Section 4
(5, 5, '2023-2024'); -- Teacher 5 as incharge for Class-Trade-Section 5

-- Insert sample attendance data
INSERT IGNORE INTO student_attendance (student_id, subject_id, attendance_date, is_present, attendance_type, academic_session) VALUES
-- Student 1 attendance
(1, 1, '2023-09-01', TRUE, 'present', '2023-2024'),
(1, 2, '2023-09-01', TRUE, 'present', '2023-2024'),
(1, 3, '2023-09-01', FALSE, 'absent', '2023-2024'),
(1, 4, '2023-09-01', TRUE, 'present', '2023-2024'),
(1, 1, '2023-09-02', TRUE, 'present', '2023-2024'),
(1, 2, '2023-09-02', TRUE, 'present', '2023-2024'),
(1, 3, '2023-09-02', TRUE, 'present', '2023-2024'),
(1, 4, '2023-09-02', FALSE, 'absent', '2023-2024'),

-- Student 2 attendance
(2, 1, '2023-09-01', TRUE, 'present', '2023-2024'),
(2, 2, '2023-09-01', TRUE, 'present', '2023-2024'),
(2, 3, '2023-09-01', TRUE, 'present', '2023-2024'),
(2, 4, '2023-09-01', TRUE, 'present', '2023-2024'),
(2, 1, '2023-09-02', FALSE, 'absent', '2023-2024'),
(2, 2, '2023-09-02', TRUE, 'present', '2023-2024'),
(2, 3, '2023-09-02', TRUE, 'present', '2023-2024'),
(2, 4, '2023-09-02', TRUE, 'present', '2023-2024');

-- =====================================================================================
-- SECTION 3: COMPREHENSIVE STUDENT RECORD VIEWS
-- =====================================================================================

-- Main view: Complete student record with all exams and teachers
CREATE OR REPLACE VIEW v_student_complete_record AS
SELECT 
    -- Student Information
    s.id AS student_id,
    s.student_id AS roll_number,
    s.name AS student_name,
    s.father_name,
    s.mother_name,
    s.class AS class_level,
    s.section,
    s.trade,
    s.academic_session,
    
    -- Trade Information (from mapping)
    tm.correct_trade_name AS trade_full_name,
    tm.correct_trade_code AS trade_code,
    
    -- Class-Trade-Section Information
    cts.id AS class_trade_section_id,
    at.trade_name AS actual_trade_name,
    asec.section_name AS section_full_name,
    
    -- Class Incharge Information
    ci_user.name AS class_incharge_name,
    ci_user.email AS class_incharge_email,
    ci_user.phone AS class_incharge_phone,
    
    -- Subject Information
    sub.id AS subject_id,
    sub.code AS subject_code,
    sub.name AS subject_name,
    sub.subject_category_new AS subject_category,
    sub.stream_group,
    
    -- Subject Teacher Information
    st_user.name AS subject_teacher_name,
    st_user.email AS subject_teacher_email,
    st_user.phone AS subject_teacher_phone,
    
    -- Exam Information
    e.exam_id,
    e.exam_name,
    e.exam_type,
    e.exam_date,
    e.max_marks AS exam_max_marks,
    
    -- Marks Information
    ssm.theory_marks,
    ssm.practical_marks,
    ssm.internal_marks AS cce_marks,
    (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) AS total_marks,
    ssm.max_marks,
    ROUND(((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100, 2) AS percentage,
    
    -- Grade Calculation
    CASE 
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 90 THEN 'A+'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 80 THEN 'A'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 70 THEN 'B+'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 60 THEN 'B'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 50 THEN 'C+'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 40 THEN 'C'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 33 THEN 'D'
        ELSE 'F'
    END AS grade,
    
    -- Result Status
    CASE 
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 33 
        THEN 'PASS' 
        ELSE 'FAIL' 
    END AS result_status,
    
    -- Additional Information
    ssm.remarks AS exam_remarks,
    ssm.created_at AS marks_entry_date
    
FROM students s
LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
LEFT JOIN subjects sub ON ssm.subject_id = sub.id
LEFT JOIN exams e ON ssm.exam_id = e.exam_id
LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
LEFT JOIN class_trade_sections cts ON s.class = cts.class_level 
    AND s.academic_session = cts.academic_session
LEFT JOIN academic_trades at ON cts.trade_id = at.id
LEFT JOIN academic_sections asec ON cts.section_id = asec.id
LEFT JOIN class_incharge ci ON cts.id = ci.class_trade_section_id 
    AND s.academic_session = ci.academic_session 
    AND ci.is_active = TRUE
LEFT JOIN users ci_user ON ci.teacher_id = ci_user.id
LEFT JOIN subject_teachers st ON sub.id = st.subject_id 
    AND cts.id = st.class_trade_section_id 
    AND s.academic_session = st.academic_session 
    AND st.is_active = TRUE
LEFT JOIN users st_user ON st.teacher_id = st_user.id
ORDER BY s.student_id, s.academic_session, e.exam_date, sub.code;

-- Student attendance summary view
CREATE OR REPLACE VIEW v_student_attendance_summary AS
SELECT
    s.id AS student_id,
    s.student_id AS roll_number,
    s.name AS student_name,
    s.class AS class_level,
    s.section,
    s.trade,
    s.academic_session,

    -- Subject Information
    sub.code AS subject_code,
    sub.name AS subject_name,

    -- Attendance Statistics
    COUNT(sa.id) AS total_attendance_days,
    SUM(CASE WHEN sa.is_present = TRUE THEN 1 ELSE 0 END) AS days_present,
    SUM(CASE WHEN sa.is_present = FALSE THEN 1 ELSE 0 END) AS days_absent,
    SUM(CASE WHEN sa.attendance_type = 'late' THEN 1 ELSE 0 END) AS days_late,
    SUM(CASE WHEN sa.attendance_type = 'excused' THEN 1 ELSE 0 END) AS days_excused,

    -- Attendance Percentage
    ROUND((SUM(CASE WHEN sa.is_present = TRUE THEN 1 ELSE 0 END) * 100.0 / COUNT(sa.id)), 2) AS attendance_percentage,

    -- Attendance Status
    CASE
        WHEN ROUND((SUM(CASE WHEN sa.is_present = TRUE THEN 1 ELSE 0 END) * 100.0 / COUNT(sa.id)), 2) >= 75 THEN 'GOOD'
        WHEN ROUND((SUM(CASE WHEN sa.is_present = TRUE THEN 1 ELSE 0 END) * 100.0 / COUNT(sa.id)), 2) >= 60 THEN 'AVERAGE'
        ELSE 'POOR'
    END AS attendance_status,

    -- Subject Teacher
    st_user.name AS subject_teacher_name

FROM students s
LEFT JOIN student_attendance sa ON s.id = sa.student_id AND s.academic_session = sa.academic_session
LEFT JOIN subjects sub ON sa.subject_id = sub.id
LEFT JOIN class_trade_sections cts ON s.class = cts.class_level
    AND s.academic_session = cts.academic_session
LEFT JOIN subject_teachers st ON sub.id = st.subject_id
    AND cts.id = st.class_trade_section_id
    AND s.academic_session = st.academic_session
    AND st.is_active = TRUE
LEFT JOIN users st_user ON st.teacher_id = st_user.id
WHERE sa.id IS NOT NULL
GROUP BY s.id, sub.id, s.academic_session
ORDER BY s.student_id, sub.code;

-- =====================================================================================
-- SECTION 4: VERIFICATION AND SAMPLE QUERIES
-- =====================================================================================

-- Test the complete student record system
SELECT 'STUDENT COMPLETE RECORD SYSTEM - VERIFICATION:' AS info;

-- Sample query: Get complete record for a specific student
SELECT 'COMPLETE RECORD FOR STUDENT (SAMPLE):' AS info;
SELECT
    roll_number,
    student_name,
    class_level,
    trade_full_name,
    class_incharge_name,
    subject_name,
    subject_teacher_name,
    exam_name,
    total_marks,
    percentage,
    grade,
    result_status
FROM v_student_complete_record
WHERE student_id = 1 AND academic_session = '2023-2024'
ORDER BY exam_date, subject_code
LIMIT 10;

-- Sample query: Get attendance summary for a student
SELECT 'STUDENT ATTENDANCE SUMMARY (SAMPLE):' AS info;
SELECT
    roll_number,
    student_name,
    subject_name,
    subject_teacher_name,
    total_attendance_days,
    days_present,
    days_absent,
    attendance_percentage,
    attendance_status
FROM v_student_attendance_summary
WHERE student_id = 1 AND academic_session = '2023-2024'
ORDER BY subject_code;

-- Verification queries
SELECT 'SYSTEM TABLES VERIFICATION:' AS info;
SELECT
    'subject_teachers' AS table_name,
    COUNT(*) AS record_count
FROM subject_teachers
UNION ALL
SELECT
    'class_incharge' AS table_name,
    COUNT(*) AS record_count
FROM class_incharge
UNION ALL
SELECT
    'student_attendance' AS table_name,
    COUNT(*) AS record_count
FROM student_attendance;

-- Show available views
SELECT 'AVAILABLE VIEWS FOR STUDENT RECORDS:' AS info;
SELECT
    'v_student_complete_record' AS view_name,
    'Complete student record with all exams, teachers, and marks' AS description
UNION ALL
SELECT
    'v_student_attendance_summary' AS view_name,
    'Subject-wise attendance summary with teacher information' AS description;
