-- =====================================================
-- CUSTOM RANGES CONFIGURATION TABLE
-- =====================================================
-- This table stores session-specific custom ranges for score distribution analysis

CREATE TABLE IF NOT EXISTS custom_score_ranges (
    id INT AUTO_INCREMENT PRIMARY KEY,
    academic_session VARCHAR(20) NOT NULL,
    range_name VARCHAR(50) NOT NULL,
    min_percentage DECIMAL(5,2) NOT NULL,
    max_percentage DECIMAL(5,2) NOT NULL,
    range_label VARCHAR(100) NOT NULL,
    display_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_session_active (academic_session, is_active),
    INDEX idx_session_order (academic_session, display_order),
    INDEX idx_session_range (academic_session, min_percentage, max_percentage),
    
    -- Constraints
    CONSTRAINT chk_valid_percentage_range CHECK (min_percentage >= 0 AND max_percentage <= 100 AND min_percentage < max_percentage),
    CONSTRAINT chk_valid_order CHECK (display_order >= 0)
);

-- Insert default ranges for current session (2023-2024)
INSERT IGNORE INTO custom_score_ranges 
(academic_session, range_name, min_percentage, max_percentage, range_label, display_order, created_by) 
VALUES
('2023-2024', 'excellent', 90.00, 100.00, '90-100 (Excellent)', 1, 'system'),
('2023-2024', 'very_good', 80.00, 89.99, '80-89 (Very Good)', 2, 'system'),
('2023-2024', 'good', 70.00, 79.99, '70-79 (Good)', 3, 'system'),
('2023-2024', 'above_average', 60.00, 69.99, '60-69 (Above Average)', 4, 'system'),
('2023-2024', 'average', 50.00, 59.99, '50-59 (Average)', 5, 'system'),
('2023-2024', 'below_average', 40.00, 49.99, '40-49 (Below Average)', 6, 'system'),
('2023-2024', 'poor', 0.00, 39.99, 'Below 40 (Poor)', 7, 'system');

-- Create a view for easy access to current session ranges
CREATE OR REPLACE VIEW v_current_score_ranges AS
SELECT 
    id,
    academic_session,
    range_name,
    min_percentage,
    max_percentage,
    range_label,
    display_order,
    is_active,
    created_by,
    created_at,
    updated_at
FROM custom_score_ranges
WHERE is_active = TRUE
ORDER BY academic_session, display_order;

-- Create stored procedure to get ranges for a specific session
DELIMITER //
CREATE PROCEDURE GetCustomRangesForSession(IN session_name VARCHAR(20))
BEGIN
    SELECT 
        id,
        range_name,
        min_percentage as min,
        max_percentage as max,
        range_label as label,
        display_order
    FROM custom_score_ranges
    WHERE academic_session = session_name 
    AND is_active = TRUE
    ORDER BY display_order;
END //
DELIMITER ;

-- Create stored procedure to update ranges for a session
DELIMITER //
CREATE PROCEDURE UpdateCustomRangesForSession(
    IN session_name VARCHAR(20),
    IN ranges_json JSON,
    IN updated_by VARCHAR(100)
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE range_index INT DEFAULT 0;
    DECLARE range_count INT;
    DECLARE current_range JSON;
    
    -- Start transaction
    START TRANSACTION;
    
    -- Deactivate existing ranges for this session
    UPDATE custom_score_ranges 
    SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
    WHERE academic_session = session_name;
    
    -- Get the number of ranges in the JSON array
    SET range_count = JSON_LENGTH(ranges_json);
    
    -- Loop through each range in the JSON array
    WHILE range_index < range_count DO
        SET current_range = JSON_EXTRACT(ranges_json, CONCAT('$[', range_index, ']'));
        
        -- Insert new range
        INSERT INTO custom_score_ranges (
            academic_session,
            range_name,
            min_percentage,
            max_percentage,
            range_label,
            display_order,
            is_active,
            created_by
        ) VALUES (
            session_name,
            CONCAT('custom_', range_index + 1),
            JSON_UNQUOTE(JSON_EXTRACT(current_range, '$.min')),
            JSON_UNQUOTE(JSON_EXTRACT(current_range, '$.max')),
            JSON_UNQUOTE(JSON_EXTRACT(current_range, '$.label')),
            range_index + 1,
            TRUE,
            updated_by
        );
        
        SET range_index = range_index + 1;
    END WHILE;
    
    -- Commit transaction
    COMMIT;
END //
DELIMITER ;

-- Create function to check if custom ranges exist for a session
DELIMITER //
CREATE FUNCTION HasCustomRanges(session_name VARCHAR(20)) 
RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE range_count INT DEFAULT 0;
    
    SELECT COUNT(*) INTO range_count
    FROM custom_score_ranges
    WHERE academic_session = session_name 
    AND is_active = TRUE
    AND created_by != 'system';
    
    RETURN range_count > 0;
END //
DELIMITER ;

-- Create trigger to validate range overlaps
DELIMITER //
CREATE TRIGGER validate_range_overlap
BEFORE INSERT ON custom_score_ranges
FOR EACH ROW
BEGIN
    DECLARE overlap_count INT DEFAULT 0;
    
    -- Check for overlapping ranges in the same session
    SELECT COUNT(*) INTO overlap_count
    FROM custom_score_ranges
    WHERE academic_session = NEW.academic_session
    AND is_active = TRUE
    AND id != NEW.id
    AND (
        (NEW.min_percentage BETWEEN min_percentage AND max_percentage) OR
        (NEW.max_percentage BETWEEN min_percentage AND max_percentage) OR
        (min_percentage BETWEEN NEW.min_percentage AND NEW.max_percentage) OR
        (max_percentage BETWEEN NEW.min_percentage AND NEW.max_percentage)
    );
    
    IF overlap_count > 0 THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = 'Range overlaps with existing range in the same session';
    END IF;
END //
DELIMITER ;
