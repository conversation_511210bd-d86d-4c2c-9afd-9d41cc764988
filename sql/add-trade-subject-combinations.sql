-- =====================================================================================
-- ADD TRADE-SUBJECT COMBINATIONS AND TEST THE SYSTEM
-- =====================================================================================

-- Insert trade-subject combinations for 2023-2024 session

-- Medical Trade Combinations
INSERT IGNORE INTO trade_subject_combinations (trade_id, subject_id, class_level, subject_type, is_mandatory, academic_session) VALUES
-- Medical Class 11 & 12 - Compulsory Core
(1, 1, '11', 'compulsory_core', TRUE, '2023-2024'), -- Physics
(1, 1, '12', 'compulsory_core', TRUE, '2023-2024'), -- Physics
(1, 2, '11', 'compulsory_core', TRUE, '2023-2024'), -- Chemistry
(1, 2, '12', 'compulsory_core', TRUE, '2023-2024'), -- Chemistry
(1, 4, '11', 'compulsory_core', TRUE, '2023-2024'), -- Biology
(1, 4, '12', 'compulsory_core', TRUE, '2023-2024'), -- Biology

-- Medical - Selective Subjects (Class 11: Economics OR MOP, Class 12: Economics OR E-Business)
(1, 7, '11', 'selective_option', FALSE, '2023-2024'), -- Economics (Class 11)
(1, 8, '11', 'selective_option', FALSE, '2023-2024'), -- MOP (Class 11)
(1, 7, '12', 'selective_option', FALSE, '2023-2024'), -- Economics (Class 12)
(1, 9, '12', 'selective_option', FALSE, '2023-2024'), -- E-Business (Class 12)

-- Medical - Compulsory Language
(1, 10, '11', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(1, 10, '12', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(1, 11, '11', 'compulsory_language', TRUE, '2023-2024'), -- English
(1, 11, '12', 'compulsory_language', TRUE, '2023-2024'), -- English

-- Medical - Additional Compulsory
(1, 12, '11', 'additional_compulsory', TRUE, '2023-2024'), -- Computer Science
(1, 12, '12', 'additional_compulsory', TRUE, '2023-2024'), -- Computer Science
(1, 13, '11', 'additional_compulsory', TRUE, '2023-2024'), -- Environmental Science
(1, 13, '12', 'additional_compulsory', TRUE, '2023-2024'), -- Environmental Science

-- Medical - Optional
(1, 15, '11', 'optional', FALSE, '2023-2024'), -- Mathematics (Optional)
(1, 15, '12', 'optional', FALSE, '2023-2024'), -- Mathematics (Optional)

-- Non-Medical Trade Combinations
-- Non-Medical Class 11 & 12 - Compulsory Core
(2, 1, '11', 'compulsory_core', TRUE, '2023-2024'), -- Physics
(2, 1, '12', 'compulsory_core', TRUE, '2023-2024'), -- Physics
(2, 2, '11', 'compulsory_core', TRUE, '2023-2024'), -- Chemistry
(2, 2, '12', 'compulsory_core', TRUE, '2023-2024'), -- Chemistry
(2, 3, '11', 'compulsory_core', TRUE, '2023-2024'), -- Mathematics
(2, 3, '12', 'compulsory_core', TRUE, '2023-2024'), -- Mathematics

-- Non-Medical - Selective Subjects
(2, 7, '11', 'selective_option', FALSE, '2023-2024'), -- Economics (Class 11)
(2, 8, '11', 'selective_option', FALSE, '2023-2024'), -- MOP (Class 11)
(2, 7, '12', 'selective_option', FALSE, '2023-2024'), -- Economics (Class 12)
(2, 9, '12', 'selective_option', FALSE, '2023-2024'), -- E-Business (Class 12)

-- Non-Medical - Compulsory Language
(2, 10, '11', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(2, 10, '12', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(2, 11, '11', 'compulsory_language', TRUE, '2023-2024'), -- English
(2, 11, '12', 'compulsory_language', TRUE, '2023-2024'), -- English

-- Non-Medical - Additional Compulsory
(2, 12, '11', 'additional_compulsory', TRUE, '2023-2024'), -- Computer Science
(2, 12, '12', 'additional_compulsory', TRUE, '2023-2024'), -- Computer Science
(2, 13, '11', 'additional_compulsory', TRUE, '2023-2024'), -- Environmental Science
(2, 13, '12', 'additional_compulsory', TRUE, '2023-2024'), -- Environmental Science

-- Non-Medical - Optional
(2, 14, '11', 'optional', FALSE, '2023-2024'), -- Biology (Optional)
(2, 14, '12', 'optional', FALSE, '2023-2024'), -- Biology (Optional)

-- Commerce Trade Combinations
-- Commerce Class 11 & 12 - Compulsory Core
(3, 5, '11', 'compulsory_core', TRUE, '2023-2024'), -- Accountancy
(3, 5, '12', 'compulsory_core', TRUE, '2023-2024'), -- Accountancy
(3, 6, '11', 'compulsory_core', TRUE, '2023-2024'), -- Business Studies
(3, 6, '12', 'compulsory_core', TRUE, '2023-2024'), -- Business Studies

-- Commerce - Selective Subjects
(3, 7, '11', 'selective_option', FALSE, '2023-2024'), -- Economics (Class 11)
(3, 8, '11', 'selective_option', FALSE, '2023-2024'), -- MOP (Class 11)
(3, 7, '12', 'selective_option', FALSE, '2023-2024'), -- Economics (Class 12)
(3, 9, '12', 'selective_option', FALSE, '2023-2024'), -- E-Business (Class 12)

-- Commerce - Compulsory Language
(3, 10, '11', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(3, 10, '12', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(3, 11, '11', 'compulsory_language', TRUE, '2023-2024'), -- English
(3, 11, '12', 'compulsory_language', TRUE, '2023-2024'), -- English

-- Commerce - Additional Compulsory
(3, 12, '11', 'additional_compulsory', TRUE, '2023-2024'), -- Computer Science
(3, 12, '12', 'additional_compulsory', TRUE, '2023-2024'), -- Computer Science
(3, 13, '11', 'additional_compulsory', TRUE, '2023-2024'), -- Environmental Science
(3, 13, '12', 'additional_compulsory', TRUE, '2023-2024'), -- Environmental Science

-- Commerce - Optional
(3, 15, '11', 'optional', FALSE, '2023-2024'), -- Mathematics (Optional)
(3, 15, '12', 'optional', FALSE, '2023-2024'); -- Mathematics (Optional)

-- Test the trade curriculum view
SELECT 'MEDICAL TRADE CURRICULUM:' AS info;
SELECT trade_code, trade_name, class_level, subject_code, subject_name, subject_type_description, include_in_grand_total
FROM v_trade_curriculum 
WHERE trade_code = 'MED' AND academic_session = '2023-2024'
ORDER BY class_level, subject_code;

SELECT 'NON-MEDICAL TRADE CURRICULUM:' AS info;
SELECT trade_code, trade_name, class_level, subject_code, subject_name, subject_type_description, include_in_grand_total
FROM v_trade_curriculum
WHERE trade_code = 'NON_MED' AND academic_session = '2023-2024'
ORDER BY class_level, subject_code;

SELECT 'COMMERCE TRADE CURRICULUM:' AS info;
SELECT trade_code, trade_name, class_level, subject_code, subject_name, subject_type_description, include_in_grand_total
FROM v_trade_curriculum
WHERE trade_code = 'COMM' AND academic_session = '2023-2024'
ORDER BY class_level, subject_code;

-- Show subjects that count in grand total vs additional subjects
SELECT 'SUBJECTS THAT COUNT IN GRAND TOTAL:' AS info;
SELECT subject_code, subject_name, subject_category, include_in_grand_total
FROM academic_subjects 
WHERE include_in_grand_total = TRUE
ORDER BY subject_order;

SELECT 'ADDITIONAL SUBJECTS (NOT IN GRAND TOTAL):' AS info;
SELECT subject_code, subject_name, subject_category, include_in_grand_total
FROM academic_subjects 
WHERE include_in_grand_total = FALSE
ORDER BY subject_order;
