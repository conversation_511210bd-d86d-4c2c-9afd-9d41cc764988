-- =====================================================================================
-- CLEANUP ACADEMIC TRADES TABLE - CORRECTED VERSION
-- =====================================================================================
-- Remove redundant columns and create dynamic views using existing table structure
-- =====================================================================================

-- The redundant columns have already been removed successfully
-- Now let's create dynamic views using the existing table structure

-- =====================================================================================
-- SECTION 1: CREATE DYNAMIC VIEWS FOR CLASS/SECTION INFORMATION
-- =====================================================================================

-- Create a view that shows trade information with dynamic class/section counts
CREATE OR REPLACE VIEW v_trade_class_summary AS
SELECT 
    t.id,
    t.trade_code,
    t.trade_name,
    t.description,
    t.is_active,
    
    -- Dynamic class information from class_trade_sections table
    GROUP_CONCAT(DISTINCT cts.class_level ORDER BY cts.class_level) AS available_classes,
    COUNT(DISTINCT cts.class_level) AS total_classes,
    COUNT(DISTINCT cts.section_id) AS total_sections,
    COUNT(cts.id) AS total_class_section_combinations,
    
    -- Section breakdown by class
    GROUP_CONCAT(
        DISTINCT CONCAT(cts.class_level, '-', sec.section_code) 
        ORDER BY cts.class_level, sec.section_code
    ) AS class_section_combinations,
    
    -- Student statistics
    SUM(cts.total_students) AS total_enrolled_students,
    ROUND(AVG(cts.total_students), 1) AS avg_students_per_section,
    
    -- Academic session
    cts.academic_session,
    
    t.created_at,
    t.updated_at
    
FROM academic_trades t
LEFT JOIN class_trade_sections cts ON t.id = cts.trade_id AND cts.is_active = TRUE
LEFT JOIN academic_sections sec ON cts.section_id = sec.id
WHERE t.is_active = TRUE
GROUP BY t.id, t.trade_code, t.trade_name, t.description, cts.academic_session, t.is_active, t.created_at, t.updated_at
ORDER BY t.trade_code, cts.academic_session;

-- =====================================================================================
-- SECTION 2: CREATE TRADE STATISTICS VIEW
-- =====================================================================================

-- Create a comprehensive trade statistics view
CREATE OR REPLACE VIEW v_trade_statistics AS
SELECT 
    t.trade_code,
    t.trade_name,
    t.description,
    
    -- Class and section statistics
    COUNT(DISTINCT cts.class_level) AS classes_offered,
    COUNT(DISTINCT cts.section_id) AS sections_offered,
    COUNT(cts.id) AS total_combinations,
    
    -- Student statistics
    SUM(cts.total_students) AS total_students,
    ROUND(AVG(cts.total_students), 1) AS avg_students_per_section,
    MAX(cts.total_students) AS max_section_size,
    MIN(cts.total_students) AS min_section_size,
    
    -- Academic session
    cts.academic_session,
    
    -- Detailed breakdown
    GROUP_CONCAT(
        DISTINCT CONCAT('Class ', cts.class_level, ': ', 
        (SELECT COUNT(*) FROM class_trade_sections cts2 
         WHERE cts2.trade_id = t.id AND cts2.class_level = cts.class_level 
         AND cts2.academic_session = cts.academic_session), ' sections')
        ORDER BY cts.class_level
    ) AS class_breakdown
    
FROM academic_trades t
LEFT JOIN class_trade_sections cts ON t.id = cts.trade_id AND cts.is_active = TRUE
WHERE t.is_active = TRUE
GROUP BY t.id, t.trade_code, t.trade_name, t.description, cts.academic_session
ORDER BY t.trade_code, cts.academic_session;

-- =====================================================================================
-- SECTION 3: VERIFICATION AND TESTING
-- =====================================================================================

-- Test the new dynamic views
SELECT 'TRADE CLASS SUMMARY (DYNAMIC):' AS info;
SELECT 
    trade_code,
    trade_name,
    available_classes,
    total_classes,
    total_sections,
    total_class_section_combinations,
    total_enrolled_students,
    academic_session
FROM v_trade_class_summary
WHERE academic_session = '2023-2024'
ORDER BY trade_code;

SELECT 'TRADE STATISTICS (COMPREHENSIVE):' AS info;
SELECT 
    trade_code,
    trade_name,
    classes_offered,
    sections_offered,
    total_students,
    avg_students_per_section,
    class_breakdown,
    academic_session
FROM v_trade_statistics
WHERE academic_session = '2023-2024'
ORDER BY trade_code;

-- =====================================================================================
-- SECTION 4: SHOW BENEFITS OF THE CLEANUP
-- =====================================================================================

SELECT 'BENEFITS OF REMOVING REDUNDANT COLUMNS:' AS info;

SELECT 
    'Before: Static columns in academic_trades table' AS before_cleanup,
    'available_classes: 11,12 (hardcoded)' AS old_classes,
    'total_sections: 2 or 6 (hardcoded)' AS old_sections;

SELECT 
    'After: Dynamic data from linked tables' AS after_cleanup,
    'available_classes: Calculated from class_trade_sections' AS new_classes,
    'total_sections: Counted from actual section assignments' AS new_sections;

-- Show the actual dynamic data
SELECT 'DYNAMIC CLASS/SECTION DATA (2023-2024):' AS info;
SELECT 
    t.trade_code,
    t.trade_name,
    GROUP_CONCAT(DISTINCT cts.class_level ORDER BY cts.class_level) AS actual_classes,
    COUNT(DISTINCT cts.section_id) AS actual_sections,
    COUNT(cts.id) AS actual_combinations
FROM academic_trades t
LEFT JOIN class_trade_sections cts ON t.id = cts.trade_id 
WHERE cts.academic_session = '2023-2024' AND cts.is_active = TRUE
GROUP BY t.trade_code, t.trade_name
ORDER BY t.trade_code;

-- =====================================================================================
-- SECTION 5: FINAL VERIFICATION
-- =====================================================================================

SELECT 'FINAL ACADEMIC_TRADES TABLE STRUCTURE:' AS info;
DESCRIBE academic_trades;

SELECT 'CLEANED ACADEMIC_TRADES DATA:' AS info;
SELECT * FROM academic_trades ORDER BY trade_code;

SELECT 'SUMMARY OF CHANGES:' AS info;
SELECT 
    'Removed: available_classes column (redundant)' AS change1,
    'Removed: total_sections column (redundant)' AS change2,
    'Added: v_trade_class_summary view (dynamic)' AS enhancement1,
    'Added: v_trade_statistics view (comprehensive)' AS enhancement2,
    'Result: Cleaner table structure with dynamic data' AS result;
