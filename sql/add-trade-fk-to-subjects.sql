-- =====================================================================================
-- ADD TRADE FOREIGN KEY TO SUBJECTS TABLE
-- =====================================================================================
-- Add trade_id column to subjects table and establish proper foreign key relationship
-- This creates a normalized structure linking subjects to trades
-- =====================================================================================

-- Disable foreign key checks temporarily for safe modifications
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================================================
-- SECTION 1: ADD TRADE COLUMN TO SUBJECTS TABLE
-- =====================================================================================

-- Add trade_id column to subjects table
ALTER TABLE subjects 
ADD COLUMN trade_id INT NULL AFTER subject_category_new,
ADD INDEX idx_subject_trade (trade_id);

-- =====================================================================================
-- SECTION 2: MAP SUBJECTS TO TRADES BASED ON STREAM/GROUP
-- =====================================================================================

-- Update subjects with appropriate trade_id based on their stream_group
-- This mapping follows the Punjab Board curriculum structure

-- Medical Trade (MED) - Biology-focused subjects
UPDATE subjects SET trade_id = (SELECT id FROM academic_trades WHERE trade_code = 'MED') 
WHERE code IN ('54', '53', '52') -- Biology, Chemistry, Physics (Medical core)
   OR (subject_category_new = 'science' AND code = '54') -- Biology specifically
   OR stream_group LIKE '%Science%' AND name LIKE '%Biology%';

-- Non-Medical Trade (NON_MED) - Mathematics-focused subjects  
UPDATE subjects SET trade_id = (SELECT id FROM academic_trades WHERE trade_code = 'NON_MED')
WHERE code IN ('28', '52', '53', '146') -- Mathematics, Physics, Chemistry, Computer Science (Non-Medical core)
   OR (subject_category_new = 'science' AND code IN ('28', '146')) -- Mathematics, Computer Science
   OR stream_group LIKE '%Engineering%' -- Engineering & Technology vocational subjects
   OR code BETWEEN '107' AND '127'; -- Engineering vocational codes

-- Commerce Trade (COMM) - Business-focused subjects
UPDATE subjects SET trade_id = (SELECT id FROM academic_trades WHERE trade_code = 'COMM')
WHERE code IN ('141', '142', '144', '26') -- Business Studies, Accountancy, E-Business, Economics
   OR subject_category_new = 'commerce'
   OR stream_group LIKE '%Business & Commerce%' -- Business & Commerce vocational subjects
   OR code BETWEEN '169' AND '217'; -- Business vocational codes

-- Subjects available to ALL trades (compulsory for all streams)
-- These will have NULL trade_id to indicate they're available to all trades
UPDATE subjects SET trade_id = NULL 
WHERE subject_category_new = 'compulsory_all' -- General English, General Punjabi
   OR subject_category_new = 'elective' -- Physical Education, Computer Application, NCC
   OR stream_group LIKE '%All Streams%'
   OR code IN ('1', '2', '49', '72', '209'); -- Compulsory and multi-stream electives

-- Humanities subjects - can be taken by any trade as electives
UPDATE subjects SET trade_id = NULL
WHERE subject_category_new = 'humanities'
   OR stream_group LIKE '%Humanities%'
   OR code IN ('3', '4', '5', '6', '7', '19', '23', '24', '25', '31', '32', '33', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '150');

-- NSQF subjects - available as vocational options for any trade
UPDATE subjects SET trade_id = NULL
WHERE subject_category_new = 'nsqf'
   OR code BETWEEN '196' AND '222';

-- Vocational Agriculture and Home Science - can be offered as specialized tracks
UPDATE subjects SET trade_id = NULL
WHERE stream_group LIKE '%Agriculture%'
   OR stream_group LIKE '%Home Science%'
   OR code BETWEEN '73' AND '106'
   OR code = '65' -- Agriculture
   OR code = '138'; -- General Foundation Course

-- =====================================================================================
-- SECTION 3: ADD FOREIGN KEY CONSTRAINT
-- =====================================================================================

-- Add foreign key constraint to link subjects to trades
ALTER TABLE subjects 
ADD CONSTRAINT fk_subject_trade 
FOREIGN KEY (trade_id) REFERENCES academic_trades(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- =====================================================================================
-- SECTION 4: CREATE ENHANCED VIEWS WITH TRADE RELATIONSHIPS
-- =====================================================================================

-- Create view showing subjects with their trade relationships
CREATE OR REPLACE VIEW v_subjects_with_trades AS
SELECT 
    s.id,
    s.code,
    s.name AS subject_name,
    s.stream_group,
    s.subject_category_new,
    s.is_compulsory,
    s.is_elective,
    s.max_theory_lectures,
    s.max_practical_lectures,
    s.total_lectures_per_week,
    
    -- Trade information
    t.id AS trade_id,
    t.trade_code,
    t.trade_name,
    
    -- Subject availability
    CASE 
        WHEN s.trade_id IS NULL THEN 'Available to All Trades'
        ELSE CONCAT('Specific to ', t.trade_name, ' Trade')
    END AS availability,
    
    -- Subject type for curriculum planning
    CASE 
        WHEN s.subject_category_new = 'compulsory_all' THEN 'Compulsory for All'
        WHEN s.subject_category_new IN ('science', 'commerce') AND s.trade_id IS NOT NULL THEN 'Trade Core Subject'
        WHEN s.subject_category_new = 'humanities' THEN 'Humanities Elective'
        WHEN s.subject_category_new = 'vocational' THEN 'Vocational Specialization'
        WHEN s.subject_category_new = 'nsqf' THEN 'Skill Development'
        WHEN s.subject_category_new = 'elective' THEN 'General Elective'
        ELSE 'Other'
    END AS curriculum_type
    
FROM subjects s
LEFT JOIN academic_trades t ON s.trade_id = t.id
ORDER BY 
    CASE WHEN s.trade_id IS NULL THEN 0 ELSE 1 END, -- All-trade subjects first
    t.trade_code,
    s.subject_category_new,
    CAST(s.code AS UNSIGNED);

-- Create view showing trade-specific curriculum
CREATE OR REPLACE VIEW v_trade_specific_curriculum AS
SELECT 
    t.trade_code,
    t.trade_name,
    
    -- Core subjects for this trade
    GROUP_CONCAT(
        CASE WHEN s.subject_category_new IN ('science', 'commerce') AND s.trade_id = t.id 
        THEN CONCAT(s.code, '-', s.name) END 
        ORDER BY CAST(s.code AS UNSIGNED) SEPARATOR ', '
    ) AS core_subjects,
    
    -- Vocational subjects for this trade
    GROUP_CONCAT(
        CASE WHEN s.subject_category_new = 'vocational' AND s.trade_id = t.id 
        THEN CONCAT(s.code, '-', s.name) END 
        ORDER BY CAST(s.code AS UNSIGNED) SEPARATOR ', '
    ) AS vocational_subjects,
    
    -- Count of subjects
    COUNT(CASE WHEN s.trade_id = t.id THEN 1 END) AS trade_specific_subjects,
    
    -- Available electives (subjects with trade_id = NULL)
    (SELECT COUNT(*) FROM subjects WHERE trade_id IS NULL) AS available_electives
    
FROM academic_trades t
LEFT JOIN subjects s ON t.id = s.trade_id
GROUP BY t.id, t.trade_code, t.trade_name
ORDER BY t.trade_code;

-- =====================================================================================
-- SECTION 5: VERIFICATION AND TESTING
-- =====================================================================================

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Test the trade-subject relationships
SELECT 'SUBJECTS BY TRADE ASSIGNMENT:' AS info;

SELECT 
    CASE 
        WHEN trade_id IS NULL THEN 'Available to All Trades'
        ELSE (SELECT CONCAT(trade_code, ' - ', trade_name) FROM academic_trades WHERE id = trade_id)
    END AS trade_assignment,
    COUNT(*) AS subject_count,
    GROUP_CONCAT(CONCAT(code, '-', name) ORDER BY CAST(code AS UNSIGNED) LIMIT 5) AS sample_subjects
FROM subjects
GROUP BY trade_id
ORDER BY trade_id;

-- Show trade-specific core subjects
SELECT 'TRADE-SPECIFIC CORE SUBJECTS:' AS info;
SELECT 
    t.trade_code,
    t.trade_name,
    COUNT(s.id) AS core_subjects_count,
    GROUP_CONCAT(CONCAT(s.code, '-', s.name) ORDER BY CAST(s.code AS UNSIGNED)) AS core_subjects
FROM academic_trades t
LEFT JOIN subjects s ON t.id = s.trade_id AND s.subject_category_new IN ('science', 'commerce')
GROUP BY t.id, t.trade_code, t.trade_name
ORDER BY t.trade_code;

-- Show subjects available to all trades
SELECT 'SUBJECTS AVAILABLE TO ALL TRADES:' AS info;
SELECT 
    subject_category_new,
    COUNT(*) AS subject_count,
    GROUP_CONCAT(CONCAT(code, '-', name) ORDER BY CAST(code AS UNSIGNED)) AS subjects
FROM subjects
WHERE trade_id IS NULL
GROUP BY subject_category_new
ORDER BY 
    CASE subject_category_new
        WHEN 'compulsory_all' THEN 1
        WHEN 'elective' THEN 2
        WHEN 'humanities' THEN 3
        WHEN 'vocational' THEN 4
        WHEN 'nsqf' THEN 5
        ELSE 6
    END;

-- Verify foreign key constraint
SELECT 'FOREIGN KEY CONSTRAINT VERIFICATION:' AS info;
SELECT 
    'subjects.trade_id -> academic_trades.id' AS relationship,
    COUNT(DISTINCT s.trade_id) AS linked_trades,
    COUNT(s.id) AS total_subjects,
    COUNT(CASE WHEN s.trade_id IS NOT NULL THEN 1 END) AS trade_specific_subjects,
    COUNT(CASE WHEN s.trade_id IS NULL THEN 1 END) AS all_trade_subjects
FROM subjects s;

-- Show the enhanced table structure
SELECT 'UPDATED SUBJECTS TABLE STRUCTURE:' AS info;
DESCRIBE subjects;
