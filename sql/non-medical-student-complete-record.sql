-- =====================================================================================
-- NON-MEDICAL STUDENT COMPLETE RECORD WITH PROPER SUBJECT CATEGORIZATION
-- =====================================================================================
-- Create comprehensive student record for Non-Medical trade with proper grand total calculation
-- Excludes additional subjects from grand total as per academic requirements
-- =====================================================================================

-- =====================================================================================
-- SECTION 1: CREATE SAMPLE NON-MEDICAL STUDENT (ANUJ)
-- =====================================================================================

-- Insert sample Non-Medical student "Anuj"
INSERT IGNORE INTO students (
    sno, student_id, udise_code, name, father_name, mother_name, gender, 
    class, section, stream, trade, session, roll_no, contact_no, 
    admission_no, admission_date, state_name, district_name, 
    cur_address, village_ward, pin_code, room_number, is_active, academic_session
) VALUES (
    6, 'STU006', 'UD006', 'Anuj Kumar', 'Rajesh Kumar', 'Sunita Kumar', 'Male',
    '11', 'A', 'Science', 'Non-Medical', '2023-24', '006', '**********',
    'ADM006', '2023-04-01', 'Punjab', 'Ludhiana',
    '456 Model Town, Ludhiana', 'Model Town', '141001', 5, 1, '2023-2024'
);

-- =====================================================================================
-- SECTION 2: CREATE SAMPLE MARKS FOR NON-MEDICAL SUBJECTS
-- =====================================================================================

-- Insert sample marks for Anuj in Non-Medical subjects
-- Core Compulsory Subjects: English, Punjabi, Physics, Chemistry, Mathematics
-- Additional Optional: Biology
-- Additional Compulsory: Computer Science, Environment Science

INSERT IGNORE INTO student_subject_marks (student_id, exam_id, subject_id, theory_marks, practical_marks, internal_marks, total_marks, max_marks, academic_session) VALUES
-- Core Compulsory Subjects (included in grand total)
(6, 83, 1, 75.00, 0.00, 15.00, 90.00, 100.00, '2023-2024'),   -- English
(6, 83, 2, 70.00, 0.00, 18.00, 88.00, 100.00, '2023-2024'),   -- Punjabi  
(6, 83, 52, 65.00, 18.00, 12.00, 95.00, 100.00, '2023-2024'), -- Physics
(6, 83, 53, 68.00, 17.00, 13.00, 98.00, 100.00, '2023-2024'), -- Chemistry
(6, 83, 28, 80.00, 0.00, 15.00, 95.00, 100.00, '2023-2024'),  -- Mathematics

-- Additional Optional Subject (NOT included in grand total)
(6, 83, 54, 72.00, 16.00, 12.00, 100.00, 100.00, '2023-2024'), -- Biology (Additional Optional)

-- Additional Compulsory Subjects (NOT included in grand total)
(6, 83, 146, 78.00, 15.00, 12.00, 105.00, 100.00, '2023-2024'), -- Computer Science (Additional Compulsory)
(6, 83, 49, 85.00, 10.00, 5.00, 100.00, 100.00, '2023-2024');   -- Physical Education/Environment Science (Additional Compulsory)

-- =====================================================================================
-- SECTION 3: COMPREHENSIVE NON-MEDICAL STUDENT RECORD VIEW
-- =====================================================================================

-- Create comprehensive view for Non-Medical student records
CREATE OR REPLACE VIEW v_non_medical_student_complete AS
SELECT
    -- Student Information
    s.id AS student_id,
    s.student_id AS roll_number,
    s.name AS student_name,
    s.father_name,
    s.mother_name,
    s.class AS class_level,
    s.section,
    s.trade AS student_trade,
    COALESCE(tm.correct_trade_name, s.trade) AS trade_full_name,
    tm.correct_trade_code AS trade_code,
    s.academic_session,
    
    -- Subject Information with Categorization
    sub.code AS subject_code,
    sub.name AS subject_name,
    sub.subject_category_new AS subject_category,
    sub.stream_group,
    
    -- Subject Classification for Non-Medical Trade
    CASE 
        WHEN sub.code IN ('1', '2') THEN 'Compulsory Language'
        WHEN sub.code IN ('28', '52', '53') AND sub.trade_id = 2 THEN 'Core Compulsory'
        WHEN sub.code = '54' THEN 'Additional Optional'
        WHEN sub.code IN ('146', '49') THEN 'Additional Compulsory'
        WHEN sub.trade_id IS NULL THEN 'General Elective'
        ELSE 'Other'
    END AS subject_classification,
    
    -- Include in Grand Total Flag
    CASE 
        WHEN sub.code IN ('1', '2', '28', '52', '53') THEN TRUE  -- Core subjects only
        ELSE FALSE  -- Additional subjects excluded from grand total
    END AS include_in_grand_total,
    
    -- Exam Information
    e.exam_id,
    e.exam_name,
    e.status AS exam_status,
    e.created_at AS exam_date,
    
    -- Marks Information
    ssm.theory_marks,
    ssm.practical_marks,
    ssm.internal_marks,
    ssm.total_marks,
    ssm.max_marks,
    ROUND((ssm.total_marks / ssm.max_marks) * 100, 2) AS percentage,
    
    -- Grade Calculation
    CASE 
        WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 90 THEN 'A+'
        WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 80 THEN 'A'
        WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 70 THEN 'B+'
        WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 60 THEN 'B'
        WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 50 THEN 'C+'
        WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 40 THEN 'C'
        WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 33 THEN 'D'
        ELSE 'F'
    END AS grade,
    
    -- Result Status
    CASE 
        WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 33 THEN 'PASS' 
        ELSE 'FAIL' 
    END AS result_status,
    
    -- Additional Information
    ssm.remarks AS exam_remarks,
    ssm.created_at AS marks_entry_date
    
FROM students s
JOIN student_subject_marks ssm ON s.id = ssm.student_id
JOIN subjects sub ON ssm.subject_id = sub.id
JOIN exams e ON ssm.exam_id = e.exam_id
LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
WHERE s.academic_session = ssm.academic_session
ORDER BY s.student_id, 
         CASE 
             WHEN sub.code IN ('1', '2', '28', '52', '53') THEN 1  -- Core subjects first
             WHEN sub.code IN ('146', '49') THEN 2                -- Additional compulsory second
             WHEN sub.code = '54' THEN 3                          -- Additional optional last
             ELSE 4
         END,
         CAST(sub.code AS UNSIGNED);

-- =====================================================================================
-- SECTION 4: NON-MEDICAL STUDENT GRAND TOTAL CALCULATION
-- =====================================================================================

-- Create view for Non-Medical student grand totals (excluding additional subjects)
CREATE OR REPLACE VIEW v_non_medical_grand_totals AS
SELECT
    s.id AS student_id,
    s.student_id AS roll_number,
    s.name AS student_name,
    s.class AS class_level,
    s.section,
    s.trade,
    COALESCE(tm.correct_trade_name, s.trade) AS trade_full_name,
    s.academic_session,
    e.exam_name,
    
    -- Core Subjects Summary (included in grand total)
    COUNT(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN 1 END) AS core_subjects_count,
    SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.total_marks ELSE 0 END) AS core_total_marks,
    SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.max_marks ELSE 0 END) AS core_max_marks,
    ROUND((SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.total_marks ELSE 0 END) / 
           SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.max_marks ELSE 0 END)) * 100, 2) AS core_percentage,
    
    -- Additional Subjects Summary (NOT included in grand total)
    COUNT(CASE WHEN sub.code NOT IN ('1', '2', '28', '52', '53') THEN 1 END) AS additional_subjects_count,
    SUM(CASE WHEN sub.code NOT IN ('1', '2', '28', '52', '53') THEN ssm.total_marks ELSE 0 END) AS additional_total_marks,
    SUM(CASE WHEN sub.code NOT IN ('1', '2', '28', '52', '53') THEN ssm.max_marks ELSE 0 END) AS additional_max_marks,
    ROUND((SUM(CASE WHEN sub.code NOT IN ('1', '2', '28', '52', '53') THEN ssm.total_marks ELSE 0 END) / 
           NULLIF(SUM(CASE WHEN sub.code NOT IN ('1', '2', '28', '52', '53') THEN ssm.max_marks ELSE 0 END), 0)) * 100, 2) AS additional_percentage,
    
    -- Grand Total (Core subjects only)
    SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.total_marks ELSE 0 END) AS grand_total_marks,
    SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.max_marks ELSE 0 END) AS grand_total_max_marks,
    ROUND((SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.total_marks ELSE 0 END) / 
           SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.max_marks ELSE 0 END)) * 100, 2) AS grand_total_percentage,
    
    -- Overall Grade (based on core subjects only)
    CASE 
        WHEN (SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.total_marks ELSE 0 END) / 
              SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.max_marks ELSE 0 END)) * 100 >= 90 THEN 'A+'
        WHEN (SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.total_marks ELSE 0 END) / 
              SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.max_marks ELSE 0 END)) * 100 >= 80 THEN 'A'
        WHEN (SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.total_marks ELSE 0 END) / 
              SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.max_marks ELSE 0 END)) * 100 >= 70 THEN 'B+'
        WHEN (SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.total_marks ELSE 0 END) / 
              SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.max_marks ELSE 0 END)) * 100 >= 60 THEN 'B'
        WHEN (SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.total_marks ELSE 0 END) / 
              SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.max_marks ELSE 0 END)) * 100 >= 50 THEN 'C+'
        WHEN (SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.total_marks ELSE 0 END) / 
              SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.max_marks ELSE 0 END)) * 100 >= 40 THEN 'C'
        WHEN (SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.total_marks ELSE 0 END) / 
              SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.max_marks ELSE 0 END)) * 100 >= 33 THEN 'D'
        ELSE 'F'
    END AS overall_grade,
    
    -- Overall Result
    CASE 
        WHEN (SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.total_marks ELSE 0 END) / 
              SUM(CASE WHEN sub.code IN ('1', '2', '28', '52', '53') THEN ssm.max_marks ELSE 0 END)) * 100 >= 33 
        THEN 'PROMOTED' 
        ELSE 'DETAINED' 
    END AS overall_result
    
FROM students s
JOIN student_subject_marks ssm ON s.id = ssm.student_id
JOIN subjects sub ON ssm.subject_id = sub.id
JOIN exams e ON ssm.exam_id = e.exam_id
LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
WHERE s.academic_session = ssm.academic_session
GROUP BY s.id, e.exam_id, s.academic_session
ORDER BY s.student_id, e.created_at;

-- =====================================================================================
-- SECTION 5: VERIFICATION AND DEMONSTRATION QUERIES
-- =====================================================================================

-- Test the Non-Medical student record system
SELECT 'NON-MEDICAL STUDENT COMPLETE RECORD - VERIFICATION:' AS info;

-- Query 1: Complete record for Anuj (Non-Medical student)
SELECT 'COMPLETE RECORD FOR ANUJ (NON-MEDICAL STUDENT):' AS info;
SELECT
    student_id,
    roll_number,
    student_name,
    class_level,
    trade_full_name,
    subject_name,
    subject_classification,
    include_in_grand_total,
    theory_marks,
    practical_marks,
    internal_marks,
    total_marks,
    max_marks,
    percentage,
    grade,
    result_status
FROM v_non_medical_student_complete
WHERE roll_number = 'STU006' AND academic_session = '2023-2024'
ORDER BY
    CASE
        WHEN subject_classification = 'Compulsory Language' THEN 1
        WHEN subject_classification = 'Core Compulsory' THEN 2
        WHEN subject_classification = 'Additional Compulsory' THEN 3
        WHEN subject_classification = 'Additional Optional' THEN 4
        ELSE 5
    END,
    subject_code;

-- Query 2: Grand Total calculation for Anuj (excluding additional subjects)
SELECT 'GRAND TOTAL CALCULATION FOR ANUJ (CORE SUBJECTS ONLY):' AS info;
SELECT
    student_id,
    roll_number,
    student_name,
    trade_full_name,
    exam_name,
    core_subjects_count,
    core_total_marks,
    core_max_marks,
    core_percentage,
    additional_subjects_count,
    additional_total_marks,
    additional_max_marks,
    additional_percentage,
    grand_total_marks,
    grand_total_max_marks,
    grand_total_percentage,
    overall_grade,
    overall_result
FROM v_non_medical_grand_totals
WHERE roll_number = 'STU006' AND academic_session = '2023-2024';

-- Query 3: Subject-wise breakdown for Non-Medical trade
SELECT 'NON-MEDICAL SUBJECT CATEGORIZATION:' AS info;
SELECT
    subject_code,
    subject_name,
    subject_classification,
    include_in_grand_total,
    CASE
        WHEN include_in_grand_total = TRUE THEN 'Included in Grand Total'
        ELSE 'Additional Subject (Not in Grand Total)'
    END AS grand_total_status
FROM v_non_medical_student_complete
WHERE roll_number = 'STU006' AND academic_session = '2023-2024'
GROUP BY subject_code, subject_name, subject_classification, include_in_grand_total
ORDER BY
    CASE
        WHEN subject_classification = 'Compulsory Language' THEN 1
        WHEN subject_classification = 'Core Compulsory' THEN 2
        WHEN subject_classification = 'Additional Compulsory' THEN 3
        WHEN subject_classification = 'Additional Optional' THEN 4
        ELSE 5
    END,
    subject_code;

-- Query 4: Comparison with other students
SELECT 'COMPARISON WITH OTHER STUDENTS:' AS info;
SELECT
    roll_number,
    student_name,
    trade_full_name,
    COUNT(DISTINCT subject_code) AS total_subjects,
    ROUND(AVG(percentage), 2) AS average_percentage
FROM v_non_medical_student_complete
WHERE academic_session = '2023-2024'
GROUP BY roll_number, student_name, trade_full_name
ORDER BY average_percentage DESC;

-- Verification of data insertion
SELECT 'DATA INSERTION VERIFICATION:' AS info;
SELECT
    'Students' AS table_name,
    COUNT(*) AS record_count
FROM students WHERE name = 'Anuj Kumar'
UNION ALL
SELECT
    'Student Marks' AS table_name,
    COUNT(*) AS record_count
FROM student_subject_marks ssm
JOIN students s ON ssm.student_id = s.id
WHERE s.name = 'Anuj Kumar';

-- Show available views
SELECT 'AVAILABLE VIEWS FOR NON-MEDICAL STUDENTS:' AS info;
SELECT
    'v_non_medical_student_complete' AS view_name,
    'Complete student record with subject categorization' AS description
UNION ALL
SELECT
    'v_non_medical_grand_totals' AS view_name,
    'Grand total calculation excluding additional subjects' AS description;
