-- =====================================================================================
-- UPDATE SUBJECTS TABLE WITH PUNJAB BOARD DATA
-- =====================================================================================
-- Replace existing subjects with comprehensive Punjab Board subject structure
-- Includes proper categorization and stream assignments
-- =====================================================================================

-- Disable foreign key checks temporarily for safe data replacement
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================================================
-- SECTION 1: BACKUP AND CLEAR EXISTING DATA
-- =====================================================================================

-- Create backup of existing subjects (optional)
CREATE TABLE IF NOT EXISTS subjects_backup_old AS SELECT * FROM subjects;

-- Clear existing subject data
DELETE FROM subjects;

-- Reset auto-increment
ALTER TABLE subjects AUTO_INCREMENT = 1;

-- =====================================================================================
-- SECTION 2: UPDATE TABLE STRUCTURE FOR PUNJAB BOARD
-- =====================================================================================

-- Modify subjects table to accommodate Punjab Board structure
ALTER TABLE subjects 
MODIFY COLUMN code VARCHAR(10) NOT NULL,
ADD COLUMN IF NOT EXISTS stream_group VARCHAR(100) AFTER name,
ADD COLUMN IF NOT EXISTS subject_category_new ENUM('compulsory_all', 'compulsory_language', 'humanities', 'science', 'commerce', 'vocational', 'nsqf', 'elective') AFTER stream_group,
ADD COLUMN IF NOT EXISTS is_compulsory BOOLEAN DEFAULT FALSE AFTER subject_category_new,
ADD COLUMN IF NOT EXISTS is_elective BOOLEAN DEFAULT FALSE AFTER is_compulsory;

-- =====================================================================================
-- SECTION 3: INSERT PUNJAB BOARD SUBJECTS DATA
-- =====================================================================================

-- Insert all Punjab Board subjects with proper categorization
INSERT INTO subjects (code, name, stream_group, subject_category_new, is_compulsory, is_elective, max_theory_lectures, max_practical_lectures, total_lectures_per_week) VALUES

-- Compulsory for All Streams
('1', 'General English', 'All Streams (Compulsory)', 'compulsory_all', TRUE, FALSE, 4, 0, 4),
('2', 'General Punjabi', 'All Streams (Compulsory)', 'compulsory_all', TRUE, FALSE, 4, 0, 4),

-- Humanities Subjects
('3', 'Punjab History & Culture', 'Humanities (or elective)', 'humanities', FALSE, TRUE, 4, 0, 4),
('4', 'Punjabi Elective', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('5', 'Hindi Elective', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('6', 'English Elective', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('7', 'Urdu', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('19', 'Sanskrit', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('23', 'French', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('24', 'German', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('25', 'History', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('26', 'Economics', 'Humanities / Commerce', 'humanities', FALSE, TRUE, 4, 0, 4),
('31', 'Political Science', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('32', 'Sociology', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('33', 'Public Administration', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('35', 'Religion', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('36', 'Music (Vocal)', 'Humanities / Arts', 'humanities', FALSE, TRUE, 2, 2, 4),
('37', 'Music Instrumental', 'Humanities / Arts', 'humanities', FALSE, TRUE, 2, 2, 4),
('38', 'Music (Tabla)', 'Humanities / Arts', 'humanities', FALSE, TRUE, 2, 2, 4),
('39', 'Gurmat Sangeet', 'Humanities / Arts', 'humanities', FALSE, TRUE, 2, 2, 4),
('40', 'Dance', 'Humanities / Arts', 'humanities', FALSE, TRUE, 2, 2, 4),
('41', 'Philosophy', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('42', 'Geography', 'Humanities', 'humanities', FALSE, TRUE, 3, 1, 4),
('43', 'Defence Studies', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('44', 'Psychology', 'Humanities', 'humanities', FALSE, TRUE, 4, 0, 4),
('45', 'Home Science', 'Humanities / Home Science', 'humanities', FALSE, TRUE, 2, 2, 4),
('150', 'Media Studies', 'Humanities / Arts', 'humanities', FALSE, TRUE, 3, 1, 4),

-- Science and Mathematics
('28', 'Mathematics', 'Science / Commerce', 'science', FALSE, TRUE, 5, 0, 5),
('52', 'Physics', 'Science', 'science', FALSE, TRUE, 3, 2, 5),
('53', 'Chemistry', 'Science', 'science', FALSE, TRUE, 3, 2, 5),
('54', 'Biology', 'Science', 'science', FALSE, TRUE, 3, 2, 5),
('146', 'Computer Science', 'Science / Multiple Streams (Elective)', 'science', FALSE, TRUE, 3, 2, 5),

-- Commerce Subjects
('141', 'Business Studies', 'Commerce', 'commerce', FALSE, TRUE, 4, 0, 4),
('142', 'Accountancy', 'Commerce', 'commerce', FALSE, TRUE, 4, 0, 4),
('144', 'Fundamentals of E-Business', 'Commerce', 'commerce', FALSE, TRUE, 4, 0, 4),

-- Elective for Multiple Streams
('49', 'Physical Education & Sports', 'All Streams (Elective)', 'elective', FALSE, TRUE, 2, 2, 4),
('72', 'Computer Application', 'Multiple Streams (Elective)', 'elective', FALSE, TRUE, 2, 2, 4),
('209', 'National Cadet Corps (NCC)', 'All Streams (Elective)', 'elective', FALSE, TRUE, 2, 2, 4),

-- Agriculture
('65', 'Agriculture', 'Agriculture', 'vocational', FALSE, TRUE, 3, 2, 5),

-- NSQF Subjects
('196', 'Organized Retail (Retail Sales Associate)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('197', 'Automotive (Four Wheeler Services Technician)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('198', 'Health Care (General Duty Assistant Trainee)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('199', 'IT/ITES (Junior Software Developer)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('200', 'Private Security (CCTV Video Footage Auditor)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('201', 'Beauty & Wellness (Beauty Therapist)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('202', 'Travel, Tourism & Hospitality (Customer Service Exe)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('203', 'Physical Education (Physical Education Assistant)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('204', 'Agriculture (Small Poultry Farmer)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('206', 'Apparel Made-ups (Specialized Sewing Machine Op)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('207', 'Construction (Painter and Decorator)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('208', 'Plumbing (Advance Plumbing Technician)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('218', 'Power (Distribution Lineman)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('219', 'Banking, Financial Services (BFSI)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('220', 'Electronics (Solar Panel Installation Technician)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('221', 'Food Processing (Craft Baker)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),
('222', 'Telecom (Optical Fiber Technician)', 'NSQF', 'nsqf', FALSE, TRUE, 2, 3, 5),

-- Vocational Agriculture Group
('73', 'Commercial Crops', 'Vocational (Agriculture Group)', 'vocational', FALSE, TRUE, 3, 2, 5),
('74', 'Landscaping & Floriculture', 'Vocational (Agriculture Group)', 'vocational', FALSE, TRUE, 3, 2, 5),
('75', 'Post Harvest Technology & Preservation', 'Vocational (Agriculture Group)', 'vocational', FALSE, TRUE, 3, 2, 5),

-- Vocational Home Science Group
('92', 'Food Processing', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('93', 'Plant Management', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('94', 'Food Packaging', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('95', 'Dress Making', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('96', 'Commercial Clothing', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('97', 'Unit Management', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('98', 'Fashioned Knitwear', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('99', 'Circular Knitting', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('100', 'Textile Yarn Calculations & Garment Making', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('101', 'Fabric Structure & Designing', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('102', 'Textile Testing & Dyeing', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('103', 'Power Loom Mechanics & Operations', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('104', 'Textile Designing & Printing- II', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('105', 'Textile Testing & Finishing', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('106', 'Textile Dyeing- II', 'Vocational (Home Science Group)', 'vocational', FALSE, TRUE, 2, 3, 5),

-- Vocational Engineering & Technology Group
('107', 'Elements of Electrical Technology', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('108', 'Electrical Domestic Appliances- II', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('109', 'Materials & Workshop Practice- II', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('110', 'Digital Electronics & Communication', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('111', 'Test & Measuring Instruments', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('112', 'Electronics Devices & Circuits', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('113', 'Engineering Drawing- II', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 1, 4, 5),
('114', 'Workshop Practice- II', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 1, 4, 5),
('115', 'Construction Material & Estimate', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('116', 'Fitting & Welding', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 1, 4, 5),
('117', 'Machine Tool Operations', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 1, 4, 5),
('118', 'Mechanical Drawing- II', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 1, 4, 5),
('122', 'Workshop Technology', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('123', 'Garage Practice & Management', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('124', 'Automobile Suspension & Controls', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('125', 'Object Oriented Programming in C++', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 3, 2, 5),
('126', 'Desktop Publishing', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('127', 'Networking', 'Vocational (Engineering & Technology Group)', 'vocational', FALSE, TRUE, 2, 3, 5),

-- Vocational Common Foundation
('138', 'General Foundation Course', 'Vocational (Common Foundation)', 'vocational', FALSE, TRUE, 4, 1, 5),

-- Vocational Business & Commerce Group
('169', 'Principles & Practices of Insurance - II', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 4, 1, 5),
('170', 'Insurance Legislation- II', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 4, 1, 5),
('171', 'Insurance Salesmanship-II', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 3, 2, 5),
('172', 'Advanced Management- II', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 4, 1, 5),
('173', 'Marketing Management- II', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 4, 1, 5),
('174', 'Salesmanship-II', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 3, 2, 5),
('181', 'Management II', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 4, 1, 5),
('182', 'Technology & E-Commerce- II', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 3, 2, 5),
('183', 'Import Management', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 4, 1, 5),
('187', 'Book Keeping-II', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 4, 1, 5),
('188', 'Application of Management', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 4, 1, 5),
('189', 'Co-operative Management-II', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 4, 1, 5),
('190', 'Elements of Book Keeping- II', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 4, 1, 5),
('191', 'Principles of Management- II', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 4, 1, 5),
('192', 'Fundamentals of Income Tax -II', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 4, 1, 5),
('211', 'Office Management- II', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 3, 2, 5),
('212', 'Typography-II (English / Punjabi)', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('213', 'Accounting & E-Commerce', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 3, 2, 5),
('214', 'Shorthand- II (English/Punjabi)', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 2, 3, 5),
('215', 'Managerial Economics & Statistics', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 4, 1, 5),
('216', 'Advances & Foreign Exchange', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 4, 1, 5),
('217', 'Introduction to Computer Application', 'Vocational (Business & Commerce Group)', 'vocational', FALSE, TRUE, 2, 3, 5);

-- =====================================================================================
-- SECTION 4: VERIFICATION AND CLEANUP
-- =====================================================================================

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Update any existing references to use the new subject structure
-- (This will be handled by the application layer)

-- =====================================================================================
-- SECTION 5: VERIFICATION QUERIES
-- =====================================================================================

-- Show summary of inserted subjects by category
SELECT 'PUNJAB BOARD SUBJECTS SUMMARY:' AS info;

SELECT
    subject_category_new AS category,
    COUNT(*) AS total_subjects,
    COUNT(CASE WHEN is_compulsory = TRUE THEN 1 END) AS compulsory_subjects,
    COUNT(CASE WHEN is_elective = TRUE THEN 1 END) AS elective_subjects
FROM subjects
GROUP BY subject_category_new
ORDER BY
    CASE subject_category_new
        WHEN 'compulsory_all' THEN 1
        WHEN 'compulsory_language' THEN 2
        WHEN 'science' THEN 3
        WHEN 'commerce' THEN 4
        WHEN 'humanities' THEN 5
        WHEN 'vocational' THEN 6
        WHEN 'nsqf' THEN 7
        WHEN 'elective' THEN 8
        ELSE 9
    END;

-- Show subjects by stream/group
SELECT 'SUBJECTS BY STREAM/GROUP:' AS info;
SELECT
    stream_group,
    COUNT(*) AS subject_count,
    GROUP_CONCAT(CONCAT(code, '-', name) ORDER BY CAST(code AS UNSIGNED) SEPARATOR ', ') AS subjects
FROM subjects
GROUP BY stream_group
ORDER BY subject_count DESC;

-- Show compulsory subjects for all streams
SELECT 'COMPULSORY SUBJECTS FOR ALL STREAMS:' AS info;
SELECT code, name, stream_group
FROM subjects
WHERE is_compulsory = TRUE
ORDER BY CAST(code AS UNSIGNED);

-- Show science subjects
SELECT 'SCIENCE SUBJECTS:' AS info;
SELECT code, name, stream_group,
       CONCAT(max_theory_lectures, ' theory + ', max_practical_lectures, ' practical = ', total_lectures_per_week, ' total') AS lecture_distribution
FROM subjects
WHERE subject_category_new = 'science' OR stream_group LIKE '%Science%'
ORDER BY CAST(code AS UNSIGNED);

-- Show commerce subjects
SELECT 'COMMERCE SUBJECTS:' AS info;
SELECT code, name, stream_group,
       CONCAT(max_theory_lectures, ' theory + ', max_practical_lectures, ' practical = ', total_lectures_per_week, ' total') AS lecture_distribution
FROM subjects
WHERE subject_category_new = 'commerce' OR stream_group LIKE '%Commerce%'
ORDER BY CAST(code AS UNSIGNED);

-- Show humanities subjects
SELECT 'HUMANITIES SUBJECTS:' AS info;
SELECT code, name, stream_group
FROM subjects
WHERE subject_category_new = 'humanities'
ORDER BY CAST(code AS UNSIGNED);

-- Show vocational subjects by group
SELECT 'VOCATIONAL SUBJECTS BY GROUP:' AS info;
SELECT
    CASE
        WHEN stream_group LIKE '%Agriculture%' THEN 'Agriculture Group'
        WHEN stream_group LIKE '%Home Science%' THEN 'Home Science Group'
        WHEN stream_group LIKE '%Engineering%' THEN 'Engineering & Technology Group'
        WHEN stream_group LIKE '%Business & Commerce%' THEN 'Business & Commerce Group'
        WHEN stream_group LIKE '%Common Foundation%' THEN 'Common Foundation'
        ELSE 'Other Vocational'
    END AS vocational_group,
    COUNT(*) AS subject_count
FROM subjects
WHERE subject_category_new = 'vocational'
GROUP BY vocational_group
ORDER BY subject_count DESC;

-- Show NSQF subjects
SELECT 'NSQF SUBJECTS:' AS info;
SELECT code, name
FROM subjects
WHERE subject_category_new = 'nsqf'
ORDER BY CAST(code AS UNSIGNED);

-- Final count verification
SELECT 'TOTAL SUBJECTS LOADED:' AS info;
SELECT COUNT(*) AS total_subjects FROM subjects;

SELECT 'SUBJECTS TABLE STRUCTURE UPDATED:' AS info;
DESCRIBE subjects;
