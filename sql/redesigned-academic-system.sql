-- =====================================================================================
-- REDESIGNED ACADEMIC MANAGEMENT SYSTEM - PROPERLY LINKED TABLES
-- =====================================================================================
-- This redesign eliminates multiple independent tables and creates a properly 
-- normalized system where all tables are interconnected through foreign keys
-- =====================================================================================

-- =====================================================================================
-- SECTION 1: CORE ACADEMIC STRUCTURE (LINKED DESIGN)
-- =====================================================================================

-- Drop existing independent tables to rebuild with proper relationships
DROP TABLE IF EXISTS trade_subject_combinations;
DROP TABLE IF EXISTS academic_subjects;
DROP TABLE IF EXISTS academic_trades;
DROP TABLE IF EXISTS academic_sections;

-- Create the main trades table (this will be referenced by everything)
CREATE TABLE IF NOT EXISTS trades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trade_code VARCHAR(20) NOT NULL UNIQUE,
    trade_name VARCHAR(100) NOT NULL,
    description TEXT,
    available_classes VARCHAR(50), -- e.g., '11,12'
    total_sections INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert the three main trades
INSERT IGNORE INTO trades (trade_code, trade_name, description, available_classes, total_sections) VALUES
('MED', 'Medical', 'Medical stream with Biology focus', '11,12', 2),
('NON_MED', 'Non-Medical', 'Non-Medical stream with Mathematics focus', '11,12', 6),
('COMM', 'Commerce', 'Commerce stream with Business focus', '11,12', 2);

-- Create sections table (linked to trades through class_sections)
CREATE TABLE IF NOT EXISTS sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    section_code VARCHAR(10) NOT NULL UNIQUE,
    section_name VARCHAR(50) NOT NULL,
    display_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sections
INSERT IGNORE INTO sections (section_code, section_name, display_order) VALUES
('A', 'Section A', 1), ('B', 'Section B', 2), ('C', 'Section C', 3),
('D', 'Section D', 4), ('E', 'Section E', 5), ('F', 'Section F', 6),
('G', 'Section G', 7), ('H', 'Section H', 8), ('I', 'Section I', 9), ('J', 'Section J', 10);

-- Create the main subjects table (this will be linked to trades)
CREATE TABLE IF NOT EXISTS subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_code VARCHAR(20) NOT NULL UNIQUE,
    subject_name VARCHAR(100) NOT NULL,
    subject_category ENUM('compulsory_core', 'selective', 'compulsory_language', 'additional_compulsory', 'optional') NOT NULL,
    theory_weightage DECIMAL(5,2) DEFAULT 70.00,
    practical_weightage DECIMAL(5,2) DEFAULT 20.00,
    cce_weightage DECIMAL(5,2) DEFAULT 10.00,
    max_theory_marks DECIMAL(6,2) DEFAULT 70.00,
    max_practical_marks DECIMAL(6,2) DEFAULT 20.00,
    max_cce_marks DECIMAL(6,2) DEFAULT 10.00,
    total_max_marks DECIMAL(6,2) DEFAULT 100.00,
    include_in_grand_total BOOLEAN DEFAULT TRUE,
    subject_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert all subjects
INSERT IGNORE INTO subjects (subject_code, subject_name, subject_category, theory_weightage, practical_weightage, cce_weightage, max_theory_marks, max_practical_marks, max_cce_marks, include_in_grand_total, subject_order) VALUES
-- Core subjects
('PHY', 'Physics', 'compulsory_core', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, TRUE, 1),
('CHEM', 'Chemistry', 'compulsory_core', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, TRUE, 2),
('MATH', 'Mathematics', 'compulsory_core', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 3),
('BIO', 'Biology', 'compulsory_core', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, TRUE, 4),
('ACC', 'Accountancy', 'compulsory_core', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 5),
('BS', 'Business Studies', 'compulsory_core', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 6),
-- Selective subjects
('ECO', 'Economics', 'selective', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 7),
('MOP', 'Methods of Production', 'selective', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, TRUE, 8),
('EBUS', 'E-Business', 'selective', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 9),
-- Language subjects
('PUN', 'Punjabi', 'compulsory_language', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 10),
('ENG', 'English', 'compulsory_language', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 11),
-- Additional compulsory (NOT in grand total)
('CS', 'Computer Science', 'additional_compulsory', 60.00, 30.00, 10.00, 60.00, 30.00, 10.00, FALSE, 12),
('EVS', 'Environmental Science', 'additional_compulsory', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, FALSE, 13),
-- Optional subjects (NOT in grand total)
('BIO_OPT', 'Biology (Optional)', 'optional', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, FALSE, 14),
('MATH_OPT', 'Mathematics (Optional)', 'optional', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, FALSE, 15);

-- =====================================================================================
-- SECTION 2: LINKED CLASS-TRADE-SECTION STRUCTURE
-- =====================================================================================

-- Create class_sections table that links classes, trades, and sections
CREATE TABLE IF NOT EXISTS class_sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    class_level VARCHAR(10) NOT NULL, -- '11' or '12'
    trade_id INT NOT NULL,
    section_id INT NOT NULL,
    academic_session VARCHAR(20) NOT NULL,
    total_students INT DEFAULT 0,
    class_teacher_id INT NULL, -- Link to users table
    room_id INT NULL, -- Link to rooms table
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints to ensure data integrity
    FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE RESTRICT,
    FOREIGN KEY (section_id) REFERENCES sections(id) ON DELETE RESTRICT,
    FOREIGN KEY (class_teacher_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE SET NULL,
    
    -- Ensure unique combinations
    UNIQUE KEY unique_class_trade_section_session (class_level, trade_id, section_id, academic_session),
    
    -- Indexes for performance
    INDEX idx_trade_class_session (trade_id, class_level, academic_session),
    INDEX idx_session_active (academic_session, is_active)
);

-- Insert class-trade-section combinations
INSERT IGNORE INTO class_sections (class_level, trade_id, section_id, academic_session) VALUES
-- Medical: 2 sections (A, B) for classes 11 & 12
('11', 1, 1, '2023-2024'), ('11', 1, 2, '2023-2024'), -- Class 11 Medical A, B
('12', 1, 1, '2023-2024'), ('12', 1, 2, '2023-2024'), -- Class 12 Medical A, B
-- Non-Medical: 6 sections (A-F) for classes 11 & 12
('11', 2, 1, '2023-2024'), ('11', 2, 2, '2023-2024'), ('11', 2, 3, '2023-2024'), -- Class 11 Non-Med A,B,C
('11', 2, 4, '2023-2024'), ('11', 2, 5, '2023-2024'), ('11', 2, 6, '2023-2024'), -- Class 11 Non-Med D,E,F
('12', 2, 1, '2023-2024'), ('12', 2, 2, '2023-2024'), ('12', 2, 3, '2023-2024'), -- Class 12 Non-Med A,B,C
('12', 2, 4, '2023-2024'), ('12', 2, 5, '2023-2024'), ('12', 2, 6, '2023-2024'), -- Class 12 Non-Med D,E,F
-- Commerce: 2 sections (A, B) for classes 11 & 12
('11', 3, 1, '2023-2024'), ('11', 3, 2, '2023-2024'), -- Class 11 Commerce A, B
('12', 3, 1, '2023-2024'), ('12', 3, 2, '2023-2024'); -- Class 12 Commerce A, B

-- =====================================================================================
-- SECTION 3: LINKED CURRICULUM STRUCTURE
-- =====================================================================================

-- Create curriculum table that links trades, subjects, and classes
CREATE TABLE IF NOT EXISTS curriculum (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trade_id INT NOT NULL,
    subject_id INT NOT NULL,
    class_level VARCHAR(10) NOT NULL, -- '11' or '12'
    subject_type ENUM('compulsory_core', 'selective_option', 'compulsory_language', 'additional_compulsory', 'optional') NOT NULL,
    is_mandatory BOOLEAN DEFAULT TRUE,
    academic_session VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    
    -- Ensure unique combinations
    UNIQUE KEY unique_trade_subject_class_session (trade_id, subject_id, class_level, academic_session),
    
    -- Indexes for performance
    INDEX idx_trade_class_session (trade_id, class_level, academic_session),
    INDEX idx_subject_session (subject_id, academic_session)
);

-- Insert curriculum data (trade-subject combinations)
INSERT IGNORE INTO curriculum (trade_id, subject_id, class_level, subject_type, is_mandatory, academic_session) VALUES
-- MEDICAL TRADE CURRICULUM
-- Core subjects for Medical
(1, 1, '11', 'compulsory_core', TRUE, '2023-2024'), (1, 1, '12', 'compulsory_core', TRUE, '2023-2024'), -- Physics
(1, 2, '11', 'compulsory_core', TRUE, '2023-2024'), (1, 2, '12', 'compulsory_core', TRUE, '2023-2024'), -- Chemistry
(1, 4, '11', 'compulsory_core', TRUE, '2023-2024'), (1, 4, '12', 'compulsory_core', TRUE, '2023-2024'), -- Biology
-- Selective subjects for Medical
(1, 7, '11', 'selective_option', FALSE, '2023-2024'), (1, 8, '11', 'selective_option', FALSE, '2023-2024'), -- Eco/MOP Class 11
(1, 7, '12', 'selective_option', FALSE, '2023-2024'), (1, 9, '12', 'selective_option', FALSE, '2023-2024'), -- Eco/E-Bus Class 12
-- Language subjects for Medical
(1, 10, '11', 'compulsory_language', TRUE, '2023-2024'), (1, 10, '12', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(1, 11, '11', 'compulsory_language', TRUE, '2023-2024'), (1, 11, '12', 'compulsory_language', TRUE, '2023-2024'), -- English
-- Additional compulsory for Medical
(1, 12, '11', 'additional_compulsory', TRUE, '2023-2024'), (1, 12, '12', 'additional_compulsory', TRUE, '2023-2024'), -- CS
(1, 13, '11', 'additional_compulsory', TRUE, '2023-2024'), (1, 13, '12', 'additional_compulsory', TRUE, '2023-2024'), -- EVS
-- Optional for Medical
(1, 15, '11', 'optional', FALSE, '2023-2024'), (1, 15, '12', 'optional', FALSE, '2023-2024'), -- Math Optional

-- NON-MEDICAL TRADE CURRICULUM
-- Core subjects for Non-Medical
(2, 1, '11', 'compulsory_core', TRUE, '2023-2024'), (2, 1, '12', 'compulsory_core', TRUE, '2023-2024'), -- Physics
(2, 2, '11', 'compulsory_core', TRUE, '2023-2024'), (2, 2, '12', 'compulsory_core', TRUE, '2023-2024'), -- Chemistry
(2, 3, '11', 'compulsory_core', TRUE, '2023-2024'), (2, 3, '12', 'compulsory_core', TRUE, '2023-2024'), -- Mathematics
-- Selective subjects for Non-Medical
(2, 7, '11', 'selective_option', FALSE, '2023-2024'), (2, 8, '11', 'selective_option', FALSE, '2023-2024'), -- Eco/MOP Class 11
(2, 7, '12', 'selective_option', FALSE, '2023-2024'), (2, 9, '12', 'selective_option', FALSE, '2023-2024'), -- Eco/E-Bus Class 12
-- Language subjects for Non-Medical
(2, 10, '11', 'compulsory_language', TRUE, '2023-2024'), (2, 10, '12', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(2, 11, '11', 'compulsory_language', TRUE, '2023-2024'), (2, 11, '12', 'compulsory_language', TRUE, '2023-2024'), -- English
-- Additional compulsory for Non-Medical
(2, 12, '11', 'additional_compulsory', TRUE, '2023-2024'), (2, 12, '12', 'additional_compulsory', TRUE, '2023-2024'), -- CS
(2, 13, '11', 'additional_compulsory', TRUE, '2023-2024'), (2, 13, '12', 'additional_compulsory', TRUE, '2023-2024'), -- EVS
-- Optional for Non-Medical
(2, 14, '11', 'optional', FALSE, '2023-2024'), (2, 14, '12', 'optional', FALSE, '2023-2024'), -- Bio Optional

-- COMMERCE TRADE CURRICULUM
-- Core subjects for Commerce
(3, 5, '11', 'compulsory_core', TRUE, '2023-2024'), (3, 5, '12', 'compulsory_core', TRUE, '2023-2024'), -- Accountancy
(3, 6, '11', 'compulsory_core', TRUE, '2023-2024'), (3, 6, '12', 'compulsory_core', TRUE, '2023-2024'), -- Business Studies
-- Selective subjects for Commerce
(3, 7, '11', 'selective_option', FALSE, '2023-2024'), (3, 8, '11', 'selective_option', FALSE, '2023-2024'), -- Eco/MOP Class 11
(3, 7, '12', 'selective_option', FALSE, '2023-2024'), (3, 9, '12', 'selective_option', FALSE, '2023-2024'), -- Eco/E-Bus Class 12
-- Language subjects for Commerce
(3, 10, '11', 'compulsory_language', TRUE, '2023-2024'), (3, 10, '12', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(3, 11, '11', 'compulsory_language', TRUE, '2023-2024'), (3, 11, '12', 'compulsory_language', TRUE, '2023-2024'), -- English
-- Additional compulsory for Commerce
(3, 12, '11', 'additional_compulsory', TRUE, '2023-2024'), (3, 12, '12', 'additional_compulsory', TRUE, '2023-2024'), -- CS
(3, 13, '11', 'additional_compulsory', TRUE, '2023-2024'), (3, 13, '12', 'additional_compulsory', TRUE, '2023-2024'), -- EVS
-- Optional for Commerce
(3, 15, '11', 'optional', FALSE, '2023-2024'), (3, 15, '12', 'optional', FALSE, '2023-2024'); -- Math Optional

-- =====================================================================================
-- SECTION 4: ENHANCED STUDENT MANAGEMENT (LINKED TO CLASS_SECTIONS)
-- =====================================================================================

-- Update students table to link to class_sections instead of separate fields
ALTER TABLE students
ADD COLUMN IF NOT EXISTS class_section_id INT AFTER stream_id,
ADD CONSTRAINT fk_student_class_section FOREIGN KEY (class_section_id) REFERENCES class_sections(id) ON DELETE SET NULL;

-- Create student_subjects table (links students to their chosen subjects)
CREATE TABLE IF NOT EXISTS student_subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    class_section_id INT NOT NULL,
    academic_session VARCHAR(20) NOT NULL,
    is_chosen BOOLEAN DEFAULT TRUE, -- For selective/optional subjects
    enrollment_date DATE DEFAULT (CURRENT_DATE),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Foreign key constraints
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (class_section_id) REFERENCES class_sections(id) ON DELETE CASCADE,

    -- Ensure unique combinations
    UNIQUE KEY unique_student_subject_session (student_id, subject_id, academic_session),

    -- Indexes for performance
    INDEX idx_student_session (student_id, academic_session),
    INDEX idx_class_section_subject (class_section_id, subject_id),
    INDEX idx_subject_session (subject_id, academic_session)
);

-- =====================================================================================
-- SECTION 5: ENHANCED MARKING SYSTEM (PROPERLY LINKED)
-- =====================================================================================

-- Update student_subject_marks to link to the new structure
ALTER TABLE student_subject_marks
ADD COLUMN IF NOT EXISTS class_section_id INT AFTER student_id,
ADD CONSTRAINT fk_marks_class_section FOREIGN KEY (class_section_id) REFERENCES class_sections(id) ON DELETE CASCADE;

-- =====================================================================================
-- SECTION 6: COMPREHENSIVE LINKED VIEWS
-- =====================================================================================

-- View 1: Complete Curriculum Structure (Properly Linked)
CREATE OR REPLACE VIEW v_curriculum_structure AS
SELECT
    t.trade_code,
    t.trade_name,
    c.class_level,
    c.academic_session,
    s.subject_code,
    s.subject_name,
    s.subject_category,
    c.subject_type,
    c.is_mandatory,
    s.include_in_grand_total,
    s.theory_weightage,
    s.practical_weightage,
    s.cce_weightage,
    s.total_max_marks,
    CASE
        WHEN c.subject_type = 'compulsory_core' THEN 'Core Subject (Counts in Total)'
        WHEN c.subject_type = 'selective_option' THEN 'Choose One (Counts in Total)'
        WHEN c.subject_type = 'compulsory_language' THEN 'Language (Counts in Total)'
        WHEN c.subject_type = 'additional_compulsory' THEN 'Additional Compulsory (NOT in Total)'
        WHEN c.subject_type = 'optional' THEN 'Optional (NOT in Total)'
        ELSE 'Other'
    END AS subject_type_description
FROM trades t
JOIN curriculum c ON t.id = c.trade_id
JOIN subjects s ON c.subject_id = s.id
ORDER BY t.trade_code, c.class_level, s.subject_order, s.subject_name;

-- View 2: Class Structure with All Links
CREATE OR REPLACE VIEW v_class_structure AS
SELECT
    cs.id AS class_section_id,
    cs.class_level,
    t.trade_code,
    t.trade_name,
    sec.section_code,
    sec.section_name,
    cs.academic_session,
    cs.total_students,
    u.name AS class_teacher_name,
    r.room_number,
    COALESCE(r.room_name, CONCAT('Room ', r.room_number)) AS room_name,
    r.capacity AS room_capacity,
    cs.is_active,
    CASE
        WHEN r.id IS NOT NULL THEN 'Room Assigned'
        ELSE 'No Room Assigned'
    END AS room_status
FROM class_sections cs
JOIN trades t ON cs.trade_id = t.id
JOIN sections sec ON cs.section_id = sec.id
LEFT JOIN users u ON cs.class_teacher_id = u.id
LEFT JOIN rooms r ON cs.room_id = r.id
ORDER BY cs.academic_session, cs.class_level, t.trade_code, sec.section_code;

-- View 3: Student Complete Information (Properly Linked)
CREATE OR REPLACE VIEW v_student_complete_info AS
SELECT
    s.id AS student_id,
    s.student_id AS roll_number,
    s.name AS student_name,
    s.father_name,
    s.mother_name,
    cs.class_level,
    t.trade_code,
    t.trade_name,
    sec.section_code,
    sec.section_name,
    s.academic_session,
    u.name AS class_teacher_name,
    r.room_number,
    cs.total_students AS class_strength,
    s.created_at AS admission_date
FROM students s
LEFT JOIN class_sections cs ON s.class_section_id = cs.id
LEFT JOIN trades t ON cs.trade_id = t.id
LEFT JOIN sections sec ON cs.section_id = sec.id
LEFT JOIN users u ON cs.class_teacher_id = u.id
LEFT JOIN rooms r ON cs.room_id = r.id
ORDER BY s.student_id;

-- View 4: Enhanced Student Marks (Properly Linked)
CREATE OR REPLACE VIEW v_enhanced_student_marks_linked AS
SELECT
    s.student_id AS roll_number,
    s.name AS student_name,
    cs.class_level,
    t.trade_code,
    t.trade_name,
    sec.section_code,
    ssm.academic_session,
    sub.subject_code,
    sub.subject_name,
    sub.subject_category,
    e.exam_name,

    -- Marks components
    ssm.theory_marks,
    ssm.practical_marks,
    ssm.internal_marks AS cce_marks,
    (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) AS total_marks,
    sub.total_max_marks AS max_marks,
    ROUND(((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / sub.total_max_marks) * 100, 2) AS percentage,

    -- Grade calculation
    CASE
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / sub.total_max_marks) * 100 >= 90 THEN 'A+'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / sub.total_max_marks) * 100 >= 80 THEN 'A'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / sub.total_max_marks) * 100 >= 70 THEN 'B+'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / sub.total_max_marks) * 100 >= 60 THEN 'B'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / sub.total_max_marks) * 100 >= 50 THEN 'C+'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / sub.total_max_marks) * 100 >= 40 THEN 'C'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / sub.total_max_marks) * 100 >= 33 THEN 'D'
        ELSE 'F'
    END AS grade,

    -- Subject categorization
    sub.include_in_grand_total,
    CASE
        WHEN sub.include_in_grand_total = TRUE THEN 'Counts in Grand Total'
        ELSE 'Additional/Optional Subject'
    END AS grade_calculation_status,

    -- Pass/Fail status
    CASE
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / sub.total_max_marks) * 100 >= 33
        THEN 'PASS'
        ELSE 'FAIL'
    END AS result_status,

    ssm.remarks,
    ssm.created_at AS marks_entry_date

FROM students s
JOIN class_sections cs ON s.class_section_id = cs.id
JOIN trades t ON cs.trade_id = t.id
JOIN sections sec ON cs.section_id = sec.id
JOIN student_subject_marks ssm ON s.id = ssm.student_id AND cs.id = ssm.class_section_id
JOIN subjects sub ON ssm.subject_id = sub.id
JOIN exams e ON ssm.exam_id = e.exam_id
ORDER BY s.student_id, ssm.academic_session, sub.subject_order, e.exam_name;

-- View 5: Student Grand Totals (Properly Linked with Correct Calculation)
CREATE OR REPLACE VIEW v_student_grand_totals_linked AS
SELECT
    s.student_id AS roll_number,
    s.name AS student_name,
    cs.class_level,
    t.trade_code,
    t.trade_name,
    sec.section_code,
    ssm.academic_session,
    e.exam_name,

    -- Grand total calculation (only subjects that count)
    COUNT(CASE WHEN sub.include_in_grand_total = TRUE THEN 1 END) AS subjects_in_total,
    SUM(CASE WHEN sub.include_in_grand_total = TRUE THEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) ELSE 0 END) AS grand_total_marks,
    SUM(CASE WHEN sub.include_in_grand_total = TRUE THEN sub.total_max_marks ELSE 0 END) AS grand_max_marks,
    ROUND((SUM(CASE WHEN sub.include_in_grand_total = TRUE THEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) ELSE 0 END) /
           SUM(CASE WHEN sub.include_in_grand_total = TRUE THEN sub.total_max_marks ELSE 0 END)) * 100, 2) AS grand_percentage,

    -- Additional subjects (not counted in total)
    COUNT(CASE WHEN sub.include_in_grand_total = FALSE THEN 1 END) AS additional_subjects,
    SUM(CASE WHEN sub.include_in_grand_total = FALSE THEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) ELSE 0 END) AS additional_total_marks,
    SUM(CASE WHEN sub.include_in_grand_total = FALSE THEN sub.total_max_marks ELSE 0 END) AS additional_max_marks,

    -- Pass/Fail status
    SUM(CASE WHEN sub.include_in_grand_total = TRUE AND ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / sub.total_max_marks) * 100 < 33 THEN 1 ELSE 0 END) AS failed_core_subjects,
    CASE
        WHEN SUM(CASE WHEN sub.include_in_grand_total = TRUE AND ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / sub.total_max_marks) * 100 < 33 THEN 1 ELSE 0 END) = 0 THEN 'PASS'
        ELSE 'FAIL'
    END AS overall_result

FROM students s
JOIN class_sections cs ON s.class_section_id = cs.id
JOIN trades t ON cs.trade_id = t.id
JOIN sections sec ON cs.section_id = sec.id
JOIN student_subject_marks ssm ON s.id = ssm.student_id AND cs.id = ssm.class_section_id
JOIN subjects sub ON ssm.subject_id = sub.id
JOIN exams e ON ssm.exam_id = e.exam_id
GROUP BY s.id, cs.id, ssm.academic_session, e.exam_id
ORDER BY s.student_id, ssm.academic_session, e.exam_name;
