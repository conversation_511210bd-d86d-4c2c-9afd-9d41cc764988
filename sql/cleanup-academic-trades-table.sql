-- =====================================================================================
-- CLEANUP ACADEMIC TRADES TABLE - REMOVE REDUNDANT COLUMNS
-- =====================================================================================
-- Remove classes and total_sections columns from academic_trades table
-- This information is better managed through the linked class_sections table
-- =====================================================================================

-- First, let's check the current structure of academic_trades table
SELECT 'CURRENT ACADEMIC_TRADES TABLE STRUCTURE:' AS info;
DESCRIBE academic_trades;

-- Show current data in the table
SELECT 'CURRENT DATA IN ACADEMIC_TRADES:' AS info;
SELECT * FROM academic_trades;

-- =====================================================================================
-- SECTION 1: REMOVE REDUNDANT COLUMNS
-- =====================================================================================

-- Remove the available_classes column (redundant - managed by class_sections)
ALTER TABLE academic_trades 
DROP COLUMN IF EXISTS available_classes;

-- Remove the total_sections column (redundant - calculated from class_sections)
ALTER TABLE academic_trades 
DROP COLUMN IF EXISTS total_sections;

-- =====================================================================================
-- SECTION 2: VERIFY THE CLEANED STRUCTURE
-- =====================================================================================

-- Show the cleaned table structure
SELECT 'CLEANED ACADEMIC_TRADES TABLE STRUCTURE:' AS info;
DESCRIBE academic_trades;

-- Show the cleaned data
SELECT 'CLEANED DATA IN ACADEMIC_TRADES:' AS info;
SELECT * FROM academic_trades;

-- =====================================================================================
-- SECTION 3: CREATE DYNAMIC VIEWS FOR CLASS/SECTION INFORMATION
-- =====================================================================================

-- Create a view that shows trade information with dynamic class/section counts
CREATE OR REPLACE VIEW v_trade_class_summary AS
SELECT 
    t.id,
    t.trade_code,
    t.trade_name,
    t.description,
    t.is_active,
    
    -- Dynamic class information from class_sections table
    GROUP_CONCAT(DISTINCT cs.class_level ORDER BY cs.class_level) AS available_classes,
    COUNT(DISTINCT cs.class_level) AS total_classes,
    COUNT(DISTINCT cs.section_id) AS total_sections,
    COUNT(cs.id) AS total_class_section_combinations,
    
    -- Section breakdown by class
    GROUP_CONCAT(
        DISTINCT CONCAT(cs.class_level, '-', sec.section_code) 
        ORDER BY cs.class_level, sec.section_code
    ) AS class_section_combinations,
    
    -- Student statistics
    SUM(cs.total_students) AS total_enrolled_students,
    ROUND(AVG(cs.total_students), 1) AS avg_students_per_section,
    
    -- Academic session
    cs.academic_session,
    
    t.created_at,
    t.updated_at
    
FROM academic_trades t
LEFT JOIN class_sections cs ON t.id = cs.trade_id AND cs.is_active = TRUE
LEFT JOIN sections sec ON cs.section_id = sec.id
WHERE t.is_active = TRUE
GROUP BY t.id, t.trade_code, t.trade_name, t.description, cs.academic_session, t.is_active, t.created_at, t.updated_at
ORDER BY t.trade_code, cs.academic_session;

-- =====================================================================================
-- SECTION 4: CREATE TRADE STATISTICS VIEW
-- =====================================================================================

-- Create a comprehensive trade statistics view
CREATE OR REPLACE VIEW v_trade_statistics AS
SELECT 
    t.trade_code,
    t.trade_name,
    t.description,
    
    -- Class and section statistics
    COUNT(DISTINCT cs.class_level) AS classes_offered,
    COUNT(DISTINCT cs.section_id) AS sections_offered,
    COUNT(cs.id) AS total_combinations,
    
    -- Student statistics
    SUM(cs.total_students) AS total_students,
    ROUND(AVG(cs.total_students), 1) AS avg_students_per_section,
    MAX(cs.total_students) AS max_section_size,
    MIN(cs.total_students) AS min_section_size,
    
    -- Academic session
    cs.academic_session,
    
    -- Detailed breakdown
    GROUP_CONCAT(
        DISTINCT CONCAT('Class ', cs.class_level, ': ', 
        (SELECT COUNT(*) FROM class_sections cs2 
         WHERE cs2.trade_id = t.id AND cs2.class_level = cs.class_level 
         AND cs2.academic_session = cs.academic_session), ' sections')
        ORDER BY cs.class_level
    ) AS class_breakdown
    
FROM academic_trades t
LEFT JOIN class_sections cs ON t.id = cs.trade_id AND cs.is_active = TRUE
WHERE t.is_active = TRUE
GROUP BY t.id, t.trade_code, t.trade_name, t.description, cs.academic_session
ORDER BY t.trade_code, cs.academic_session;

-- =====================================================================================
-- SECTION 5: VERIFICATION AND TESTING
-- =====================================================================================

-- Test the new dynamic views
SELECT 'TRADE CLASS SUMMARY (DYNAMIC):' AS info;
SELECT 
    trade_code,
    trade_name,
    available_classes,
    total_classes,
    total_sections,
    total_class_section_combinations,
    total_enrolled_students,
    academic_session
FROM v_trade_class_summary
WHERE academic_session = '2023-2024'
ORDER BY trade_code;

SELECT 'TRADE STATISTICS (COMPREHENSIVE):' AS info;
SELECT 
    trade_code,
    trade_name,
    classes_offered,
    sections_offered,
    total_students,
    avg_students_per_section,
    class_breakdown,
    academic_session
FROM v_trade_statistics
WHERE academic_session = '2023-2024'
ORDER BY trade_code;

-- =====================================================================================
-- SECTION 6: UPDATE EXISTING VIEWS TO USE DYNAMIC DATA
-- =====================================================================================

-- Update v_comprehensive_trade_analysis to use dynamic data
CREATE OR REPLACE VIEW v_comprehensive_trade_analysis AS
SELECT 
    tp.academic_session,
    tp.trade,
    tp.trade_code,
    tp.total_students,
    tp.trade_average_percentage,
    tp.pass_rate,
    tp.trade_performance_rating,
    tp.excellent_performers,
    tp.good_performers,
    tp.average_performers,
    tp.poor_performers,
    
    -- Add dynamic trade statistics
    ts.classes_offered,
    ts.sections_offered,
    ts.avg_students_per_section,
    ts.class_breakdown,
    
    -- Add curriculum information
    tc.total_subjects_offered,
    tc.compulsory_subjects,
    tc.elective_subjects,
    
    -- Add top performer information
    tt.top_performer_name,
    tt.top_performer_percentage
    
FROM v_trade_performance tp
LEFT JOIN v_trade_statistics ts ON tp.trade_code = ts.trade_code AND tp.academic_session = ts.academic_session
LEFT JOIN (
    SELECT 
        trade_code,
        COUNT(*) AS total_subjects_offered,
        SUM(CASE WHEN is_compulsory = 1 THEN 1 ELSE 0 END) AS compulsory_subjects,
        SUM(CASE WHEN is_elective = 1 THEN 1 ELSE 0 END) AS elective_subjects
    FROM v_corrected_trade_curriculum
    GROUP BY trade_code
) tc ON tp.trade_code = tc.trade_code
LEFT JOIN (
    SELECT 
        trade_code,
        student_name AS top_performer_name,
        session_average_percentage AS top_performer_percentage
    FROM v_trade_toppers
    WHERE trade_rank = 1
) tt ON tp.trade_code = tt.trade_code
ORDER BY tp.trade_average_percentage DESC;

-- Final verification
SELECT 'FINAL VERIFICATION - CLEANED ACADEMIC_TRADES TABLE:' AS info;
SELECT 
    'Table now contains only essential columns:' AS note,
    'id, trade_code, trade_name, description, is_active, created_at, updated_at' AS columns;

SELECT 'DYNAMIC CLASS/SECTION INFO NOW AVAILABLE THROUGH VIEWS:' AS info;
SELECT 
    'v_trade_class_summary - Dynamic class/section information' AS view1,
    'v_trade_statistics - Comprehensive trade statistics' AS view2,
    'v_comprehensive_trade_analysis - Complete analysis with dynamic data' AS view3;
