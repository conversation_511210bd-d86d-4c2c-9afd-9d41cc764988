-- =====================================================================================
-- FIX ALL TRADE VIEWS - COMPLETE CORRECTION OF SUBJECT VS TRADE CONFUSION
-- =====================================================================================
-- This script fixes ALL views that incorrectly use subjects as trades
-- Ensures consistent use of proper trades: Medical, Non-Medical, Commerce
-- =====================================================================================

-- =====================================================================================
-- SECTION 1: FIX v_student_session_summary VIEW
-- =====================================================================================

DROP VIEW IF EXISTS v_student_session_summary;
CREATE VIEW v_student_session_summary AS
SELECT 
    s.student_id AS roll_number,
    s.name AS student_name,
    s.class,
    s.section,
    COALESCE(tm.correct_trade_name, s.trade, 'Unknown') AS trade,
    tm.correct_trade_code AS trade_code,
    ssm.academic_session,
    
    -- Subject and exam statistics
    COUNT(DISTINCT ssm.subject_id) AS total_subjects,
    COUNT(ssm.exam_id) AS total_exams_taken,
    
    -- Performance metrics
    ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) AS session_average_percentage,
    MAX((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) AS highest_percentage,
    MIN((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) AS lowest_percentage,
    
    -- Grade distribution
    SUM(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 80 THEN 1 ELSE 0 END) AS excellent_grades,
    SUM(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 BETWEEN 60 AND 79.99 THEN 1 ELSE 0 END) AS good_grades,
    SUM(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 BETWEEN 40 AND 59.99 THEN 1 ELSE 0 END) AS average_grades,
    SUM(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 < 33 THEN 1 ELSE 0 END) AS failed_grades,
    
    -- Pass/Fail statistics
    SUM(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 33 THEN 1 ELSE 0 END) AS exams_passed,
    SUM(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 < 33 THEN 1 ELSE 0 END) AS exams_failed,
    ROUND(SUM(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 33 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS pass_rate,
    
    -- Overall result
    CASE 
        WHEN SUM(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 < 33 THEN 1 ELSE 0 END) = 0 THEN 'PROMOTED'
        WHEN SUM(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 < 33 THEN 1 ELSE 0 END) <= 2 THEN 'CONDITIONAL'
        ELSE 'DETAINED'
    END AS session_result
    
FROM students s
JOIN student_subject_marks ssm ON s.id = ssm.student_id
LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
GROUP BY s.id, ssm.academic_session, COALESCE(tm.correct_trade_name, s.trade), tm.correct_trade_code
ORDER BY s.student_id, ssm.academic_session;

-- =====================================================================================
-- SECTION 2: FIX v_session_toppers VIEW
-- =====================================================================================

DROP VIEW IF EXISTS v_session_toppers;
CREATE VIEW v_session_toppers AS
SELECT 
    ranked_students.academic_session,
    ranked_students.roll_number,
    ranked_students.student_name,
    ranked_students.class,
    ranked_students.section,
    ranked_students.trade,
    ranked_students.trade_code,
    ranked_students.session_average_percentage,
    ranked_students.total_subjects,
    ranked_students.exams_passed,
    ranked_students.excellent_grades,
    ranked_students.session_result,
    ranked_students.overall_rank
FROM (
    SELECT 
        sss.roll_number,
        sss.student_name,
        sss.class,
        sss.section,
        sss.trade,
        sss.trade_code,
        sss.academic_session,
        sss.total_subjects,
        sss.total_exams_taken,
        sss.session_average_percentage,
        sss.highest_percentage,
        sss.lowest_percentage,
        sss.excellent_grades,
        sss.good_grades,
        sss.average_grades,
        sss.failed_grades,
        sss.exams_passed,
        sss.exams_failed,
        sss.pass_rate,
        sss.session_result,
        RANK() OVER (
            PARTITION BY sss.academic_session
            ORDER BY sss.session_average_percentage DESC, sss.exams_passed DESC, sss.excellent_grades DESC
        ) AS overall_rank
    FROM v_student_session_summary sss
    WHERE sss.session_result IN ('PROMOTED', 'CONDITIONAL')
) ranked_students
WHERE ranked_students.overall_rank <= 10
ORDER BY ranked_students.academic_session, ranked_students.overall_rank;

-- =====================================================================================
-- SECTION 3: FIX v_class_toppers VIEW
-- =====================================================================================

DROP VIEW IF EXISTS v_class_toppers;
CREATE VIEW v_class_toppers AS
SELECT 
    ranked_students.academic_session,
    ranked_students.class,
    ranked_students.section,
    ranked_students.roll_number,
    ranked_students.student_name,
    ranked_students.trade,
    ranked_students.trade_code,
    ranked_students.session_average_percentage,
    ranked_students.total_subjects,
    ranked_students.exams_passed,
    ranked_students.session_result,
    ranked_students.class_rank
FROM (
    SELECT 
        sss.roll_number,
        sss.student_name,
        sss.class,
        sss.section,
        sss.trade,
        sss.trade_code,
        sss.academic_session,
        sss.total_subjects,
        sss.total_exams_taken,
        sss.session_average_percentage,
        sss.highest_percentage,
        sss.lowest_percentage,
        sss.excellent_grades,
        sss.good_grades,
        sss.average_grades,
        sss.failed_grades,
        sss.exams_passed,
        sss.exams_failed,
        sss.pass_rate,
        sss.session_result,
        RANK() OVER (
            PARTITION BY sss.academic_session, sss.class, sss.section
            ORDER BY sss.session_average_percentage DESC, sss.exams_passed DESC
        ) AS class_rank
    FROM v_student_session_summary sss
    WHERE sss.session_result IN ('PROMOTED', 'CONDITIONAL')
) ranked_students
WHERE ranked_students.class_rank <= 3
ORDER BY ranked_students.academic_session, ranked_students.class, ranked_students.section, ranked_students.class_rank;

-- =====================================================================================
-- SECTION 4: CREATE COMPREHENSIVE TRADE ANALYSIS VIEW
-- =====================================================================================

CREATE OR REPLACE VIEW v_comprehensive_trade_analysis AS
SELECT 
    tp.academic_session,
    tp.trade,
    tp.trade_code,
    tp.total_students,
    tp.trade_average_percentage,
    tp.pass_rate,
    tp.trade_performance_rating,
    tp.excellent_performers,
    tp.good_performers,
    tp.average_performers,
    tp.poor_performers,
    
    -- Add curriculum information
    tc.total_subjects_offered,
    tc.compulsory_subjects,
    tc.elective_subjects,
    
    -- Add top performer information
    tt.top_performer_name,
    tt.top_performer_percentage
    
FROM v_trade_performance tp
LEFT JOIN (
    SELECT 
        trade_code,
        COUNT(*) AS total_subjects_offered,
        SUM(CASE WHEN is_compulsory = 1 THEN 1 ELSE 0 END) AS compulsory_subjects,
        SUM(CASE WHEN is_elective = 1 THEN 1 ELSE 0 END) AS elective_subjects
    FROM v_corrected_trade_curriculum
    GROUP BY trade_code
) tc ON tp.trade_code = tc.trade_code
LEFT JOIN (
    SELECT 
        trade_code,
        student_name AS top_performer_name,
        session_average_percentage AS top_performer_percentage
    FROM v_trade_toppers
    WHERE trade_rank = 1
) tt ON tp.trade_code = tt.trade_code
ORDER BY tp.trade_average_percentage DESC;

-- =====================================================================================
-- SECTION 5: VERIFICATION AND TESTING
-- =====================================================================================

-- Test the corrected views
SELECT 'CORRECTED STUDENT SESSION SUMMARY:' AS info;
SELECT roll_number, student_name, class, trade, trade_code, session_average_percentage, session_result
FROM v_student_session_summary
WHERE academic_session = '2023-2024'
ORDER BY session_average_percentage DESC;

SELECT 'CORRECTED SESSION TOPPERS:' AS info;
SELECT academic_session, overall_rank, student_name, class, trade, session_average_percentage
FROM v_session_toppers
WHERE academic_session = '2023-2024'
ORDER BY overall_rank;

SELECT 'CORRECTED CLASS TOPPERS:' AS info;
SELECT academic_session, class, section, class_rank, student_name, trade, session_average_percentage
FROM v_class_toppers
WHERE academic_session = '2023-2024'
ORDER BY class, section, class_rank;

SELECT 'COMPREHENSIVE TRADE ANALYSIS:' AS info;
SELECT trade, trade_code, total_students, trade_average_percentage, pass_rate, 
       trade_performance_rating, total_subjects_offered, top_performer_name
FROM v_comprehensive_trade_analysis
ORDER BY trade_average_percentage DESC;
