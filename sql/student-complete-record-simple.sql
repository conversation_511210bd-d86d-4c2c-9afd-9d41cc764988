-- =====================================================================================
-- STUDENT COMPLETE RECORD SYSTEM - SIMPLE VERSION
-- =====================================================================================
-- Create comprehensive student record views with existing table structure
-- Includes all exams, teachers, and attendance information
-- =====================================================================================

-- =====================================================================================
-- SECTION 1: ENHANCE EXISTING TABLES FOR COMPLETE RECORDS
-- =====================================================================================

-- Add subject teacher assignment table (simplified)
CREATE TABLE IF NOT EXISTS subject_teachers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_id INT NOT NULL,
    teacher_id INT NOT NULL,
    class_level VARCHAR(10),
    trade VARCHAR(50),
    section VARCHAR(10),
    academic_session VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    assigned_date DATE DEFAULT (CURRENT_DATE),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_subject_teacher_class (subject_id, teacher_id, class_level, trade, section, academic_session),
    INDEX idx_teacher_subject (teacher_id, subject_id),
    INDEX idx_class_session (class_level, academic_session)
);

-- Add class incharge assignment table (simplified)
CREATE TABLE IF NOT EXISTS class_incharge (
    id INT AUTO_INCREMENT PRIMARY KEY,
    teacher_id INT NOT NULL,
    class_level VARCHAR(10) NOT NULL,
    trade VARCHAR(50),
    section VARCHAR(10),
    academic_session VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    assigned_date DATE DEFAULT (CURRENT_DATE),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_class_incharge (class_level, trade, section, academic_session),
    INDEX idx_teacher_class (teacher_id, class_level),
    INDEX idx_session_class (academic_session, class_level)
);

-- Add student attendance table
CREATE TABLE IF NOT EXISTS student_attendance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    attendance_date DATE NOT NULL,
    is_present BOOLEAN DEFAULT TRUE,
    attendance_type ENUM('present', 'absent', 'late', 'excused') DEFAULT 'present',
    marked_by INT,
    academic_session VARCHAR(20) NOT NULL,
    remarks TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (marked_by) REFERENCES users(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_student_subject_date (student_id, subject_id, attendance_date),
    INDEX idx_student_session (student_id, academic_session),
    INDEX idx_subject_date (subject_id, attendance_date)
);

-- =====================================================================================
-- SECTION 2: INSERT SAMPLE DATA FOR DEMONSTRATION
-- =====================================================================================

-- Insert sample subject teachers
INSERT IGNORE INTO subject_teachers (subject_id, teacher_id, class_level, trade, section, academic_session) VALUES
-- Medical Trade Teachers
(1, 1, '11', 'Medical', 'A', '2023-2024'), -- English for Medical 11-A
(2, 2, '11', 'Medical', 'A', '2023-2024'), -- Punjabi for Medical 11-A
(54, 3, '11', 'Medical', 'A', '2023-2024'), -- Biology for Medical 11-A
(53, 4, '11', 'Medical', 'A', '2023-2024'), -- Chemistry for Medical 11-A
(52, 5, '11', 'Medical', 'A', '2023-2024'), -- Physics for Medical 11-A

-- Non-Medical Trade Teachers
(1, 1, '11', 'Non-Medical', 'A', '2023-2024'), -- English for Non-Medical 11-A
(2, 2, '11', 'Non-Medical', 'A', '2023-2024'), -- Punjabi for Non-Medical 11-A
(28, 3, '11', 'Non-Medical', 'A', '2023-2024'), -- Mathematics for Non-Medical 11-A
(53, 4, '11', 'Non-Medical', 'A', '2023-2024'), -- Chemistry for Non-Medical 11-A
(52, 5, '11', 'Non-Medical', 'A', '2023-2024'), -- Physics for Non-Medical 11-A

-- Commerce Trade Teachers
(1, 1, '11', 'Commerce', 'A', '2023-2024'), -- English for Commerce 11-A
(2, 2, '11', 'Commerce', 'A', '2023-2024'), -- Punjabi for Commerce 11-A
(141, 3, '11', 'Commerce', 'A', '2023-2024'), -- Business Studies for Commerce 11-A
(142, 4, '11', 'Commerce', 'A', '2023-2024'), -- Accountancy for Commerce 11-A
(26, 5, '11', 'Commerce', 'A', '2023-2024'); -- Economics for Commerce 11-A

-- Insert sample class incharge
INSERT IGNORE INTO class_incharge (teacher_id, class_level, trade, section, academic_session) VALUES
(1, '11', 'Medical', 'A', '2023-2024'), -- Medical 11-A incharge
(2, '11', 'Non-Medical', 'A', '2023-2024'), -- Non-Medical 11-A incharge
(3, '11', 'Commerce', 'A', '2023-2024'), -- Commerce 11-A incharge
(4, '12', 'Medical', 'A', '2023-2024'), -- Medical 12-A incharge
(5, '12', 'Non-Medical', 'A', '2023-2024'); -- Non-Medical 12-A incharge

-- Insert sample attendance data
INSERT IGNORE INTO student_attendance (student_id, subject_id, attendance_date, is_present, attendance_type, academic_session) VALUES
-- Student 1 attendance
(1, 1, '2023-09-01', TRUE, 'present', '2023-2024'),
(1, 2, '2023-09-01', TRUE, 'present', '2023-2024'),
(1, 54, '2023-09-01', FALSE, 'absent', '2023-2024'),
(1, 53, '2023-09-01', TRUE, 'present', '2023-2024'),
(1, 1, '2023-09-02', TRUE, 'present', '2023-2024'),
(1, 2, '2023-09-02', TRUE, 'present', '2023-2024'),
(1, 54, '2023-09-02', TRUE, 'present', '2023-2024'),
(1, 53, '2023-09-02', FALSE, 'absent', '2023-2024'),

-- Student 2 attendance
(2, 1, '2023-09-01', TRUE, 'present', '2023-2024'),
(2, 2, '2023-09-01', TRUE, 'present', '2023-2024'),
(2, 28, '2023-09-01', TRUE, 'present', '2023-2024'),
(2, 53, '2023-09-01', TRUE, 'present', '2023-2024'),
(2, 1, '2023-09-02', FALSE, 'absent', '2023-2024'),
(2, 2, '2023-09-02', TRUE, 'present', '2023-2024'),
(2, 28, '2023-09-02', TRUE, 'present', '2023-2024'),
(2, 53, '2023-09-02', TRUE, 'present', '2023-2024');

-- =====================================================================================
-- SECTION 3: COMPREHENSIVE STUDENT RECORD VIEWS
-- =====================================================================================

-- Main view: Complete student record with all exams and teachers
CREATE OR REPLACE VIEW v_student_complete_record AS
SELECT 
    -- Student Information
    s.id AS student_id,
    s.student_id AS roll_number,
    s.name AS student_name,
    s.father_name,
    s.mother_name,
    s.class AS class_level,
    s.section,
    s.trade,
    s.academic_session,
    
    -- Trade Information (from mapping)
    COALESCE(tm.correct_trade_name, s.trade, 'Unknown') AS trade_full_name,
    tm.correct_trade_code AS trade_code,
    
    -- Class Incharge Information
    ci_user.name AS class_incharge_name,
    ci_user.email AS class_incharge_email,
    ci_user.phone AS class_incharge_phone,
    
    -- Subject Information
    sub.id AS subject_id,
    sub.code AS subject_code,
    sub.name AS subject_name,
    sub.subject_category_new AS subject_category,
    sub.stream_group,
    
    -- Subject Teacher Information
    st_user.name AS subject_teacher_name,
    st_user.email AS subject_teacher_email,
    st_user.phone AS subject_teacher_phone,
    
    -- Exam Information
    e.exam_id,
    e.exam_name,
    e.exam_type,
    e.exam_date,
    e.max_marks AS exam_max_marks,
    
    -- Marks Information
    ssm.theory_marks,
    ssm.practical_marks,
    ssm.internal_marks AS cce_marks,
    (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) AS total_marks,
    ssm.max_marks,
    ROUND(((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100, 2) AS percentage,
    
    -- Grade Calculation
    CASE 
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 90 THEN 'A+'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 80 THEN 'A'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 70 THEN 'B+'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 60 THEN 'B'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 50 THEN 'C+'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 40 THEN 'C'
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 33 THEN 'D'
        ELSE 'F'
    END AS grade,
    
    -- Result Status
    CASE 
        WHEN ((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks) * 100 >= 33 
        THEN 'PASS' 
        ELSE 'FAIL' 
    END AS result_status,
    
    -- Additional Information
    ssm.remarks AS exam_remarks,
    ssm.created_at AS marks_entry_date
    
FROM students s
LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
LEFT JOIN subjects sub ON ssm.subject_id = sub.id
LEFT JOIN exams e ON ssm.exam_id = e.exam_id
LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
LEFT JOIN class_incharge ci ON s.class = ci.class_level 
    AND s.trade = ci.trade 
    AND s.section = ci.section
    AND s.academic_session = ci.academic_session 
    AND ci.is_active = TRUE
LEFT JOIN users ci_user ON ci.teacher_id = ci_user.id
LEFT JOIN subject_teachers st ON sub.id = st.subject_id 
    AND s.class = st.class_level 
    AND s.trade = st.trade
    AND s.section = st.section
    AND s.academic_session = st.academic_session 
    AND st.is_active = TRUE
LEFT JOIN users st_user ON st.teacher_id = st_user.id
ORDER BY s.student_id, s.academic_session, e.exam_date, sub.code;

-- Student attendance summary view
CREATE OR REPLACE VIEW v_student_attendance_summary AS
SELECT
    s.id AS student_id,
    s.student_id AS roll_number,
    s.name AS student_name,
    s.class AS class_level,
    s.section,
    s.trade,
    s.academic_session,

    -- Subject Information
    sub.code AS subject_code,
    sub.name AS subject_name,

    -- Attendance Statistics
    COUNT(sa.id) AS total_attendance_days,
    SUM(CASE WHEN sa.is_present = TRUE THEN 1 ELSE 0 END) AS days_present,
    SUM(CASE WHEN sa.is_present = FALSE THEN 1 ELSE 0 END) AS days_absent,
    SUM(CASE WHEN sa.attendance_type = 'late' THEN 1 ELSE 0 END) AS days_late,
    SUM(CASE WHEN sa.attendance_type = 'excused' THEN 1 ELSE 0 END) AS days_excused,

    -- Attendance Percentage
    ROUND((SUM(CASE WHEN sa.is_present = TRUE THEN 1 ELSE 0 END) * 100.0 / COUNT(sa.id)), 2) AS attendance_percentage,

    -- Attendance Status
    CASE
        WHEN ROUND((SUM(CASE WHEN sa.is_present = TRUE THEN 1 ELSE 0 END) * 100.0 / COUNT(sa.id)), 2) >= 75 THEN 'GOOD'
        WHEN ROUND((SUM(CASE WHEN sa.is_present = TRUE THEN 1 ELSE 0 END) * 100.0 / COUNT(sa.id)), 2) >= 60 THEN 'AVERAGE'
        ELSE 'POOR'
    END AS attendance_status,

    -- Subject Teacher
    st_user.name AS subject_teacher_name

FROM students s
LEFT JOIN student_attendance sa ON s.id = sa.student_id AND s.academic_session = sa.academic_session
LEFT JOIN subjects sub ON sa.subject_id = sub.id
LEFT JOIN subject_teachers st ON sub.id = st.subject_id
    AND s.class = st.class_level
    AND s.trade = st.trade
    AND s.section = st.section
    AND s.academic_session = st.academic_session
    AND st.is_active = TRUE
LEFT JOIN users st_user ON st.teacher_id = st_user.id
WHERE sa.id IS NOT NULL
GROUP BY s.id, sub.id, s.academic_session
ORDER BY s.student_id, sub.code;

-- =====================================================================================
-- SECTION 4: VERIFICATION AND SAMPLE QUERIES
-- =====================================================================================

-- Test the complete student record system
SELECT 'STUDENT COMPLETE RECORD SYSTEM - VERIFICATION:' AS info;

-- Sample query: Get complete record for a specific student
SELECT 'COMPLETE RECORD FOR STUDENT (SAMPLE):' AS info;
SELECT
    roll_number,
    student_name,
    class_level,
    trade_full_name,
    class_incharge_name,
    subject_name,
    subject_teacher_name,
    exam_name,
    total_marks,
    percentage,
    grade,
    result_status
FROM v_student_complete_record
WHERE student_id = 1 AND academic_session = '2023-2024'
ORDER BY exam_date, subject_code
LIMIT 10;

-- Sample query: Get attendance summary for a student
SELECT 'STUDENT ATTENDANCE SUMMARY (SAMPLE):' AS info;
SELECT
    roll_number,
    student_name,
    subject_name,
    subject_teacher_name,
    total_attendance_days,
    days_present,
    days_absent,
    attendance_percentage,
    attendance_status
FROM v_student_attendance_summary
WHERE student_id = 1 AND academic_session = '2023-2024'
ORDER BY subject_code;

-- Verification queries
SELECT 'SYSTEM TABLES VERIFICATION:' AS info;
SELECT
    'subject_teachers' AS table_name,
    COUNT(*) AS record_count
FROM subject_teachers
UNION ALL
SELECT
    'class_incharge' AS table_name,
    COUNT(*) AS record_count
FROM class_incharge
UNION ALL
SELECT
    'student_attendance' AS table_name,
    COUNT(*) AS record_count
FROM student_attendance;

-- Show available views
SELECT 'AVAILABLE VIEWS FOR STUDENT RECORDS:' AS info;
SELECT
    'v_student_complete_record' AS view_name,
    'Complete student record with all exams, teachers, and marks' AS description
UNION ALL
SELECT
    'v_student_attendance_summary' AS view_name,
    'Subject-wise attendance summary with teacher information' AS description;
