<%- include('../partials/header') %>

<div class="principal-container">
    <!-- Page Header -->
    <div class="principal-header mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-principal-dark mb-2">
                    <i class="fas fa-chart-network mr-3 text-principal-primary"></i>
                    Student-Subject-Trade Analysis
                </h1>
                <p class="text-principal-silver">Comprehensive overview of student enrollment patterns across trades and subjects</p>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="exportAnalysis()" class="btn-principal-secondary">
                    <i class="fas fa-download mr-2"></i>
                    Export Report
                </button>
                <button onclick="refreshData()" class="btn-principal-primary">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh Data
                </button>
            </div>
        </div>
    </div>

    <!-- Trade Distribution Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div class="lg:col-span-2">
            <div class="principal-card">
                <div class="principal-card-header">
                    <h3 class="principal-card-title">
                        <i class="fas fa-users mr-2"></i>
                        Trade Distribution by Class
                    </h3>
                </div>
                <div class="principal-card-body">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-principal-light">
                                    <th class="text-left py-3 px-4 font-semibold text-principal-dark">Class</th>
                                    <th class="text-left py-3 px-4 font-semibold text-principal-dark">Trade Category</th>
                                    <th class="text-center py-3 px-4 font-semibold text-principal-dark">Students</th>
                                    <th class="text-left py-3 px-4 font-semibold text-principal-dark">Original Trades</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% tradeStats.forEach(stat => { %>
                                    <tr class="border-b border-gray-100 hover:bg-principal-light/20">
                                        <td class="py-3 px-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Class <%= stat.class %>
                                            </span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="font-medium text-principal-dark"><%= stat.trade_category %></span>
                                        </td>
                                        <td class="py-3 px-4 text-center">
                                            <span class="text-2xl font-bold text-principal-primary"><%= stat.student_count %></span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="flex flex-wrap gap-1">
                                                <% if (stat.original_trades) { %>
                                                    <% stat.original_trades.split(',').forEach(trade => { %>
                                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700">
                                                            <%= trade.trim() %>
                                                        </span>
                                                    <% }); %>
                                                <% } %>
                                            </div>
                                        </td>
                                    </tr>
                                <% }); %>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div>
            <div class="principal-card">
                <div class="principal-card-header">
                    <h3 class="principal-card-title">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Quick Stats
                    </h3>
                </div>
                <div class="principal-card-body space-y-4">
                    <% 
                        const totalStudents = tradeStats.reduce((sum, stat) => sum + stat.student_count, 0);
                        const uniqueTrades = [...new Set(tradeStats.map(stat => stat.trade_category))];
                        const classCount = [...new Set(tradeStats.map(stat => stat.class))].length;
                    %>
                    
                    <div class="text-center p-4 bg-principal-light/30 rounded-lg">
                        <div class="text-3xl font-bold text-principal-primary"><%= totalStudents %></div>
                        <div class="text-sm text-principal-silver">Total Students</div>
                    </div>
                    
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <div class="text-3xl font-bold text-blue-600"><%= uniqueTrades.length %></div>
                        <div class="text-sm text-gray-600">Trade Categories</div>
                    </div>
                    
                    <div class="text-center p-4 bg-green-50 rounded-lg">
                        <div class="text-3xl font-bold text-green-600"><%= classCount %></div>
                        <div class="text-sm text-gray-600">Active Classes</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Subject Enrollment Analysis -->
    <div class="principal-card mb-8">
        <div class="principal-card-header">
            <h3 class="principal-card-title">
                <i class="fas fa-book-open mr-2"></i>
                Subject Enrollment by Trade Category
            </h3>
        </div>
        <div class="principal-card-body">
            <div class="mb-4">
                <div class="flex flex-wrap gap-2">
                    <button onclick="filterByTrade('all')" class="trade-filter-btn active" data-trade="all">
                        All Trades
                    </button>
                    <% uniqueTrades.forEach(trade => { %>
                        <button onclick="filterByTrade('<%= trade %>')" class="trade-filter-btn" data-trade="<%= trade %>">
                            <%= trade %>
                        </button>
                    <% }); %>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full" id="subjectTable">
                    <thead>
                        <tr class="border-b border-principal-light">
                            <th class="text-left py-3 px-4 font-semibold text-principal-dark">Subject Code</th>
                            <th class="text-left py-3 px-4 font-semibold text-principal-dark">Subject Name</th>
                            <th class="text-left py-3 px-4 font-semibold text-principal-dark">Stream</th>
                            <th class="text-left py-3 px-4 font-semibold text-principal-dark">Category</th>
                            <th class="text-left py-3 px-4 font-semibold text-principal-dark">Trade</th>
                            <th class="text-center py-3 px-4 font-semibold text-principal-dark">Enrolled Students</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% subjectStats.forEach(subject => { %>
                            <tr class="border-b border-gray-100 hover:bg-principal-light/20 subject-row" data-trade="<%= subject.trade_category %>">
                                <td class="py-3 px-4">
                                    <span class="font-mono text-sm bg-gray-100 px-2 py-1 rounded"><%= subject.subject_code %></span>
                                </td>
                                <td class="py-3 px-4">
                                    <span class="font-medium text-principal-dark"><%= subject.subject_name %></span>
                                </td>
                                <td class="py-3 px-4">
                                    <span class="text-sm text-principal-silver"><%= subject.stream || 'N/A' %></span>
                                </td>
                                <td class="py-3 px-4">
                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-purple-100 text-purple-800">
                                        <%= subject.subject_category_new || 'General' %>
                                    </span>
                                </td>
                                <td class="py-3 px-4">
                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs 
                                        <% if (subject.trade_category === 'Medical') { %>bg-red-100 text-red-800<% } %>
                                        <% if (subject.trade_category === 'Non Medical') { %>bg-blue-100 text-blue-800<% } %>
                                        <% if (subject.trade_category === 'Commerce') { %>bg-green-100 text-green-800<% } %>
                                        <% if (subject.trade_category === 'Humanities') { %>bg-yellow-100 text-yellow-800<% } %>
                                        <% if (subject.trade_category === 'General') { %>bg-gray-100 text-gray-800<% } %>
                                    ">
                                        <%= subject.trade_category %>
                                    </span>
                                </td>
                                <td class="py-3 px-4 text-center">
                                    <span class="text-lg font-bold text-principal-primary"><%= subject.enrolled_students %></span>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Detailed Student-Subject Mapping -->
    <div class="principal-card">
        <div class="principal-card-header">
            <h3 class="principal-card-title">
                <i class="fas fa-table mr-2"></i>
                Detailed Student-Subject Mapping
            </h3>
            <div class="flex items-center space-x-2">
                <input type="text" id="searchStudents" placeholder="Search students..." 
                       class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                <select id="classFilter" class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                    <option value="">All Classes</option>
                    <option value="10">Class 10</option>
                    <option value="11">Class 11</option>
                    <option value="12">Class 12</option>
                </select>
            </div>
        </div>
        <div class="principal-card-body">
            <div class="overflow-x-auto max-h-96">
                <table class="w-full text-sm" id="studentMappingTable">
                    <thead class="sticky top-0 bg-white">
                        <tr class="border-b border-principal-light">
                            <th class="text-left py-2 px-3 font-semibold text-principal-dark">Student</th>
                            <th class="text-left py-2 px-3 font-semibold text-principal-dark">Class</th>
                            <th class="text-left py-2 px-3 font-semibold text-principal-dark">Trade</th>
                            <th class="text-left py-2 px-3 font-semibold text-principal-dark">Subject</th>
                            <th class="text-center py-2 px-3 font-semibold text-principal-dark">Marks</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% studentSubjectTrades.forEach(mapping => { %>
                            <% if (mapping.subject_name) { %>
                                <tr class="border-b border-gray-50 hover:bg-principal-light/10 student-mapping-row" 
                                    data-class="<%= mapping.class %>" 
                                    data-student="<%= mapping.student_name.toLowerCase() %>">
                                    <td class="py-2 px-3">
                                        <div>
                                            <div class="font-medium text-principal-dark"><%= mapping.student_name %></div>
                                            <div class="text-xs text-principal-silver">ID: <%= mapping.roll_number %></div>
                                        </div>
                                    </td>
                                    <td class="py-2 px-3">
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
                                            <%= mapping.class %>-<%= mapping.section %>
                                        </span>
                                    </td>
                                    <td class="py-2 px-3">
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs 
                                            <% if (mapping.trade_category === 'Medical') { %>bg-red-100 text-red-800<% } %>
                                            <% if (mapping.trade_category === 'Non Medical') { %>bg-blue-100 text-blue-800<% } %>
                                            <% if (mapping.trade_category === 'Commerce') { %>bg-green-100 text-green-800<% } %>
                                            <% if (mapping.trade_category === 'Humanities') { %>bg-yellow-100 text-yellow-800<% } %>
                                            <% if (mapping.trade_category === 'General') { %>bg-gray-100 text-gray-800<% } %>
                                        ">
                                            <%= mapping.trade_category %>
                                        </span>
                                    </td>
                                    <td class="py-2 px-3">
                                        <div>
                                            <div class="font-medium"><%= mapping.subject_name %></div>
                                            <div class="text-xs text-gray-500">Code: <%= mapping.subject_code %></div>
                                        </div>
                                    </td>
                                    <td class="py-2 px-3 text-center">
                                        <% if (mapping.total_marks !== null) { %>
                                            <span class="font-medium"><%= mapping.total_marks %>/<%= mapping.max_marks %></span>
                                        <% } else { %>
                                            <span class="text-gray-400">No marks</span>
                                        <% } %>
                                    </td>
                                </tr>
                            <% } %>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// Trade filtering functionality
function filterByTrade(trade) {
    const rows = document.querySelectorAll('.subject-row');
    const buttons = document.querySelectorAll('.trade-filter-btn');
    
    // Update button states
    buttons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.trade === trade) {
            btn.classList.add('active');
        }
    });
    
    // Filter rows
    rows.forEach(row => {
        if (trade === 'all' || row.dataset.trade === trade) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Search functionality
document.getElementById('searchStudents').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('.student-mapping-row');
    
    rows.forEach(row => {
        const studentName = row.dataset.student;
        if (studentName.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// Class filtering
document.getElementById('classFilter').addEventListener('change', function(e) {
    const selectedClass = e.target.value;
    const rows = document.querySelectorAll('.student-mapping-row');
    
    rows.forEach(row => {
        if (!selectedClass || row.dataset.class === selectedClass) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// Export functionality
function exportAnalysis() {
    // Implementation for exporting the analysis data
    alert('Export functionality will be implemented');
}

// Refresh functionality
function refreshData() {
    window.location.reload();
}
</script>

<style>
.trade-filter-btn {
    @apply px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors;
}

.trade-filter-btn.active {
    @apply bg-principal-primary text-white border-principal-primary;
}
</style>

<%- include('../partials/footer') %>
