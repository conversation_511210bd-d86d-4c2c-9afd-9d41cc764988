<!DOCTYPE html>
<html lang="<%= currentLanguage %>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= typeof title !== 'undefined' ? title : 'Admin Dashboard' %> - <%= __('app.name') %> <%= __('admin.dashboard') %></title>
  <link rel="stylesheet" href="/styles.css">
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="/js/toast-notifications.js"></script>
  <script src="/js/notification-events.js"></script>
  <script src="/js/notification-counter.js"></script>
  <script src="/js/admin-notifications.js"></script>
  <script src="/js/broadcast-notifications.js"></script>
  <script src="/js/confirmation-dialog.js"></script>
  <script src="/socket.io/socket.io.js"></script>
  <script src="/js/chat-icon.js"></script>
  <script src="/js/unified-chat-icon.js"></script>
  <script src="/js/websocket-client.js"></script>
  <link rel="stylesheet" href="/css/session-timer.css">
  <link rel="stylesheet" href="/css/site-title.css">
  <link rel="stylesheet" href="/css/spacing-utilities.css">
  <link rel="stylesheet" href="/css/responsive-tables.css">
  <link rel="stylesheet" href="/css/modal-styles.css">
  <link rel="stylesheet" href="/css/responsive-images.css">
  <link rel="stylesheet" href="/css/form-styles.css">
  <script src="/js/session-timer.js"></script>
  <script src="/js/global-fix.js"></script>
  <script src="/js/modal-manager.js"></script>
  <script src="/js/form-validator.js"></script>
  <link rel="stylesheet" href="/css/toast-notifications.css">
  <link rel="stylesheet" href="/css/chat-icon.css">
  <link rel="stylesheet" href="/css/unified-chat-icon.css">
  <!-- Chosen Library -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.min.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.jquery.min.js"></script>
  <%- style %>
  <!-- Additional CSS -->
  <style>
    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #e9d5ff;
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #a855f7;
    }
    /* Site logo styles */
    .site-logo {
      height: 36px;
      width: 36px;
      object-fit: cover;
      object-position: center;
      border-radius: 4px;
      flex-shrink: 0;
    }
  </style>
  <!-- Add KaTeX CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css" integrity="sha384-GvrOXuhMATgEsSwCs4smul74iXGOixntILdUW9XmUC6+HX0sLNAK3q71HotJqlAn" crossorigin="anonymous">
  <!-- Add KaTeX JS -->
  <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js" integrity="sha384-cpW21h6RZv/phavutF+AuVYrr+dA8xD9zs6FwLpaCct6O9ctzYFfFr4dgmgccOTx" crossorigin="anonymous"></script>
  <!-- Add auto-render extension for automatic rendering of math expressions -->
  <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>
  <style>
    /* Optional: Adjust math display styles */
    .katex { font-size: 1.1em; }
    .math-display { margin: 1em 0; }
  </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col <%= locals.userId ? 'logged-in' : '' %>" data-user-id="<%= locals.userId || '' %>">

  <!-- Admin Navbar -->
  <%- include('../partials/admin-navbar', {
    currentPage: locals.currentPage || '',
    user: locals.user || null,
    notificationCount: locals.notificationCount || 0
  }) %>

  <!-- Main Content -->
  <main class="flex-grow container mx-auto px-4 py-6">
    <% if (locals.pageTitle) { %>
      <h1 class="text-2xl font-bold text-gray-800 mb-6"><%= pageTitle %></h1>
    <% } %>

    <!-- Flash Messages -->
    <% if (locals.messages && messages.success && messages.success.length > 0) { %>
      <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <% messages.success.forEach(function(message) { %>
          <p><%= message %></p>
        <% }); %>
      </div>
    <% } %>

    <% if (locals.messages && messages.error && messages.error.length > 0) { %>
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <% messages.error.forEach(function(message) { %>
          <p><%= message %></p>
        <% }); %>
      </div>
    <% } %>

    <!-- Direct error message -->
    <% if (locals.error) { %>
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <p><%= error %></p>
      </div>
    <% } %>

    <%- body %>
  </main>

  <!-- Footer -->
  <footer class="bg-white border-t border-gray-200 py-4 mt-auto">
    <div class="container mx-auto px-4 text-center text-gray-600 text-sm">
      &copy; <%= new Date().getFullYear() %> Meritorious Exam Preparation Platform. All rights reserved.
    </div>
  </footer>

  <!-- Common Scripts -->
  <script>
    // Toast notifications are now initialized in the header
  </script>

  <!-- Device Testing Tool (only loads in development mode) -->
  <script src="/js/device-testing.js"></script>

  <%- script %>
  <script>
    document.addEventListener("DOMContentLoaded", function() {
      // Auto-render math expressions
      renderMathInElement(document.body, {
        delimiters: [
          {left: "$$", right: "$$", display: true},
          {left: "$", right: "$", display: false},
          {left: "\\(", right: "\\)", display: false},
          {left: "\\[", right: "\\]", display: true}
        ],
        throwOnError: false
      });
    });
  </script>


  <!-- Roles Management Modal -->
  <div id="roles-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
      <div class="px-6 py-4 border-b flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-900">Manage User Roles</h3>
        <button id="close-roles-modal" class="text-gray-400 hover:text-gray-500">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <div class="overflow-auto p-6 flex-grow" id="roles-modal-content">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-800">User Roles</h2>
          <button id="create-role-btn" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Create New Role
          </button>
        </div>

        <div id="roles-table-container" class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" id="roles-table-body">
              <!-- Roles will be loaded here via AJAX -->
              <tr>
                <td colspan="5" class="px-6 py-4 text-center text-gray-500">Loading roles...</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Create Role Modal -->
  <div id="create-role-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-[60] hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
      <div class="px-6 py-4 border-b flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-900">Create New Role</h3>
        <button id="close-create-role-modal" class="text-gray-400 hover:text-gray-500">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <form id="create-role-form">
        <div class="px-6 py-4">
          <div class="mb-4">
            <label for="role_name" class="block text-sm font-medium text-gray-700 mb-1">Role Name</label>
            <input type="text" id="role_name" name="role_name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div class="mb-4">
            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea id="description" name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
          </div>

          <div id="permissions-container" class="mb-4">
            <!-- Permissions will be loaded here via AJAX -->
          </div>
        </div>
        <div class="px-6 py-3 bg-gray-50 flex justify-end rounded-b-lg">
          <button type="button" id="cancel-create-role" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 mr-2">Cancel</button>
          <button type="submit" class="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700">Create Role</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Edit Role Modal -->
  <div id="edit-role-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-[60] hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
      <div class="px-6 py-4 border-b flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-900">Edit Role</h3>
        <button id="close-edit-role-modal" class="text-gray-400 hover:text-gray-500">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <form id="edit-role-form">
        <input type="hidden" id="edit_role_id" name="role_id">
        <div class="px-6 py-4">
          <div class="mb-4">
            <label for="edit_role_name" class="block text-sm font-medium text-gray-700 mb-1">Role Name</label>
            <input type="text" id="edit_role_name" name="role_name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div class="mb-4">
            <label for="edit_description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea id="edit_description" name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
          </div>

          <div id="edit-permissions-container" class="mb-4">
            <!-- Permissions will be loaded here via AJAX -->
          </div>
        </div>
        <div class="px-6 py-3 bg-gray-50 flex justify-end rounded-b-lg">
          <button type="button" id="cancel-edit-role" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 mr-2">Cancel</button>
          <button type="submit" class="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700">Update Role</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Delete Role Confirmation Modal -->
  <div id="delete-role-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-[60] hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
      <div class="px-6 py-4 border-b">
        <h3 class="text-lg font-semibold text-gray-900">Delete Role</h3>
      </div>
      <div class="px-6 py-4">
        <p class="text-gray-700">Are you sure you want to delete the role <span id="delete-role-name" class="font-semibold"></span>?</p>
        <p class="text-gray-500 mt-2">This action cannot be undone. Users with this role will need to be reassigned.</p>
      </div>
      <div class="px-6 py-3 bg-gray-50 flex justify-end rounded-b-lg">
        <button type="button" id="cancel-delete-role" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 mr-2">Cancel</button>
        <button type="button" id="confirm-delete-role" class="px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700">Delete</button>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Role Management Modal
      const rolesModal = document.getElementById('roles-modal');
      const closeRolesModal = document.getElementById('close-roles-modal');
      const rolesTableBody = document.getElementById('roles-table-body');

      // Create Role Modal
      const createRoleModal = document.getElementById('create-role-modal');
      const createRoleBtn = document.getElementById('create-role-btn');
      const closeCreateRoleModal = document.getElementById('close-create-role-modal');
      const cancelCreateRole = document.getElementById('cancel-create-role');
      const createRoleForm = document.getElementById('create-role-form');
      const permissionsContainer = document.getElementById('permissions-container');

      // Edit Role Modal
      const editRoleModal = document.getElementById('edit-role-modal');
      const closeEditRoleModal = document.getElementById('close-edit-role-modal');
      const cancelEditRole = document.getElementById('cancel-edit-role');
      const editRoleForm = document.getElementById('edit-role-form');
      const editPermissionsContainer = document.getElementById('edit-permissions-container');

      // Delete Role Modal
      const deleteRoleModal = document.getElementById('delete-role-modal');
      const cancelDeleteRole = document.getElementById('cancel-delete-role');
      const confirmDeleteRole = document.getElementById('confirm-delete-role');
      const deleteRoleName = document.getElementById('delete-role-name');
      let deleteRoleId = null;

      // Open Roles Modal - Now handled in the users page script
      function openRolesModal() {
        if (rolesModal) {
          rolesModal.classList.remove('hidden');
          loadRoles();
        }
      }

      // Close Roles Modal
      function closeRolesModalFn() {
        rolesModal.classList.add('hidden');
      }

      // Load Roles via AJAX
      function loadRoles() {
        fetch('/admin/api/roles')
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              renderRolesTable(data.roles);
            } else {
              rolesTableBody.innerHTML = `<tr><td colspan="5" class="px-6 py-4 text-center text-red-500">${data.message || 'Failed to load roles'}</td></tr>`;
            }
          })
          .catch(error => {
            console.error('Error loading roles:', error);
            rolesTableBody.innerHTML = '<tr><td colspan="5" class="px-6 py-4 text-center text-red-500">Error loading roles. Please try again.</td></tr>';
          });
      }

      // Render Roles Table
      function renderRolesTable(roles) {
        if (!roles || roles.length === 0) {
          rolesTableBody.innerHTML = '<tr><td colspan="5" class="px-6 py-4 text-center text-gray-500">No roles found. Create your first role.</td></tr>';
          return;
        }

        let html = '';
        roles.forEach(role => {
          html += `
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">
                  ${getRoleBadge(role.role_name)}
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-500">${role.description || 'No description'}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">${role.user_count}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${role.is_system ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}">
                  ${role.is_system ? 'System' : 'Custom'}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button class="view-users-btn text-blue-600 hover:text-blue-900 mr-3" data-role-id="${role.role_id}">View Users</button>
                ${!role.is_system ? `
                  <button class="edit-role-btn text-indigo-600 hover:text-indigo-900 mr-3" data-role-id="${role.role_id}" data-role-name="${role.role_name}" data-description="${role.description || ''}">Edit</button>
                  <button class="delete-role-btn text-red-600 hover:text-red-900" data-role-id="${role.role_id}" data-role-name="${role.role_name}">Delete</button>
                ` : ''}
              </td>
            </tr>
          `;
        });

        rolesTableBody.innerHTML = html;

        // Add event listeners to the buttons
        document.querySelectorAll('.edit-role-btn').forEach(btn => {
          btn.addEventListener('click', function() {
            const roleId = this.getAttribute('data-role-id');
            const roleName = this.getAttribute('data-role-name');
            const description = this.getAttribute('data-description');
            openEditRoleModal(roleId, roleName, description);
          });
        });

        document.querySelectorAll('.delete-role-btn').forEach(btn => {
          btn.addEventListener('click', function() {
            const roleId = this.getAttribute('data-role-id');
            const roleName = this.getAttribute('data-role-name');
            openDeleteRoleModal(roleId, roleName);
          });
        });

        document.querySelectorAll('.view-users-btn').forEach(btn => {
          btn.addEventListener('click', function() {
            const roleId = this.getAttribute('data-role-id');
            // Implement view users functionality
            alert('View users for role ID: ' + roleId + ' - This feature is coming soon!');
          });
        });
      }

      // Helper function to get role badge HTML
      function getRoleBadge(roleName) {
        let badgeClass = '';

        if (roleName === 'admin') {
          badgeClass = 'bg-purple-100 text-purple-800';
        } else if (roleName === 'teacher') {
          badgeClass = 'bg-green-100 text-green-800';
        } else if (roleName === 'student') {
          badgeClass = 'bg-blue-100 text-blue-800';
        } else {
          badgeClass = 'bg-gray-100 text-gray-800';
        }

        return `<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${badgeClass}">${roleName}</span>`;
      }

      // Open Create Role Modal
      function openCreateRoleModal() {
        createRoleModal.classList.remove('hidden');
        loadPermissions(permissionsContainer);
      }

      // Close Create Role Modal
      function closeCreateRoleModalFn() {
        createRoleModal.classList.add('hidden');
        createRoleForm.reset();
      }

      // Open Edit Role Modal
      function openEditRoleModal(roleId, roleName, description) {
        document.getElementById('edit_role_id').value = roleId;
        document.getElementById('edit_role_name').value = roleName;
        document.getElementById('edit_description').value = description;

        editRoleModal.classList.remove('hidden');
        loadRolePermissions(roleId, editPermissionsContainer);
      }

      // Close Edit Role Modal
      function closeEditRoleModalFn() {
        editRoleModal.classList.add('hidden');
        editRoleForm.reset();
      }

      // Open Delete Role Modal
      function openDeleteRoleModal(roleId, roleName) {
        deleteRoleId = roleId;
        deleteRoleName.textContent = roleName;
        deleteRoleModal.classList.remove('hidden');
      }

      // Close Delete Role Modal
      function closeDeleteRoleModalFn() {
        deleteRoleModal.classList.add('hidden');
        deleteRoleId = null;
      }

      // Load Permissions
      function loadPermissions(container) {
        fetch('/admin/api/permissions')
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              renderPermissions(data.permissions, container);
            } else {
              container.innerHTML = `<p class="text-red-500">${data.message || 'Failed to load permissions'}</p>`;
            }
          })
          .catch(error => {
            console.error('Error loading permissions:', error);
            container.innerHTML = '<p class="text-red-500">Error loading permissions. Please try again.</p>';
          });
      }

      // Load Role Permissions
      function loadRolePermissions(roleId, container) {
        fetch(`/admin/api/roles/${roleId}/permissions`)
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              renderPermissions(data.permissions, container, data.rolePermissions);
            } else {
              container.innerHTML = `<p class="text-red-500">${data.message || 'Failed to load role permissions'}</p>`;
            }
          })
          .catch(error => {
            console.error('Error loading role permissions:', error);
            container.innerHTML = '<p class="text-red-500">Error loading role permissions. Please try again.</p>';
          });
      }

      // Render Permissions
      function renderPermissions(permissions, container, selectedPermissions = []) {
        if (!permissions || Object.keys(permissions).length === 0) {
          container.innerHTML = '<p class="text-gray-500">No permissions available.</p>';
          return;
        }

        let html = '<h4 class="text-sm font-medium text-gray-700 mb-2">Permissions</h4>';

        Object.keys(permissions).forEach(category => {
          html += `
            <div class="mb-3">
              <h5 class="text-xs font-semibold text-gray-600 uppercase mb-1">${category}</h5>
              <div class="grid grid-cols-2 gap-2">
          `;

          permissions[category].forEach(permission => {
            const isChecked = selectedPermissions.includes(permission.permission_id) ? 'checked' : '';
            html += `
              <div class="flex items-center">
                <input type="checkbox" id="permission_${permission.permission_id}" name="permissions[]" value="${permission.permission_id}" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" ${isChecked}>
                <label for="permission_${permission.permission_id}" class="ml-2 block text-sm text-gray-700">${permission.permission_name}</label>
              </div>
            `;
          });

          html += `
              </div>
            </div>
          `;
        });

        container.innerHTML = html;
      }

      // Event listeners for the roles modal are now handled in the users page script

      if (closeRolesModal) {
        closeRolesModal.addEventListener('click', closeRolesModalFn);
      }

      if (createRoleBtn) {
        createRoleBtn.addEventListener('click', openCreateRoleModal);
      }

      if (closeCreateRoleModal) {
        closeCreateRoleModal.addEventListener('click', closeCreateRoleModalFn);
      }

      if (cancelCreateRole) {
        cancelCreateRole.addEventListener('click', closeCreateRoleModalFn);
      }

      if (closeEditRoleModal) {
        closeEditRoleModal.addEventListener('click', closeEditRoleModalFn);
      }

      if (cancelEditRole) {
        cancelEditRole.addEventListener('click', closeEditRoleModalFn);
      }

      if (cancelDeleteRole) {
        cancelDeleteRole.addEventListener('click', closeDeleteRoleModalFn);
      }

      // Create Role Form Submit
      if (createRoleForm) {
        createRoleForm.addEventListener('submit', function(e) {
          e.preventDefault();
          const formData = new FormData(this);
          const data = {};

          formData.forEach((value, key) => {
            if (key === 'permissions[]') {
              if (!data.permissions) data.permissions = [];
              data.permissions.push(value);
            } else {
              data[key] = value;
            }
          });

          fetch('/admin/api/roles/create', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
          })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                closeCreateRoleModalFn();
                loadRoles();
                ToastNotifications.success('Role created successfully');
              } else {
                ToastNotifications.error(data.message || 'Failed to create role');
              }
            })
            .catch(error => {
              console.error('Error creating role:', error);
              ToastNotifications.error('Error creating role. Please try again.');
            });
        });
      }

      // Edit Role Form Submit
      if (editRoleForm) {
        editRoleForm.addEventListener('submit', function(e) {
          e.preventDefault();
          const formData = new FormData(this);
          const data = {};
          const roleId = formData.get('role_id');

          formData.forEach((value, key) => {
            if (key === 'permissions[]') {
              if (!data.permissions) data.permissions = [];
              data.permissions.push(value);
            } else {
              data[key] = value;
            }
          });

          fetch(`/admin/api/roles/${roleId}/update`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
          })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                closeEditRoleModalFn();
                loadRoles();
                ToastNotifications.success('Role updated successfully');
              } else {
                ToastNotifications.error(data.message || 'Failed to update role');
              }
            })
            .catch(error => {
              console.error('Error updating role:', error);
              ToastNotifications.error('Error updating role. Please try again.');
            });
        });
      }

      // Delete Role Confirmation
      if (confirmDeleteRole) {
        confirmDeleteRole.addEventListener('click', function() {
          if (!deleteRoleId) return;

          fetch(`/admin/api/roles/${deleteRoleId}/delete`, {
            method: 'POST',
          })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                closeDeleteRoleModalFn();
                loadRoles();
                ToastNotifications.success('Role deleted successfully');
              } else {
                ToastNotifications.error(data.message || 'Failed to delete role');
              }
            })
            .catch(error => {
              console.error('Error deleting role:', error);
              ToastNotifications.error('Error deleting role. Please try again.');
            });
        });
      }

      // Close modals when clicking outside
      window.addEventListener('click', function(event) {
        if (event.target === rolesModal) {
          closeRolesModalFn();
        }
        if (event.target === createRoleModal) {
          closeCreateRoleModalFn();
        }
        if (event.target === editRoleModal) {
          closeEditRoleModalFn();
        }
        if (event.target === deleteRoleModal) {
          closeDeleteRoleModalFn();
        }
      });
    });
  </script>
</body>
</html>
