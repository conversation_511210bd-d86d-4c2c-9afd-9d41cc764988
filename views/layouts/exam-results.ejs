<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - Exam Results System</title>

    <!-- Tailwind CSS - Production Build -->
    <link rel="stylesheet" href="/styles.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Toast Notifications -->
    <script src="/js/toast-notifications.js"></script>

    <!-- Apache ECharts for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

    <!-- Exam Results Custom CSS -->
    <style>
        :root {
            /* Navy/Gold Theme Colors - Matching Principal Dashboard */
            --exam-primary: #1e3a8a;      /* Navy Blue */
            --exam-secondary: #1e40af;    /* Deep Blue */
            --exam-accent: #f59e0b;       /* Gold/Amber */
            --exam-success: #10b981;      /* Green */
            --exam-warning: #f59e0b;      /* Amber */
            --exam-danger: #ef4444;       /* Red */
            --exam-light: #eff6ff;        /* Light Navy */
            --exam-dark: #1e293b;         /* Dark Blue Gray */
            --exam-silver: #64748b;       /* Slate */
            --exam-gold: #f59e0b;         /* Gold */
        }

        body {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        /* Header Styling */
        .exam-header {
            background: linear-gradient(135deg, var(--exam-primary) 0%, var(--exam-secondary) 100%);
            box-shadow: 0 10px 25px rgba(30, 64, 175, 0.15);
        }

        .exam-badge {
            background: linear-gradient(135deg, var(--exam-primary) 0%, var(--exam-accent) 100%);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Sidebar Styling */
        .exam-sidebar {
            background: white;
            border-right: 3px solid var(--exam-light);
        }

        .sidebar-link {
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 2px 0;
        }

        .sidebar-link:hover {
            background: var(--exam-light);
            color: var(--exam-primary);
            transform: translateX(4px);
        }

        .sidebar-link.active {
            background: linear-gradient(135deg, var(--exam-primary) 0%, var(--exam-secondary) 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
        }

        /* Card Styling */
        .exam-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(30, 64, 175, 0.08);
            border: 1px solid rgba(30, 64, 175, 0.1);
            transition: all 0.3s ease;
        }

        .exam-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(30, 64, 175, 0.15);
        }

        /* Button Styling */
        .btn-exam-primary {
            background: linear-gradient(135deg, var(--exam-primary) 0%, var(--exam-secondary) 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-exam-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
        }

        .btn-exam-secondary {
            background: linear-gradient(135deg, var(--exam-accent) 0%, var(--exam-gold) 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-exam-secondary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(14, 165, 233, 0.3);
        }

        /* Table Styling */
        .exam-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(30, 64, 175, 0.08);
        }

        .exam-table th {
            background: linear-gradient(135deg, var(--exam-primary) 0%, var(--exam-secondary) 100%);
            color: white;
            font-weight: 600;
            padding: 12px;
            text-align: center;
        }

        .exam-table td {
            padding: 10px;
            border-bottom: 1px solid var(--exam-light);
            text-align: center;
        }

        .exam-table tbody tr:hover {
            background: var(--exam-light);
        }

        /* Tab Styling */
        .exam-tabs {
            border-bottom: 2px solid var(--exam-light);
        }

        .exam-tab {
            padding: 12px 24px;
            border-radius: 8px 8px 0 0;
            background: transparent;
            color: var(--exam-silver);
            border: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .exam-tab.active {
            background: linear-gradient(135deg, var(--exam-primary) 0%, var(--exam-secondary) 100%);
            color: white;
            box-shadow: 0 -2px 10px rgba(30, 64, 175, 0.2);
        }

        .exam-tab:hover:not(.active) {
            background: var(--exam-light);
            color: var(--exam-primary);
        }

        /* Stats Cards */
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(30, 64, 175, 0.08);
            border-left: 4px solid var(--exam-primary);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(30, 64, 175, 0.15);
        }

        .stats-icon {
            background: var(--exam-light);
            color: var(--exam-primary);
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        /* Grade Badges */
        .grade-a-plus { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; }
        .grade-a { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; }
        .grade-b-plus { background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); color: white; }
        .grade-b { background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); color: white; }
        .grade-c-plus { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; }
        .grade-c { background: linear-gradient(135deg, #f97316 0%, #ea580c 100%); color: white; }
        .grade-d { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; }
        .grade-f { background: linear-gradient(135deg, #991b1b 0%, #7f1d1d 100%); color: white; }

        .grade-badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            min-width: 30px;
        }

        /* Loading Animation */
        .loading-spinner {
            border: 3px solid var(--exam-light);
            border-top: 3px solid var(--exam-primary);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .exam-sidebar {
                transform: translateX(-100%);
                position: fixed;
                z-index: 1000;
                height: 100vh;
                width: 250px;
                transition: transform 0.3s ease;
            }

            .exam-sidebar.open {
                transform: translateX(0);
            }

            .mobile-menu-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
                display: none;
            }

            .mobile-menu-overlay.open {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="exam-header text-white p-4 shadow-lg">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <button class="md:hidden mr-4 text-white hover:text-blue-200" onclick="toggleMobileMenu()">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <div class="flex items-center">
                    <div class="stats-icon mr-3">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold">Exam Results Management System</h1>
                        <p class="text-blue-100 text-sm">
                            Academic Session <%= (typeof examResultsSession !== 'undefined' && examResultsSession) ? examResultsSession : '2023-2024' %>
                        </p>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Session Selector -->
                <div class="relative">
                    <select id="sessionSelector" class="bg-white/20 text-white border border-white/30 rounded-lg px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-white/50 cursor-pointer">
                        <%
                        const currentSession = (typeof examResultsSession !== 'undefined' && examResultsSession) ? examResultsSession : '2023-2024';
                        %>
                        <option value="2023-2024" <%= (currentSession === '2023-2024') ? 'selected' : '' %>>2023-2024</option>
                        <option value="2022-2023" <%= (currentSession === '2022-2023') ? 'selected' : '' %>>2022-2023</option>
                        <option value="2021-2022" <%= (currentSession === '2021-2022') ? 'selected' : '' %>>2021-2022</option>
                        <option value="2020-2021" <%= (currentSession === '2020-2021') ? 'selected' : '' %>>2020-2021</option>
                    </select>
                </div>

                <div class="exam-badge px-3 py-1 rounded-full text-sm">
                    <i class="fas fa-user-graduate mr-2"></i>
                    <%= user || 'Results Coordinator' %>
                </div>
                <form action="/exam-results/logout" method="POST" class="inline">
                    <button type="submit" class="text-blue-100 hover:text-white transition-colors">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </form>
            </div>
        </div>
    </header>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-menu-overlay" onclick="toggleMobileMenu()"></div>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="exam-sidebar w-64 min-h-screen shadow-lg">
            <div class="p-6">
                <nav class="space-y-2">
                    <a href="/exam-results/dashboard" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg <%= currentPage === 'dashboard' ? 'active' : '' %>">
                        <i class="fas fa-tachometer-alt mr-3 text-lg"></i>
                        Dashboard
                    </a>

                    <!-- Main Features -->
                    <div class="mt-6 mb-3">
                        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider px-4">Main Features</h3>
                    </div>

                    <a href="/exam-results/students" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg <%= currentPage === 'students' ? 'active' : '' %>">
                        <i class="fas fa-users mr-3 text-lg text-blue-600"></i>
                        <div>
                            <div class="font-medium">Student Results</div>
                            <div class="text-xs text-gray-500">Complete academic records</div>
                        </div>
                    </a>

                    <a href="/exam-results/analysis" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg <%= currentPage === 'analysis' ? 'active' : '' %>">
                        <i class="fas fa-chart-bar mr-3 text-lg text-green-600"></i>
                        <div>
                            <div class="font-medium">Performance Analysis</div>
                            <div class="text-xs text-gray-500">Detailed analytics & insights</div>
                        </div>
                    </a>

                    <a href="/exam-results/teacher-analysis" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg <%= currentPage === 'teacher-analysis' ? 'active' : '' %>">
                        <i class="fas fa-chalkboard-teacher mr-3 text-lg text-purple-600"></i>
                        <div>
                            <div class="font-medium">Teacher Analysis</div>
                            <div class="text-xs text-gray-500">Subject & class performance</div>
                        </div>
                    </a>

                    <a href="/exam-results/score-cards" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg <%= currentPage === 'score-cards' ? 'active' : '' %>">
                        <i class="fas fa-file-pdf mr-3 text-lg text-red-600"></i>
                        <div>
                            <div class="font-medium">Score Cards</div>
                            <div class="text-xs text-gray-500">PDF & Excel exports</div>
                        </div>
                    </a>

                    <a href="/exam-results/score-distribution" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg <%= currentPage === 'score-distribution' ? 'active' : '' %>">
                        <i class="fas fa-chart-pie mr-3 text-lg text-purple-600"></i>
                        <div>
                            <div class="font-medium">Distribution Analysis</div>
                            <div class="text-xs text-gray-500">Score range breakdowns</div>
                        </div>
                    </a>

                    <!-- Quick Actions -->
                    <div class="border-t border-gray-200 my-4"></div>
                    <div class="mb-3">
                        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider px-4">Quick Actions</h3>
                    </div>

                    <a href="/exam-results/export/excel" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg">
                        <i class="fas fa-file-excel mr-3 text-lg text-green-600"></i>
                        Export to Excel
                    </a>

                    <a href="/exam-results/bulk-scorecards" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg">
                        <i class="fas fa-file-pdf mr-3 text-lg text-red-600"></i>
                        Bulk Score Cards
                    </a>
                </nav>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <!-- Page Header -->
            <div class="mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-800"><%= title %></h1>
                        <p class="text-gray-600 mt-1 flex items-center">
                            <i class="fas fa-chart-line text-blue-600 mr-2"></i>
                            Comprehensive exam results and academic performance analysis
                        </p>
                    </div>
                    <div class="exam-badge px-4 py-2 rounded-full">
                        <i class="fas fa-graduation-cap mr-2"></i>
                        <span class="font-bold">RESULTS SYSTEM</span>
                    </div>
                </div>
            </div>

            <!-- Flash Messages -->
            <% if (typeof success !== 'undefined' && success) { %>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <i class="fas fa-check-circle mr-2"></i>
                    <%= success %>
                </div>
            <% } %>

            <% if (typeof error !== 'undefined' && error) { %>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <%= error %>
                </div>
            <% } %>

            <!-- Page Content -->
            <%- body %>
        </main>
    </div>

    <!-- External JavaScript Files -->
    <script src="/js/exam-results.js"></script>
    <% if (currentPage === 'score-distribution') { %>
    <script src="/js/score-distribution.js"></script>
    <% } %>

    <!-- Page-specific initialization -->
    <script>
        $(document).ready(function() {
            <% if (currentPage === 'students') { %>
                console.log('Initializing students page...');
                console.log('ExamResults available:', typeof ExamResults !== 'undefined');
                console.log('Student data available:', typeof window.studentResultsData !== 'undefined');
                console.log('Student data is array:', Array.isArray(window.studentResultsData));
                console.log('Student data length:', window.studentResultsData ? window.studentResultsData.length : 'undefined');

                // Initialize students page functionality immediately
                if (typeof ExamResults !== 'undefined' && ExamResults.initializeStudentsPage) {
                    ExamResults.initializeStudentsPage();
                } else {
                    console.error('ExamResults or initializeStudentsPage not available');
                }
            <% } %>
        });
    </script>
</body>
</html>
