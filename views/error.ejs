<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= typeof title !== 'undefined' ? title : 'Error' %> - Meritorious EP</title>
    <link rel="stylesheet" href="/styles.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="/css/session-timer.css">
    <script src="/js/session-timer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-lg max-w-lg w-full p-6 text-center">
        <div class="flex justify-center mb-6">
            <div class="bg-red-100 p-3 rounded-full">
                <svg class="w-16 h-16 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
            </div>
        </div>

        <h1 class="text-3xl font-bold text-gray-800 mb-2">Error <%= typeof error !== 'undefined' && error.status ? error.status : '500' %></h1>
        <p class="text-gray-600 mb-6"><%= typeof message !== 'undefined' ? message : 'An error occurred' %></p>

        <% if (typeof error !== 'undefined' && error.status === 500 && typeof message !== 'undefined' && message.includes("doesn't exist")) { %>
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 text-left">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700">
                            It looks like your database is missing some tables. Run the following command to fix it:
                        </p>
                        <p class="mt-2 text-sm bg-gray-800 text-white p-2 rounded font-mono">
                            npm run fix-db
                        </p>
                    </div>
                </div>
            </div>
        <% } %>

        <div class="flex justify-center space-x-4">
            <a href="/" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-6 rounded-md transition">
                Go Home
            </a>
            <button onclick="history.back()" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-6 rounded-md transition">
                Go Back
            </button>
        </div>

        <% if (process.env.NODE_ENV === 'development' && typeof error !== 'undefined' && error.stack) { %>
            <div class="mt-8 p-4 bg-gray-100 rounded-md text-left">
                <h3 class="text-sm font-semibold text-gray-700 mb-2">Stack Trace:</h3>
                <pre class="text-xs text-gray-600 overflow-x-auto"><%= error.stack %></pre>
            </div>
        <% } %>
    </div>
</body>
</html>