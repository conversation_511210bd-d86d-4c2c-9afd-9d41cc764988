<!-- Session Selection Card -->
<div class="min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full">
        <div class="exam-card p-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="stats-icon mx-auto mb-4">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-800 mb-2">Select Academic Session</h1>
                <p class="text-gray-600">Choose the academic session to view exam results</p>
            </div>

            <!-- Session Selection Form -->
            <form action="/exam-results/session-selection" method="POST" class="space-y-6">
                <div>
                    <label for="academic_session" class="block text-sm font-medium text-gray-700 mb-2">
                        Academic Session
                    </label>
                    <select 
                        id="academic_session" 
                        name="academic_session" 
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        required
                    >
                        <option value="">Select Academic Session</option>
                        <option value="2023-2024">2023-2024</option>
                        <option value="2022-2023">2022-2023</option>
                        <option value="2021-2022">2021-2022</option>
                        <option value="2020-2021">2020-2021</option>
                    </select>
                </div>

                <button type="submit" class="btn-exam-primary w-full py-3 text-lg font-semibold">
                    <i class="fas fa-arrow-right mr-2"></i>
                    Continue to Dashboard
                </button>
            </form>

            <!-- Session Information -->
            <div class="mt-8 p-4 bg-blue-50 rounded-lg">
                <h3 class="text-sm font-semibold text-blue-800 mb-2 flex items-center">
                    <i class="fas fa-info-circle mr-2"></i>
                    Session Information
                </h3>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li>• Each session contains complete academic records</li>
                    <li>• Student results, grades, and performance data</li>
                    <li>• Trade-wise and class-wise analysis</li>
                    <li>• Comprehensive score distribution reports</li>
                </ul>
            </div>

            <!-- Available Sessions -->
            <div class="mt-6">
                <h3 class="text-sm font-semibold text-gray-700 mb-3">Available Sessions:</h3>
                <div class="grid grid-cols-2 gap-3">
                    <div class="p-3 bg-green-50 border border-green-200 rounded-lg text-center">
                        <div class="text-sm font-medium text-green-800">2023-2024</div>
                        <div class="text-xs text-green-600">Current Session</div>
                    </div>
                    <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg text-center">
                        <div class="text-sm font-medium text-gray-700">2022-2023</div>
                        <div class="text-xs text-gray-500">Previous Session</div>
                    </div>
                    <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg text-center">
                        <div class="text-sm font-medium text-gray-700">2021-2022</div>
                        <div class="text-xs text-gray-500">Archive</div>
                    </div>
                    <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg text-center">
                        <div class="text-sm font-medium text-gray-700">2020-2021</div>
                        <div class="text-xs text-gray-500">Archive</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="flex justify-between items-center">
                    <a href="/exam-results/login" class="text-sm text-gray-500 hover:text-gray-700 flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Login
                    </a>
                    <div class="text-xs text-gray-400">
                        Logged in as: <%= user %>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Auto-load session data -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const sessionSelect = document.getElementById('academic_session');
    
    // Auto-select current session
    sessionSelect.value = '2023-2024';
    
    // Add session change handler
    sessionSelect.addEventListener('change', function() {
        const selectedSession = this.value;
        if (selectedSession) {
            // Update session cards to highlight selected
            document.querySelectorAll('.grid > div').forEach(card => {
                card.classList.remove('bg-blue-50', 'border-blue-200');
                card.classList.add('bg-gray-50', 'border-gray-200');
                
                const sessionText = card.querySelector('.text-sm').textContent;
                if (sessionText === selectedSession) {
                    card.classList.remove('bg-gray-50', 'border-gray-200');
                    card.classList.add('bg-blue-50', 'border-blue-200');
                }
            });
        }
    });
    
    // Trigger change event to highlight current selection
    sessionSelect.dispatchEvent(new Event('change'));
});
</script>
