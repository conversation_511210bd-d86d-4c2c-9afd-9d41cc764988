<!-- Enhanced Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">Total Students</p>
                <p class="text-3xl font-bold text-gray-800"><%= stats.total_students || 0 %></p>
                <p class="text-green-600 text-sm mt-1">
                    <i class="fas fa-users mr-1"></i>
                    Active Records
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-graduation-cap"></i>
            </div>
        </div>
    </div>

    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">Pass Rate</p>
                <p class="text-3xl font-bold text-gray-800">
                    <% if (stats.pass_count && stats.total_students) { %>
                        <%= ((stats.pass_count / (stats.pass_count + stats.fail_count)) * 100).toFixed(1) %>%
                    <% } else { %>
                        0.0%
                    <% } %>
                </p>
                <p class="text-green-600 text-sm mt-1">
                    <i class="fas fa-check-circle mr-1"></i>
                    <%= stats.pass_count || 0 %> Passed
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-trophy"></i>
            </div>
        </div>
    </div>

    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">A+ Students</p>
                <p class="text-3xl font-bold text-gray-800"><%= stats.a_plus_students || 0 %></p>
                <p class="text-purple-600 text-sm mt-1">
                    <i class="fas fa-star mr-1"></i>
                    Excellence
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-medal"></i>
            </div>
        </div>
    </div>

    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">Overall Average</p>
                <p class="text-3xl font-bold text-gray-800"><%= stats.overall_average ? parseFloat(stats.overall_average).toFixed(1) : '0.0' %>%</p>
                <p class="text-emerald-600 text-sm mt-1">
                    <i class="fas fa-chart-line mr-1"></i>
                    Performance
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-percentage"></i>
            </div>
        </div>
    </div>
</div>

<!-- Performance Range Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="exam-card p-6 border-l-4 border-green-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">Highest Score</p>
                <p class="text-2xl font-bold text-green-600"><%= stats.highest_percentage ? parseFloat(stats.highest_percentage).toFixed(1) : '0.0' %>%</p>
                <p class="text-gray-500 text-sm mt-1">Peak Performance</p>
            </div>
            <div class="text-green-500">
                <i class="fas fa-arrow-up text-2xl"></i>
            </div>
        </div>
    </div>

    <div class="exam-card p-6 border-l-4 border-blue-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">Average Score</p>
                <p class="text-2xl font-bold text-blue-600"><%= stats.overall_average ? parseFloat(stats.overall_average).toFixed(1) : '0.0' %>%</p>
                <p class="text-gray-500 text-sm mt-1">Class Performance</p>
            </div>
            <div class="text-blue-500">
                <i class="fas fa-equals text-2xl"></i>
            </div>
        </div>
    </div>

    <div class="exam-card p-6 border-l-4 border-orange-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">Lowest Score</p>
                <p class="text-2xl font-bold text-orange-600"><%= stats.lowest_percentage ? parseFloat(stats.lowest_percentage).toFixed(1) : '0.0' %>%</p>
                <p class="text-gray-500 text-sm mt-1">Needs Attention</p>
            </div>
            <div class="text-orange-500">
                <i class="fas fa-arrow-down text-2xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="exam-card p-6 mb-8">
    <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
        <i class="fas fa-bolt text-blue-600 mr-2"></i>
        Quick Actions
    </h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <a href="/exam-results/students" class="btn-exam-primary text-center py-3 rounded-lg hover:shadow-lg transition-all">
            <i class="fas fa-users mb-2 block text-lg"></i>
            View All Results
        </a>
        <a href="/exam-results/analysis" class="btn-exam-secondary text-center py-3 rounded-lg hover:shadow-lg transition-all">
            <i class="fas fa-chart-bar mb-2 block text-lg"></i>
            Performance Analysis
        </a>
        <a href="/exam-results/score-distribution" class="btn-exam-primary text-center py-3 rounded-lg hover:shadow-lg transition-all">
            <i class="fas fa-chart-pie mb-2 block text-lg"></i>
            Score Distribution
        </a>
        <a href="/exam-results/export/excel" class="btn-exam-secondary text-center py-3 rounded-lg hover:shadow-lg transition-all">
            <i class="fas fa-file-excel mb-2 block text-lg"></i>
            Export Data
        </a>
    </div>
</div>

<!-- Trade-wise Performance -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <div class="exam-card p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-chart-bar text-green-600 mr-2"></i>
            Trade-wise Performance
        </h2>
        <div class="space-y-4">
            <% if (tradeStats && tradeStats.length > 0) { %>
                <% tradeStats.forEach(trade => { %>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full mr-3" style="background: linear-gradient(135deg, #1e40af 0%, #059669 100%);"></div>
                            <div>
                                <p class="font-semibold text-gray-800"><%= trade.trade %></p>
                                <p class="text-sm text-gray-600"><%= trade.student_count %> students</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-lg text-gray-800"><%= trade.average_percentage ? parseFloat(trade.average_percentage).toFixed(1) : '0.0' %>%</p>
                            <div class="w-20 bg-gray-200 rounded-full h-2 mt-1">
                                <div class="bg-gradient-to-r from-blue-600 to-green-600 h-2 rounded-full" style="width: <%= Math.min(trade.average_percentage || 0, 100) %>%"></div>
                            </div>
                        </div>
                    </div>
                <% }); %>
            <% } else { %>
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-chart-bar text-4xl mb-4"></i>
                    <p>No trade performance data available</p>
                </div>
            <% } %>
        </div>
    </div>

    <div class="exam-card p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-layer-group text-blue-600 mr-2"></i>
            Class-wise Performance
        </h2>
        <div class="space-y-4">
            <% if (classStats && classStats.length > 0) { %>
                <% classStats.forEach(classData => { %>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-green-600 rounded-lg flex items-center justify-center text-white font-bold mr-3">
                                <%= classData.class %>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-800">Class <%= classData.class %></p>
                                <p class="text-sm text-gray-600"><%= classData.student_count %> students</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-lg text-gray-800"><%= classData.average_percentage ? parseFloat(classData.average_percentage).toFixed(1) : '0.0' %>%</p>
                            <div class="w-20 bg-gray-200 rounded-full h-2 mt-1">
                                <div class="bg-gradient-to-r from-blue-600 to-green-600 h-2 rounded-full" style="width: <%= Math.min(classData.average_percentage || 0, 100) %>%"></div>
                            </div>
                        </div>
                    </div>
                <% }); %>
            <% } else { %>
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-layer-group text-4xl mb-4"></i>
                    <p>No class performance data available</p>
                </div>
            <% } %>
        </div>
    </div>
</div>

<!-- Grade Distribution & Subject Insights -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <div class="exam-card p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-chart-pie text-indigo-600 mr-2"></i>
            Grade Distribution
        </h2>
        <div class="space-y-3">
            <% if (gradeDistribution && gradeDistribution.length > 0) { %>
                <% gradeDistribution.forEach(grade => { %>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold mr-3 text-sm
                                <%= grade.grade === 'A+' ? 'bg-green-600' :
                                    grade.grade === 'A' ? 'bg-green-500' :
                                    grade.grade === 'B+' ? 'bg-blue-600' :
                                    grade.grade === 'B' ? 'bg-blue-500' :
                                    grade.grade === 'C+' ? 'bg-yellow-600' :
                                    grade.grade === 'C' ? 'bg-yellow-500' :
                                    grade.grade === 'D' ? 'bg-orange-500' : 'bg-red-500' %>">
                                <%= grade.grade %>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-800">Grade <%= grade.grade %></p>
                                <p class="text-sm text-gray-600">
                                    <%= grade.grade === 'A+' ? '90-100%' :
                                        grade.grade === 'A' ? '80-89%' :
                                        grade.grade === 'B+' ? '70-79%' :
                                        grade.grade === 'B' ? '60-69%' :
                                        grade.grade === 'C+' ? '50-59%' :
                                        grade.grade === 'C' ? '40-49%' :
                                        grade.grade === 'D' ? '33-39%' : 'Below 33%' %>
                                </p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-lg text-gray-800"><%= grade.count %></p>
                            <p class="text-sm text-gray-500">students</p>
                        </div>
                    </div>
                <% }); %>
            <% } else { %>
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-chart-pie text-4xl mb-4"></i>
                    <p>No grade distribution data available</p>
                </div>
            <% } %>
        </div>
    </div>

    <div class="exam-card p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-book text-teal-600 mr-2"></i>
            Top Performing Subjects
        </h2>
        <div class="space-y-3">
            <% if (subjectInsights && subjectInsights.length > 0) { %>
                <% subjectInsights.slice(0, 8).forEach((subject, index) => { %>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-gradient-to-br from-teal-500 to-blue-600 flex items-center justify-center text-white font-bold mr-3 text-sm">
                                <%= index + 1 %>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-800"><%= subject.subject_name %></p>
                                <p class="text-sm text-gray-600">
                                    <%= subject.pass_count %>/<%= subject.student_count %> passed
                                    (<%= ((subject.pass_count / subject.student_count) * 100).toFixed(1) %>%)
                                </p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-lg text-gray-800"><%= subject.average_percentage ? parseFloat(subject.average_percentage).toFixed(1) : '0.0' %>%</p>
                            <div class="w-16 bg-gray-200 rounded-full h-2 mt-1">
                                <div class="bg-gradient-to-r from-teal-500 to-blue-600 h-2 rounded-full" style="width: <%= Math.min(subject.average_percentage || 0, 100) %>%"></div>
                            </div>
                        </div>
                    </div>
                <% }); %>
            <% } else { %>
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-book text-4xl mb-4"></i>
                    <p>No subject performance data available</p>
                </div>
            <% } %>
        </div>
    </div>
</div>

<!-- Performance Chart -->
<div class="exam-card p-6 mb-8">
    <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
        <i class="fas fa-chart-line text-purple-600 mr-2"></i>
        Performance Overview
    </h2>
    <div class="h-64">
        <div id="performanceChart" style="width: 100%; height: 100%;"></div>
    </div>
</div>

<!-- Recent Activity & System Status -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <div class="exam-card p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-clock text-orange-600 mr-2"></i>
            System Information
        </h2>
        <div class="space-y-3">
            <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-600 mr-3"></i>
                    <span class="text-gray-800">Database Connection</span>
                </div>
                <span class="text-green-600 font-semibold">Active</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-calendar-alt text-blue-600 mr-3"></i>
                    <span class="text-gray-800">Academic Session</span>
                </div>
                <span class="text-blue-600 font-semibold">2023-2024</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-sync-alt text-purple-600 mr-3"></i>
                    <span class="text-gray-800">Last Updated</span>
                </div>
                <span class="text-purple-600 font-semibold">Just Now</span>
            </div>
        </div>
    </div>

    <div class="exam-card p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-info-circle text-cyan-600 mr-2"></i>
            Quick Statistics
        </h2>
        <div class="space-y-3">
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span class="text-gray-700">Total Exams Conducted</span>
                <span class="font-bold text-gray-800"><%= stats.total_exams || 0 %></span>
            </div>
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span class="text-gray-700">Highest Performance</span>
                <span class="font-bold text-green-600">
                    <% if (tradeStats && tradeStats.length > 0) { %>
                        <%= tradeStats[0].average_percentage ? parseFloat(tradeStats[0].average_percentage).toFixed(1) : '0.0' %>%
                    <% } else { %>
                        0.0%
                    <% } %>
                </span>
            </div>
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span class="text-gray-700">System Status</span>
                <span class="font-bold text-green-600">
                    <i class="fas fa-circle text-green-500 mr-1"></i>
                    Online
                </span>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Performance Chart using ECharts
    const chartContainer = document.getElementById('performanceChart');

    if (!chartContainer) {
        console.error('Performance chart container not found');
        return;
    }

    const tradeData = [
        <% if (tradeStats && tradeStats.length > 0) { %>
            <% tradeStats.forEach((trade, index) => { %>
                {
                    trade: '<%= trade.trade %>',
                    percentage: <%= trade.average_percentage || 0 %>,
                    students: <%= trade.student_count || 0 %>
                }<%= index < tradeStats.length - 1 ? ',' : '' %>
            <% }); %>
        <% } %>
    ];

    const classData = [
        <% if (classStats && classStats.length > 0) { %>
            <% classStats.forEach((classItem, index) => { %>
                {
                    class: '<%= classItem.class %>',
                    percentage: <%= classItem.average_percentage || 0 %>,
                    students: <%= classItem.student_count || 0 %>
                }<%= index < classStats.length - 1 ? ',' : '' %>
            <% }); %>
        <% } %>
    ];

    // Initialize ECharts
    const chart = echarts.init(chartContainer);

    // Prepare data for ECharts
    const categories = [...tradeData.map(t => t.trade), ...classData.map(c => `Class ${c.class}`)];
    const values = [...tradeData.map(t => t.percentage), ...classData.map(c => c.percentage)];
    const studentCounts = [...tradeData.map(t => t.students), ...classData.map(c => c.students)];

    // ECharts configuration
    const option = {
        title: {
            text: 'Performance Overview',
            subtext: 'Trade-wise and Class-wise Average Performance',
            left: 'center',
            textStyle: {
                fontSize: 16,
                fontWeight: 'bold',
                color: '#374151'
            },
            subtextStyle: {
                fontSize: 12,
                color: '#6b7280'
            }
        },
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#3b82f6',
            borderWidth: 1,
            textStyle: {
                color: '#ffffff'
            },
            formatter: function(params) {
                const param = params[0];
                const index = param.dataIndex;
                const studentCount = studentCounts[index];
                return `
                    <div style="padding: 8px;">
                        <div style="font-weight: bold; margin-bottom: 4px;">${param.name}</div>
                        <div>Average: <span style="color: #60a5fa;">${param.value.toFixed(1)}%</span></div>
                        <div>Students: <span style="color: #34d399;">${studentCount}</span></div>
                    </div>
                `;
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '20%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: categories,
            axisLabel: {
                rotate: 45,
                fontSize: 11,
                color: '#374151'
            },
            axisLine: {
                lineStyle: {
                    color: '#d1d5db'
                }
            }
        },
        yAxis: {
            type: 'value',
            min: 0,
            max: 100,
            axisLabel: {
                formatter: '{value}%',
                fontSize: 11,
                color: '#374151'
            },
            axisLine: {
                lineStyle: {
                    color: '#d1d5db'
                }
            },
            splitLine: {
                lineStyle: {
                    color: '#f3f4f6'
                }
            }
        },
        series: [{
            name: 'Average Percentage',
            type: 'bar',
            data: values.map((value, index) => ({
                value: value,
                itemStyle: {
                    color: index < tradeData.length ? '#1e40af' : '#059669',
                    borderRadius: [4, 4, 0, 0]
                }
            })),
            barWidth: '60%',
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };

    // Set chart option
    chart.setOption(option);

    // Handle window resize
    window.addEventListener('resize', function() {
        chart.resize();
    });
});
</script>
