<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exam Results Login - Academic Management System</title>
    
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="/styles.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --exam-primary: #1e40af;
            --exam-secondary: #059669;
            --exam-accent: #0ea5e9;
            --exam-light: #f0f9ff;
        }

        body {
            background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .login-header {
            background: linear-gradient(135deg, var(--exam-primary) 0%, var(--exam-secondary) 100%);
            border-radius: 16px 16px 0 0;
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .login-icon {
            background: rgba(255, 255, 255, 0.2);
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
        }

        .form-group {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .form-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--exam-primary);
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
        }

        .form-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            font-size: 1.1rem;
        }

        .login-btn {
            background: linear-gradient(135deg, var(--exam-primary) 0%, var(--exam-secondary) 100%);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(30, 64, 175, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .demo-credentials {
            background: var(--exam-light);
            border: 1px solid rgba(30, 64, 175, 0.2);
            border-radius: 12px;
            padding: 1rem;
            margin-top: 1.5rem;
        }

        .demo-credentials h4 {
            color: var(--exam-primary);
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .demo-credentials p {
            color: #374151;
            font-size: 0.9rem;
            margin: 0.25rem 0;
        }

        .alert {
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }

        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }

        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        .shape:nth-child(4) {
            width: 100px;
            height: 100px;
            bottom: 10%;
            right: 20%;
            animation-delay: 1s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .system-info {
            text-align: center;
            margin-top: 2rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .system-info h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .system-info p {
            font-size: 1rem;
            margin-bottom: 0.25rem;
        }

        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .feature-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 12px;
            text-align: center;
            color: white;
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: rgba(255, 255, 255, 0.9);
        }
    </style>
</head>
<body>
    <!-- Floating Background Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            <!-- Login Form -->
            <div class="login-container">
                <div class="login-header">
                    <div class="login-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h1 class="text-2xl font-bold">Exam Results System</h1>
                    <p class="text-blue-100 mt-2">Academic Performance Management</p>
                </div>

                <div class="p-8">
                    <!-- Flash Messages -->
                    <% if (typeof error !== 'undefined' && error) { %>
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            <%= error %>
                        </div>
                    <% } %>

                    <% if (typeof success !== 'undefined' && success) { %>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle mr-2"></i>
                            <%= success %>
                        </div>
                    <% } %>

                    <!-- Login Form -->
                    <form action="/exam-results/login" method="POST">
                        <div class="form-group">
                            <i class="form-icon fas fa-user"></i>
                            <input 
                                type="text" 
                                name="username" 
                                class="form-input" 
                                placeholder="Username"
                                required
                                autocomplete="username"
                            >
                        </div>

                        <div class="form-group">
                            <i class="form-icon fas fa-lock"></i>
                            <input 
                                type="password" 
                                name="password" 
                                class="form-input" 
                                placeholder="Password"
                                required
                                autocomplete="current-password"
                            >
                        </div>

                        <button type="submit" class="login-btn">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Access Results System
                        </button>
                    </form>

                    <!-- Demo Credentials -->
                    <div class="demo-credentials">
                        <h4>
                            <i class="fas fa-info-circle mr-2"></i>
                            Demo Access Credentials
                        </h4>
                        <p><strong>Username:</strong> examresults</p>
                        <p><strong>Password:</strong> results2024</p>
                        <p class="text-xs text-gray-600 mt-2">
                            <i class="fas fa-shield-alt mr-1"></i>
                            Use these credentials to access the exam results management system
                        </p>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="system-info">
                <h3>Comprehensive Exam Results Management</h3>
                <p>Academic Session 2023-2024</p>
                
                <div class="features-list">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h4 class="font-semibold">Student Results</h4>
                        <p class="text-sm">Complete academic records</p>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h4 class="font-semibold">Performance Analysis</h4>
                        <p class="text-sm">Detailed analytics & insights</p>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <h4 class="font-semibold">Score Cards</h4>
                        <p class="text-sm">PDF & Excel exports</p>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <h4 class="font-semibold">Distribution Analysis</h4>
                        <p class="text-sm">Score range breakdowns</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-fill demo credentials on page load for convenience
        document.addEventListener('DOMContentLoaded', function() {
            const usernameField = document.querySelector('input[name="username"]');
            const passwordField = document.querySelector('input[name="password"]');
            
            // Add click handler to demo credentials section
            document.querySelector('.demo-credentials').addEventListener('click', function() {
                usernameField.value = 'examresults';
                passwordField.value = 'results2024';
                usernameField.focus();
            });
        });
    </script>
</body>
</html>
