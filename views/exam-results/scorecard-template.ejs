<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Student Score Card</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 16px;
            line-height: 1.4;
            margin: 8mm;
            color: #000;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        .header h1 {
            font-size: 24px;
            margin: 0;
            font-weight: bold;
        }
        .header p {
            font-size: 14px;
            margin: 5px 0;
            color: #666;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        .info-item {
            font-size: 14px;
        }
        .info-label {
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            font-size: 12px;
        }
        th, td {
            border: 1px solid #000;
            padding: 6px;
            text-align: center;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .subject-name {
            text-align: left !important;
            max-width: 120px;
        }
        .category-header {
            background-color: #e0e0e0;
            font-weight: bold;
            text-align: left;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .summary-box {
            border: 1px solid #000;
            padding: 10px;
        }
        .summary-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }
        .grade-excellent { color: #059669; font-weight: bold; }
        .grade-good { color: #0891b2; font-weight: bold; }
        .grade-average { color: #d97706; font-weight: bold; }
        .grade-poor { color: #dc2626; font-weight: bold; }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>DETAILED SCORE CARD</h1>
        <p>Academic Session <%= academicSession %></p>
    </div>

    <!-- Student Information -->
    <div class="section">
        <div class="section-title">STUDENT INFORMATION</div>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Student ID:</span> <%= student.student_id %>
            </div>
            <div class="info-item">
                <span class="info-label">Roll Number:</span> <%= student.student_id %>
            </div>
            <div class="info-item">
                <span class="info-label">Class:</span> <%= student.class %>-<%= student.section %>
            </div>
            <div class="info-item">
                <span class="info-label">Name:</span> <%= student.name %>
            </div>
            <div class="info-item">
                <span class="info-label">Father's Name:</span> <%= student.father_name || 'N/A' %>
            </div>
            <div class="info-item">
                <span class="info-label">Trade:</span> <%= student.trade_full_name %>
            </div>
        </div>
    </div>

    <!-- Subject-wise Performance -->
    <div class="section">
        <div class="section-title">SUBJECT-WISE PERFORMANCE</div>
        <table>
            <thead>
                <tr>
                    <th style="width: 25%;">Subject</th>
                    <th style="width: 12%;">Category</th>
                    <th style="width: 10%;">Theory</th>
                    <th style="width: 10%;">Practical</th>
                    <th style="width: 8%;">CCE</th>
                    <th style="width: 10%;">Total</th>
                    <th style="width: 8%;">%</th>
                    <th style="width: 8%;">Grade</th>
                    <th style="width: 9%;">Status</th>
                </tr>
            </thead>
            <tbody>
                <% 
                let currentCategory = '';
                subjectMarks.forEach((subject, index) => { 
                    // Add category separator
                    if (subject.subject_classification !== currentCategory) {
                        currentCategory = subject.subject_classification;
                %>
                <tr>
                    <td colspan="9" class="category-header"><%= currentCategory.toUpperCase() %></td>
                </tr>
                <% } %>
                <tr>
                    <td class="subject-name"><%= subject.subject_name %></td>
                    <td><%= subject.include_in_grand_total ? 'Core' : 'Additional' %></td>
                    <td><%= (subject.theory_marks || 0).toFixed(1) %></td>
                    <td><%= (subject.practical_marks || 0).toFixed(1) %></td>
                    <td><%= (subject.internal_marks || 0).toFixed(1) %></td>
                    <td><%= (subject.total_marks || 0).toFixed(1) %></td>
                    <td><%= (subject.percentage || 0).toFixed(1) %></td>
                    <td class="<%= 
                        (subject.grade === 'A+' || subject.grade === 'A') ? 'grade-excellent' :
                        (subject.grade === 'B+' || subject.grade === 'B') ? 'grade-good' :
                        (subject.grade === 'C+' || subject.grade === 'C') ? 'grade-average' : 'grade-poor'
                    %>"><%= subject.grade || 'F' %></td>
                    <td><%= subject.result_status || 'FAIL' %></td>
                </tr>
                <% }); %>
            </tbody>
        </table>
    </div>

    <!-- Grand Total Summary -->
    <div class="section">
        <div class="section-title">GRAND TOTAL SUMMARY</div>
        <div class="summary-grid">
            <div class="summary-box">
                <div class="summary-title">Core Subjects Performance</div>
                <p><strong>Total Marks:</strong> <%= grandTotalMarks.toFixed(1) %> / <%= grandTotalMax.toFixed(1) %></p>
                <p><strong>Percentage:</strong> <%= overallPercentage.toFixed(2) %>%</p>
                <p><strong>Grade:</strong> <span class="<%= 
                    (overallGrade === 'A+' || overallGrade === 'A') ? 'grade-excellent' :
                    (overallGrade === 'B+' || overallGrade === 'B') ? 'grade-good' :
                    (overallGrade === 'C+' || overallGrade === 'C') ? 'grade-average' : 'grade-poor'
                %>"><%= overallGrade %></span></p>
            </div>
            <div class="summary-box">
                <div class="summary-title">Additional Information</div>
                <p><strong>Additional Subjects:</strong> <%= additionalMarks.toFixed(1) %> / <%= additionalMax.toFixed(1) %></p>
                <p><em>(Not included in Grand Total)</em></p>
                <p><strong>Promotion Status:</strong> <span class="<%= promotionStatus === 'PROMOTED' ? 'grade-excellent' : 'grade-poor' %>"><%= promotionStatus %></span></p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>Generated on <%= generatedDate %> | This is a computer-generated document</p>
    </div>
</body>
</html>
