<!-- Filters and Controls -->
<div class="exam-card p-6 mb-6">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex flex-col sm:flex-row gap-4">
            <!-- Trade Filter -->
            <div class="flex items-center">
                <label class="text-sm font-medium text-gray-700 mr-2">Trade:</label>
                <select id="tradeFilter" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="all" <%= filters.trade === 'all' ? 'selected' : '' %>>All Trades</option>
                    <%
                        // Remove duplicates by trade_name
                        const uniqueTrades = trades.filter((trade, index, self) =>
                            index === self.findIndex(t => t.trade_name === trade.trade_name)
                        );
                        uniqueTrades.forEach(trade => {
                    %>
                        <option value="<%= trade.trade_name %>" <%= filters.trade === trade.trade_name || filters.trade === trade.original_trade ? 'selected' : '' %>><%= trade.trade_name %></option>
                    <% }); %>
                </select>
            </div>

            <!-- Class Filter -->
            <div class="flex items-center">
                <label class="text-sm font-medium text-gray-700 mr-2">Class:</label>
                <select id="classFilter" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="all" <%= filters.class === 'all' ? 'selected' : '' %>>All Classes</option>
                    <% classes.forEach(classItem => { %>
                        <option value="<%= classItem.class %>" <%= filters.class === classItem.class ? 'selected' : '' %>>Class <%= classItem.class %></option>
                    <% }); %>
                </select>
            </div>

            <!-- Section Filter -->
            <div class="flex items-center">
                <label class="text-sm font-medium text-gray-700 mr-2">Section:</label>
                <select id="sectionFilter" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="all" <%= filters.section === 'all' ? 'selected' : '' %>>All Sections</option>
                    <% sections.forEach(section => { %>
                        <option value="<%= section.section %>" <%= filters.section === section.section ? 'selected' : '' %>>Section <%= section.section %></option>
                    <% }); %>
                </select>
            </div>
        </div>

        <div class="flex gap-2">
            <button id="applyFiltersBtn" class="btn-exam-primary">
                <i class="fas fa-filter mr-2"></i>Apply Filters
            </button>
            <a href="/exam-results/export/excel?trade=<%= filters.trade %>&class=<%= filters.class %>&section=<%= filters.section %>" class="btn-exam-secondary">
                <i class="fas fa-file-excel mr-2"></i>Export Excel
            </a>
        </div>
    </div>
</div>

<!-- Trade-based Tabs -->
<div class="exam-card mb-6">
    <div class="exam-tabs flex border-b">
        <button class="exam-tab trade-tab <%= filters.trade === 'all' ? 'active' : '' %>" data-trade="all">
            <i class="fas fa-users mr-2"></i>All Students
        </button>
        <%
            // Remove duplicates by trade_name for tabs
            const uniqueTradesForTabs = trades.filter((trade, index, self) =>
                index === self.findIndex(t => t.trade_name === trade.trade_name)
            );
            uniqueTradesForTabs.forEach(trade => {
        %>
            <button class="exam-tab trade-tab <%= filters.trade === trade.trade_name || filters.trade === trade.original_trade ? 'active' : '' %>" data-trade="<%= trade.trade_name %>">
                <i class="fas fa-graduation-cap mr-2"></i><%= trade.trade_name %>
            </button>
        <% }); %>
    </div>
</div>

<!-- Results Summary -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
    <div class="stats-card">
        <div class="text-center">
            <p class="text-2xl font-bold text-blue-600"><%= studentResults.length %></p>
            <p class="text-sm text-gray-600">Total Students</p>
        </div>
    </div>
    <div class="stats-card">
        <div class="text-center">
            <% 
                const avgPercentage = studentResults.length > 0 ? 
                    studentResults.reduce((sum, student) => sum + (student.overall_percentage || 0), 0) / studentResults.length : 0;
            %>
            <p class="text-2xl font-bold text-green-600"><%= Number(avgPercentage).toFixed(1) %>%</p>
            <p class="text-sm text-gray-600">Average Performance</p>
        </div>
    </div>
    <div class="stats-card">
        <div class="text-center">
            <% const passCount = studentResults.filter(student => (student.overall_percentage || 0) >= 33).length; %>
            <p class="text-2xl font-bold text-emerald-600"><%= passCount %></p>
            <p class="text-sm text-gray-600">Students Passed</p>
        </div>
    </div>
    <div class="stats-card">
        <div class="text-center">
            <% const excellentCount = studentResults.filter(student => (student.overall_percentage || 0) >= 90).length; %>
            <p class="text-2xl font-bold text-purple-600"><%= excellentCount %></p>
            <p class="text-sm text-gray-600">Excellent (90%+)</p>
        </div>
    </div>
</div>

<!-- Student Results Table -->
<div class="exam-card">
    <div class="overflow-x-auto">
        <table class="exam-table w-full">
            <thead>
                <tr>
                    <th class="px-4 py-3">Student ID</th>
                    <th class="px-4 py-3">Roll Number</th>
                    <th class="px-4 py-3">Student Name</th>
                    <th class="px-4 py-3">Class</th>
                    <th class="px-4 py-3">Section</th>
                    <th class="px-4 py-3">Trade</th>
                    <th class="px-4 py-3">Subjects</th>
                    <th class="px-4 py-3">Grand Total</th>
                    <th class="px-4 py-3">Overall %</th>
                    <th class="px-4 py-3">Grade</th>
                    <th class="px-4 py-3">Status</th>
                    <th class="px-4 py-3">Actions</th>
                </tr>
            </thead>
            <tbody>
                <% if (studentResults && studentResults.length > 0) { %>
                    <% studentResults.forEach(student => { %>
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-3 font-medium"><%= student.student_info.student_id %></td>
                            <td class="px-4 py-3"><%= student.student_info.roll_number %></td>
                            <td class="px-4 py-3 font-medium text-left">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3">
                                        <%= student.student_info.student_name.charAt(0).toUpperCase() %>
                                    </div>
                                    <%= student.student_info.student_name %>
                                </div>
                            </td>
                            <td class="px-4 py-3"><%= student.student_info.class %></td>
                            <td class="px-4 py-3"><%= student.student_info.section %></td>
                            <td class="px-4 py-3">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                                    <%= student.student_info.trade_full_name || student.student_info.trade %>
                                </span>
                            </td>
                            <td class="px-4 py-3">
                                <div class="text-sm">
                                    <div class="text-green-600 font-medium">Core: <%= student.subjects.filter(s => s.include_in_grand_total).length %></div>
                                    <div class="text-gray-500">Additional: <%= student.subjects.filter(s => !s.include_in_grand_total).length %></div>
                                </div>
                            </td>
                            <td class="px-4 py-3">
                                <div class="text-sm">
                                    <div class="font-bold"><%= Number(student.grand_total_marks || 0).toFixed(1) %> / <%= Number(student.grand_total_max || 0).toFixed(1) %></div>
                                    <div class="text-gray-500 text-xs">
                                        Additional: <%= Number(student.additional_marks || 0).toFixed(1) %> / <%= Number(student.additional_max || 0).toFixed(1) %>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-3">
                                <div class="text-lg font-bold <%= (student.overall_percentage || 0) >= 75 ? 'text-green-600' : (student.overall_percentage || 0) >= 50 ? 'text-yellow-600' : 'text-red-600' %>">
                                    <%= Number(student.overall_percentage || 0).toFixed(1) %>%
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                    <div class="bg-gradient-to-r from-blue-600 to-green-600 h-1.5 rounded-full" style="width: <%= Math.min(student.overall_percentage || 0, 100) %>%"></div>
                                </div>
                            </td>
                            <td class="px-4 py-3">
                                <%
                                    const grade = student.overall_grade || 'F';
                                    let gradeClass = 'grade-f';
                                    switch(grade) {
                                        case 'A+': gradeClass = 'grade-a-plus'; break;
                                        case 'A': gradeClass = 'grade-a'; break;
                                        case 'B+': gradeClass = 'grade-b-plus'; break;
                                        case 'B': gradeClass = 'grade-b'; break;
                                        case 'C+': gradeClass = 'grade-c-plus'; break;
                                        case 'C': gradeClass = 'grade-c'; break;
                                        case 'D': gradeClass = 'grade-d'; break;
                                        case 'F': gradeClass = 'grade-f'; break;
                                        default: gradeClass = 'grade-f';
                                    }
                                %>
                                <span class="grade-badge <%= gradeClass %>">
                                    <%= grade %>
                                </span>
                            </td>
                            <td class="px-4 py-3">
                                <span class="px-2 py-1 rounded-full text-xs font-medium <%= (student.promotion_status === 'PROMOTED') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                                    <%= student.promotion_status || 'PENDING' %>
                                </span>
                            </td>
                            <td class="px-4 py-3">
                                <div class="flex gap-2">
                                    <button class="view-student-details-btn text-blue-600 hover:text-blue-800" data-student-id="<%= student.student_info.student_id %>" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="generate-scorecard-btn text-green-600 hover:text-green-800" data-student-id="<%= student.student_info.student_id %>" title="Generate Score Card">
                                        <i class="fas fa-file-pdf"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                <% } else { %>
                    <tr>
                        <td colspan="12" class="px-4 py-8 text-center text-gray-500">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-users text-4xl mb-4 text-gray-300"></i>
                                <p class="text-lg font-medium">No student results found</p>
                                <p class="text-sm">Try adjusting your filters or check if data has been loaded</p>
                            </div>
                        </td>
                    </tr>
                <% } %>
            </tbody>
        </table>
    </div>
</div>

<!-- Student Details Modal -->
<div id="studentDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
            <div class="p-6 border-b">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-bold text-gray-800">Student Details</h2>
                    <button id="closeStudentModalBtn" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div id="studentDetailsContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
// Store student data globally to avoid JSON parsing issues
try {
    window.studentResultsData = <%- JSON.stringify(studentResults) %>;
    console.log('Student data loaded successfully:', window.studentResultsData ? window.studentResultsData.length : 0, 'students');
    console.log('Sample student data:', window.studentResultsData ? window.studentResultsData[0] : 'No data');
} catch (error) {
    console.error('Error loading student data:', error);
    window.studentResultsData = [];
}
</script>
