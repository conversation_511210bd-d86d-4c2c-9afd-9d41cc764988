<!-- Score Cards Management -->
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">Score Cards</h1>
            <p class="text-gray-600 mt-1">Generate and download PDF score cards for students</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="generateBulkScoreCards()" class="btn-exam-primary">
                <i class="fas fa-file-pdf mr-2"></i>
                Bulk Generate
            </button>
            <button onclick="exportScoreCardsExcel()" class="btn-exam-secondary">
                <i class="fas fa-file-excel mr-2"></i>
                Export to Excel
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="exam-card p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Filter Students</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Trade</label>
                <select id="tradeFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="all">All Trades</option>
                    <% trades.forEach(trade => { %>
                        <option value="<%= trade.original_trade %>" <%= filters.trade === trade.original_trade ? 'selected' : '' %>>
                            <%= trade.trade_name %>
                        </option>
                    <% }); %>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Class</label>
                <select id="classFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="all">All Classes</option>
                    <% classes.forEach(cls => { %>
                        <option value="<%= cls.class %>" <%= filters.class === cls.class ? 'selected' : '' %>>
                            Class <%= cls.class %>
                        </option>
                    <% }); %>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Section</label>
                <select id="sectionFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="all">All Sections</option>
                    <% sections.forEach(section => { %>
                        <option value="<%= section.section %>" <%= filters.section === section.section ? 'selected' : '' %>>
                            Section <%= section.section %>
                        </option>
                    <% }); %>
                </select>
            </div>
            <div class="flex items-end">
                <button onclick="applyFilters()" class="btn-exam-primary w-full">
                    <i class="fas fa-filter mr-2"></i>
                    Apply Filters
                </button>
            </div>
        </div>
    </div>

    <!-- Students List -->
    <div class="exam-card">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-800">Students List</h3>
                <div class="flex items-center space-x-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="selectAll" class="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        Select All
                    </label>
                    <span class="text-sm text-gray-600">
                        <span id="selectedCount">0</span> of <%= studentResults.length %> selected
                    </span>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="headerSelectAll" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trade</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Overall %</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% studentResults.forEach((student, index) => { %>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="student-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" 
                                       value="<%= student.student_info.student_id %>" 
                                       data-name="<%= student.student_info.student_name %>">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            <%= student.student_info.student_name %>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            ID: <%= student.student_info.roll_number %>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <%= student.student_info.class %>-<%= student.student_info.section %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <%= student.student_info.trade_full_name || student.student_info.trade %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm font-medium text-gray-900">
                                    <%= student.overall_percentage ? student.overall_percentage.toFixed(1) : '0.0' %>%
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="grade-badge grade-<%= (student.overall_grade || 'F').toLowerCase().replace('+', '-plus') %>">
                                    <%= student.overall_grade || 'F' %>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full 
                                    <%= student.promotion_status === 'PROMOTED' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                                    <%= student.promotion_status || 'DETAINED' %>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="generateSingleScoreCard(<%= student.student_info.student_id %>)" 
                                            class="text-blue-600 hover:text-blue-900" title="Generate PDF">
                                        <i class="fas fa-file-pdf"></i>
                                    </button>
                                    <button onclick="viewStudentDetails(<%= student.student_info.student_id %>)" 
                                            class="text-green-600 hover:text-green-900" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>

        <% if (studentResults.length === 0) { %>
            <div class="text-center py-12">
                <i class="fas fa-users text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Students Found</h3>
                <p class="text-gray-500">Try adjusting your filters to see more results.</p>
            </div>
        <% } %>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-8 max-w-sm w-full mx-4">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Generating Score Cards</h3>
            <p class="text-gray-500" id="loadingText">Please wait while we generate the PDF files...</p>
            <div class="mt-4">
                <div class="bg-gray-200 rounded-full h-2">
                    <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <p class="text-sm text-gray-500 mt-2" id="progressText">0% complete</p>
            </div>
        </div>
    </div>
</div>

<script>
// Filter functionality
function applyFilters() {
    const trade = document.getElementById('tradeFilter').value;
    const classLevel = document.getElementById('classFilter').value;
    const section = document.getElementById('sectionFilter').value;
    
    const params = new URLSearchParams();
    if (trade !== 'all') params.append('trade', trade);
    if (classLevel !== 'all') params.append('class', classLevel);
    if (section !== 'all') params.append('section', section);
    
    window.location.href = `/exam-results/score-cards?${params.toString()}`;
}

// Selection functionality
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const headerSelectAllCheckbox = document.getElementById('headerSelectAll');
    const studentCheckboxes = document.querySelectorAll('.student-checkbox');
    const selectedCountSpan = document.getElementById('selectedCount');
    
    function updateSelectedCount() {
        const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
        selectedCountSpan.textContent = checkedBoxes.length;
        
        // Update select all checkboxes
        const allChecked = checkedBoxes.length === studentCheckboxes.length && studentCheckboxes.length > 0;
        const someChecked = checkedBoxes.length > 0;
        
        selectAllCheckbox.checked = allChecked;
        headerSelectAllCheckbox.checked = allChecked;
        selectAllCheckbox.indeterminate = someChecked && !allChecked;
        headerSelectAllCheckbox.indeterminate = someChecked && !allChecked;
    }
    
    function toggleAllCheckboxes(checked) {
        studentCheckboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });
        updateSelectedCount();
    }
    
    selectAllCheckbox.addEventListener('change', function() {
        toggleAllCheckboxes(this.checked);
    });
    
    headerSelectAllCheckbox.addEventListener('change', function() {
        toggleAllCheckboxes(this.checked);
    });
    
    studentCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
    
    updateSelectedCount();
});

// Score card generation functions
function generateSingleScoreCard(studentId) {
    window.open(`/exam-results/student/${studentId}/scorecard`, '_blank');
}

function generateBulkScoreCards() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    
    if (checkedBoxes.length === 0) {
        alert('Please select at least one student to generate score cards.');
        return;
    }
    
    const studentIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    // Show loading modal
    showLoadingModal();
    
    // Generate score cards for selected students
    generateScoreCardsForStudents(studentIds);
}

function generateScoreCardsForStudents(studentIds) {
    let completed = 0;
    const total = studentIds.length;
    
    studentIds.forEach((studentId, index) => {
        setTimeout(() => {
            // Update progress
            completed++;
            const progress = (completed / total) * 100;
            updateProgress(progress, `Generating score card ${completed} of ${total}...`);
            
            // Generate PDF
            window.open(`/exam-results/student/${studentId}/scorecard`, '_blank');
            
            // Hide modal when complete
            if (completed === total) {
                setTimeout(() => {
                    hideLoadingModal();
                }, 1000);
            }
        }, index * 500); // Stagger the requests
    });
}

function exportScoreCardsExcel() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    
    if (checkedBoxes.length === 0) {
        // Export all if none selected
        window.open('/exam-results/export/excel', '_blank');
    } else {
        // Export selected students
        const studentIds = Array.from(checkedBoxes).map(cb => cb.value);
        const params = new URLSearchParams();
        studentIds.forEach(id => params.append('students', id));
        window.open(`/exam-results/export/excel?${params.toString()}`, '_blank');
    }
}

function viewStudentDetails(studentId) {
    // This could open a modal or navigate to a detailed view
    window.open(`/exam-results/student/${studentId}/details`, '_blank');
}

// Loading modal functions
function showLoadingModal() {
    document.getElementById('loadingModal').classList.remove('hidden');
    document.getElementById('loadingModal').classList.add('flex');
}

function hideLoadingModal() {
    document.getElementById('loadingModal').classList.add('hidden');
    document.getElementById('loadingModal').classList.remove('flex');
}

function updateProgress(percentage, text) {
    document.getElementById('progressBar').style.width = percentage + '%';
    document.getElementById('progressText').textContent = Math.round(percentage) + '% complete';
    document.getElementById('loadingText').textContent = text;
}
</script>
