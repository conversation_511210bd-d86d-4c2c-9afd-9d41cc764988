<!-- Teacher Analysis Header -->
<div class="exam-card p-6 mb-6">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-chalkboard-teacher text-blue-600 mr-3"></i>
                Teacher-wise Performance Analysis
            </h1>
            <p class="text-gray-600 mt-2">Comprehensive analysis of teacher performance across subjects and classes</p>
        </div>
        <div class="flex items-center space-x-4">
            <div class="stats-badge">
                <i class="fas fa-users mr-2"></i>
                <%= teacherAnalysis.length %> Teachers
            </div>
        </div>
    </div>
</div>

<!-- Summary Statistics -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">Total Teachers</p>
                <p class="text-3xl font-bold text-blue-600"><%= teacherAnalysis.length %></p>
                <p class="text-blue-600 text-sm mt-1">
                    <i class="fas fa-chalkboard-teacher mr-1"></i>
                    Active Faculty
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-users"></i>
            </div>
        </div>
    </div>

    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <% const totalSubjects = teacherAnalysis.reduce((sum, t) => sum + t.overall_stats.subjects_taught, 0); %>
                <p class="text-gray-600 text-sm font-medium">Subject Assignments</p>
                <p class="text-3xl font-bold text-green-600"><%= totalSubjects %></p>
                <p class="text-green-600 text-sm mt-1">
                    <i class="fas fa-book mr-1"></i>
                    Total Assignments
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-book-open"></i>
            </div>
        </div>
    </div>

    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <% const totalClasses = teacherAnalysis.reduce((sum, t) => sum + t.overall_stats.classes_managed, 0); %>
                <p class="text-gray-600 text-sm font-medium">Class Incharge</p>
                <p class="text-3xl font-bold text-purple-600"><%= totalClasses %></p>
                <p class="text-purple-600 text-sm mt-1">
                    <i class="fas fa-school mr-1"></i>
                    Classes Managed
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-school"></i>
            </div>
        </div>
    </div>

    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <% const totalStudents = teacherAnalysis.reduce((sum, t) => sum + t.overall_stats.total_students, 0); %>
                <p class="text-gray-600 text-sm font-medium">Students Impacted</p>
                <p class="text-3xl font-bold text-orange-600"><%= totalStudents %></p>
                <p class="text-orange-600 text-sm mt-1">
                    <i class="fas fa-graduation-cap mr-1"></i>
                    Total Reach
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-graduation-cap"></i>
            </div>
        </div>
    </div>
</div>

<!-- Teacher Cards -->
<div class="space-y-6">
    <% teacherAnalysis.forEach((teacher, index) => { %>
        <div class="exam-card">
            <!-- Teacher Header -->
            <div class="p-6 border-b bg-gradient-to-r from-blue-50 to-green-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-green-600 flex items-center justify-center text-white text-xl font-bold mr-4">
                            <%= teacher.name.charAt(0).toUpperCase() %>
                        </div>
                        <div>
                            <h2 class="text-xl font-bold text-gray-800"><%= teacher.full_name || teacher.name %></h2>
                            <p class="text-gray-600"><%= teacher.email %></p>
                            <div class="flex items-center mt-2 space-x-4">
                                <span class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                    <i class="fas fa-book mr-1"></i>
                                    <%= teacher.overall_stats.subjects_taught %> Subjects
                                </span>
                                <span class="text-sm bg-green-100 text-green-800 px-2 py-1 rounded-full">
                                    <i class="fas fa-school mr-1"></i>
                                    <%= teacher.overall_stats.classes_managed %> Classes
                                </span>
                                <span class="text-sm bg-purple-100 text-purple-800 px-2 py-1 rounded-full">
                                    <i class="fas fa-users mr-1"></i>
                                    <%= teacher.overall_stats.total_students %> Students
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold <%= teacher.overall_stats.avg_performance >= 75 ? 'text-green-600' : teacher.overall_stats.avg_performance >= 50 ? 'text-yellow-600' : 'text-red-600' %>">
                            <%= teacher.overall_stats.avg_performance.toFixed(1) %>%
                        </div>
                        <p class="text-sm text-gray-600">Overall Performance</p>
                    </div>
                </div>
            </div>

            <!-- Teacher Details -->
            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Subject Teaching -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-book-open text-blue-600 mr-2"></i>
                            Subject Teaching
                        </h3>
                        <% if (teacher.subjects.length > 0) { %>
                            <div class="space-y-3">
                                <% teacher.subjects.forEach(subject => { %>
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-2">
                                            <div>
                                                <h4 class="font-medium text-gray-800"><%= subject.subject_name %></h4>
                                                <p class="text-sm text-gray-600">
                                                    Class <%= subject.class_level %>-<%= subject.section %> | <%= subject.trade %>
                                                </p>
                                            </div>
                                            <div class="text-right">
                                                <div class="font-bold <%= subject.avg_percentage >= 75 ? 'text-green-600' : subject.avg_percentage >= 50 ? 'text-yellow-600' : 'text-red-600' %>">
                                                    <%= subject.avg_percentage.toFixed(1) %>%
                                                </div>
                                                <p class="text-xs text-gray-500">Avg Score</p>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-3 gap-4 text-sm">
                                            <div class="text-center">
                                                <div class="font-semibold text-gray-800"><%= subject.total_students %></div>
                                                <div class="text-gray-600">Students</div>
                                            </div>
                                            <div class="text-center">
                                                <div class="font-semibold text-green-600"><%= subject.passed_students %></div>
                                                <div class="text-gray-600">Passed</div>
                                            </div>
                                            <div class="text-center">
                                                <div class="font-semibold text-blue-600"><%= subject.distinction_students %></div>
                                                <div class="text-gray-600">Distinction</div>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <div class="w-full bg-gray-200 rounded-full h-2">
                                                <div class="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full" 
                                                     style="width: <%= subject.pass_rate %>%"></div>
                                            </div>
                                            <p class="text-xs text-gray-600 mt-1">Pass Rate: <%= subject.pass_rate.toFixed(1) %>%</p>
                                        </div>
                                    </div>
                                <% }); %>
                            </div>
                        <% } else { %>
                            <div class="text-center py-8 text-gray-500">
                                <i class="fas fa-book text-4xl mb-2"></i>
                                <p>No subject assignments found</p>
                            </div>
                        <% } %>
                    </div>

                    <!-- Class Incharge -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-school text-green-600 mr-2"></i>
                            Class Incharge
                        </h3>
                        <% if (teacher.classes_incharge.length > 0) { %>
                            <div class="space-y-3">
                                <% teacher.classes_incharge.forEach(classInfo => { %>
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-2">
                                            <div>
                                                <h4 class="font-medium text-gray-800">
                                                    Class <%= classInfo.class_level %>-<%= classInfo.section %>
                                                </h4>
                                                <p class="text-sm text-gray-600"><%= classInfo.trade %></p>
                                            </div>
                                            <div class="text-right">
                                                <div class="font-bold <%= classInfo.class_avg_percentage >= 75 ? 'text-green-600' : classInfo.class_avg_percentage >= 50 ? 'text-yellow-600' : 'text-red-600' %>">
                                                    <%= classInfo.class_avg_percentage.toFixed(1) %>%
                                                </div>
                                                <p class="text-xs text-gray-500">Class Avg</p>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-2 gap-4 text-sm">
                                            <div class="text-center">
                                                <div class="font-semibold text-gray-800"><%= classInfo.total_students %></div>
                                                <div class="text-gray-600">Total Students</div>
                                            </div>
                                            <div class="text-center">
                                                <div class="font-semibold text-green-600"><%= classInfo.passed_students %></div>
                                                <div class="text-gray-600">Passed</div>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <div class="w-full bg-gray-200 rounded-full h-2">
                                                <div class="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full" 
                                                     style="width: <%= classInfo.pass_rate %>%"></div>
                                            </div>
                                            <p class="text-xs text-gray-600 mt-1">Pass Rate: <%= classInfo.pass_rate.toFixed(1) %>%</p>
                                        </div>
                                    </div>
                                <% }); %>
                            </div>
                        <% } else { %>
                            <div class="text-center py-8 text-gray-500">
                                <i class="fas fa-school text-4xl mb-2"></i>
                                <p>No class incharge assignments found</p>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    <% }); %>
</div>

<% if (teacherAnalysis.length === 0) { %>
    <div class="exam-card p-12 text-center">
        <i class="fas fa-chalkboard-teacher text-6xl text-gray-300 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">No Teacher Data Available</h3>
        <p class="text-gray-500">No teacher assignments or performance data found for the selected academic session.</p>
    </div>
<% } %>
