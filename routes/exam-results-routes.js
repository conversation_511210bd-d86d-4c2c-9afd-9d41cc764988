const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { checkAuthenticated } = require('../middleware/auth');
const PDFDocument = require('pdfkit');
const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');

// Demo login for exam results view
router.get('/login', (req, res) => {
    res.render('exam-results/login', { 
        title: 'Exam Results Login',
        layout: false,
        error: req.session.flashError,
        success: req.session.flashSuccess
    });
});

// Handle demo login
router.post('/login', (req, res) => {
    const { username, password } = req.body;

    // Demo credentials for exam results access
    if (username === 'examresults' && password === 'results2024') {
        req.session.examResultsAccess = true;
        req.session.examResultsUser = 'Exam Results Coordinator';
        req.session.examResultsSession = '2023-2024'; // Set default session
        req.session.flashSuccess = 'Successfully logged in to Exam Results System';
        return res.redirect('/exam-results/dashboard');
    }

    req.session.flashError = 'Invalid credentials. Use: examresults / results2024';
    res.redirect('/exam-results/login');
});

// Session selection page
router.get('/session-selection', (req, res) => {
    if (!req.session.examResultsAccess) {
        return res.redirect('/exam-results/login');
    }

    res.render('exam-results/session-selection', {
        title: 'Select Academic Session',
        layout: 'layouts/exam-results',
        user: req.session.examResultsUser,
        examResultsSession: req.session.examResultsSession || '2023-2024'
    });
});

// Handle session selection
router.post('/session-selection', async (req, res) => {
    try {
        if (!req.session.examResultsAccess) {
            return res.redirect('/exam-results/login');
        }

        const { academic_session } = req.body;

        // Validate session format
        const validSessions = ['2023-2024', '2022-2023', '2021-2022', '2020-2021'];
        if (!validSessions.includes(academic_session)) {
            req.session.flashError = 'Invalid academic session selected';
            return res.redirect('/exam-results/session-selection');
        }

        // Verify session exists in database (optional check)
        const [sessions] = await db.query(`
            SELECT DISTINCT academic_session
            FROM students
            WHERE academic_session = ?
        `, [academic_session]);

        if (sessions.length === 0) {
            req.session.flashError = 'Selected academic session has no data available';
            return res.redirect('/exam-results/session-selection');
        }

        // Set the selected session
        req.session.examResultsSession = academic_session;
        req.session.flashSuccess = `Academic session ${academic_session} selected successfully`;

        return res.redirect('/exam-results/dashboard');
    } catch (error) {
        console.error('Error in session selection:', error);
        req.session.flashError = 'Error selecting academic session';
        res.redirect('/exam-results/session-selection');
    }
});

// Middleware to check exam results access
const checkExamResultsAccess = (req, res, next) => {
    if (!req.session.examResultsAccess) {
        return res.redirect('/exam-results/login');
    }

    // Check if session is selected - redirect to session selection if not
    if (!req.session.examResultsSession) {
        // Don't redirect if we're already on session-selection page
        if (!req.path.includes('/session-selection')) {
            return res.redirect('/exam-results/session-selection');
        }
        // Set default for session-selection page itself
        req.session.examResultsSession = '2023-2024';
    }

    next();
};

// Main dashboard
router.get('/dashboard', checkExamResultsAccess, async (req, res) => {
    try {
        const selectedSession = req.session.examResultsSession;

        // Get comprehensive statistics
        const [stats] = await db.query(`
            SELECT
                COUNT(DISTINCT s.id) as total_students,
                COUNT(DISTINCT s.class) as total_classes,
                COUNT(DISTINCT s.trade) as total_trades,
                COUNT(DISTINCT ssm.exam_id) as total_exams,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as overall_average,
                COUNT(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 90 THEN 1 END) as a_plus_students,
                COUNT(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 80 AND (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 < 90 THEN 1 END) as a_students,
                COUNT(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 33 THEN 1 END) as pass_count,
                COUNT(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 < 33 THEN 1 END) as fail_count,
                MAX((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) as highest_percentage,
                MIN((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) as lowest_percentage
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id AND ssm.academic_session = s.academic_session
            WHERE s.academic_session = ?
        `, [selectedSession]);

        // Get trade-wise statistics
        const [tradeStats] = await db.query(`
            SELECT
                s.trade,
                COUNT(DISTINCT s.id) as student_count,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as average_percentage
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id AND ssm.academic_session = s.academic_session
            WHERE s.academic_session = ?
            GROUP BY s.trade
            ORDER BY average_percentage DESC
        `, [selectedSession]);

        // Get class-wise statistics
        const [classStats] = await db.query(`
            SELECT
                s.class,
                COUNT(DISTINCT s.id) as student_count,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as average_percentage
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id AND ssm.academic_session = s.academic_session
            WHERE s.academic_session = ?
            GROUP BY s.class
            ORDER BY s.class
        `, [selectedSession]);

        // Get subject-wise performance insights
        const [subjectInsights] = await db.query(`
            SELECT
                sub.name as subject_name,
                COUNT(DISTINCT ssm.student_id) as student_count,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as average_percentage,
                COUNT(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 33 THEN 1 END) as pass_count,
                COUNT(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 < 33 THEN 1 END) as fail_count
            FROM student_subject_marks ssm
            JOIN subjects sub ON ssm.subject_id = sub.id
            JOIN students s ON ssm.student_id = s.id
            WHERE s.academic_session = ?
            GROUP BY sub.id, sub.name
            ORDER BY average_percentage DESC
            LIMIT 10
        `, [selectedSession]);

        // Get grade distribution
        const [gradeDistribution] = await db.query(`
            SELECT
                CASE
                    WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 90 THEN 'A+'
                    WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 80 THEN 'A'
                    WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 70 THEN 'B+'
                    WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 60 THEN 'B'
                    WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 50 THEN 'C+'
                    WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 40 THEN 'C'
                    WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 33 THEN 'D'
                    ELSE 'F'
                END as grade,
                COUNT(*) as count
            FROM student_subject_marks ssm
            JOIN students s ON ssm.student_id = s.id
            WHERE s.academic_session = ?
            GROUP BY grade
            ORDER BY
                CASE grade
                    WHEN 'A+' THEN 1
                    WHEN 'A' THEN 2
                    WHEN 'B+' THEN 3
                    WHEN 'B' THEN 4
                    WHEN 'C+' THEN 5
                    WHEN 'C' THEN 6
                    WHEN 'D' THEN 7
                    WHEN 'F' THEN 8
                END
        `, [selectedSession]);

        res.render('exam-results/dashboard', {
            title: 'Exam Results Dashboard',
            layout: 'layouts/exam-results',
            currentPage: 'dashboard',
            stats: stats[0] || {},
            tradeStats,
            classStats,
            subjectInsights,
            gradeDistribution,
            user: req.session.examResultsUser,
            examResultsSession: req.session.examResultsSession
        });
    } catch (error) {
        console.error('Error loading exam results dashboard:', error);
        req.session.flashError = 'Error loading dashboard data';
        res.redirect('/exam-results/login');
    }
});

// Simple test route
router.get('/test', (req, res) => {
    res.json({
        message: 'Exam results routes working',
        session: req.session.examResultsSession,
        timestamp: new Date().toISOString()
    });
});

// Debug route to check database data
router.get('/debug-data', checkExamResultsAccess, async (req, res) => {
    try {
        const selectedSession = req.session.examResultsSession || '2023-2024';
        console.log('🔍 Debug data requested for session:', selectedSession);

        // Check students table
        const [students] = await db.query(`
            SELECT COUNT(*) as count FROM students WHERE academic_session = ?
        `, [selectedSession]);

        // Check marks table
        const [marks] = await db.query(`
            SELECT COUNT(*) as count FROM student_subject_marks WHERE academic_session = ?
        `, [selectedSession]);

        // Check subjects table
        const [subjects] = await db.query(`
            SELECT COUNT(*) as count FROM subjects
        `);

        // Check all tables in database
        const [tables] = await db.query(`SHOW TABLES`);

        // Check for trade-related tables/views
        const [tradeViews] = await db.query(`
            SHOW TABLES LIKE '%trade%'
        `);

        // Check student-subject relationships
        const [studentSubjects] = await db.query(`
            SELECT s.name, s.class, s.trade, sub.subject_name, sub.stream
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
            LEFT JOIN subjects sub ON ssm.subject_id = sub.id
            WHERE s.academic_session = ?
            LIMIT 10
        `, [selectedSession]);

        // Check all sessions in students table
        const [allSessions] = await db.query(`
            SELECT academic_session, COUNT(*) as count
            FROM students
            GROUP BY academic_session
            ORDER BY count DESC
        `);

        // Sample student data
        const [sampleStudents] = await db.query(`
            SELECT id, student_id, name, class, section, trade, academic_session
            FROM students WHERE academic_session = ? LIMIT 5
        `, [selectedSession]);

        // Sample marks data
        const [sampleMarks] = await db.query(`
            SELECT student_id, subject_id, theory_marks, practical_marks, internal_marks, academic_session
            FROM student_subject_marks WHERE academic_session = ? LIMIT 5
        `, [selectedSession]);

        // Check subjects by stream
        const [subjectsByStream] = await db.query(`
            SELECT stream, COUNT(*) as count, GROUP_CONCAT(subject_name LIMIT 5) as sample_subjects
            FROM subjects
            GROUP BY stream
        `);

        const result = {
            session: selectedSession,
            counts: {
                students: students[0].count,
                marks: marks[0].count,
                subjects: subjects[0].count
            },
            allTables: tables.map(t => Object.values(t)[0]),
            tradeRelatedTables: tradeViews.map(t => Object.values(t)[0]),
            studentSubjectRelationships: studentSubjects,
            subjectsByStream,
            allSessions,
            sampleStudents,
            sampleMarks
        };

        console.log('🔍 Debug data result:', result);
        res.json(result);
    } catch (error) {
        console.error('Debug data error:', error);
        res.json({ error: error.message, stack: error.stack });
    }
});

// Student results view with trade-based tabs
router.get('/students', checkExamResultsAccess, async (req, res) => {
    try {
        const { trade = 'all', class: classLevel = 'all', section = 'all' } = req.query;

        const selectedSession = req.session.examResultsSession || '2023-2024';
        console.log('Loading students for session:', selectedSession);

        let whereConditions = ["s.academic_session = ?"];
        let queryParams = [selectedSession];

        if (trade !== 'all') {
            // Class-specific trade mapping
            const tradeMapping = {
                // Class 10: General only
                'General': ['Biology', 'Physics', 'Chemistry', 'Mathematics', 'Computer Science', 'Commerce', 'Economics', 'Accountancy'],

                // Class 11 & 12: Specific streams
                'Medical': ['Biology'],
                'Non Medical': ['Physics', 'Chemistry', 'Mathematics', 'Computer Science'],
                'Commerce': ['Commerce', 'Economics', 'Accountancy'],
                'Humanities': ['History', 'Geography', 'Political Science', 'Psychology', 'Sociology', 'Philosophy']
            };

            if (tradeMapping[trade]) {
                // If it's a mapped trade category, filter by the individual subjects
                const placeholders = tradeMapping[trade].map(() => '?').join(',');
                whereConditions.push(`s.trade IN (${placeholders})`);
                queryParams.push(...tradeMapping[trade]);
                console.log('🎯 Filtering by mapped trade:', trade, '→', tradeMapping[trade]);
            } else {
                // If it's an individual subject, filter directly
                whereConditions.push('s.trade = ?');
                queryParams.push(trade);
                console.log('🎯 Filtering by individual trade:', trade);
            }
        }
        if (classLevel !== 'all') {
            whereConditions.push('s.class = ?');
            queryParams.push(classLevel);
        }
        if (section !== 'all') {
            whereConditions.push('s.section = ?');
            queryParams.push(section);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get student results with subject-wise marks
        const [studentResults] = await db.query(`
            SELECT
                s.id as student_id,
                s.student_id as roll_number,
                s.name as student_name,
                s.class,
                s.section,
                s.trade,
                s.trade as trade_full_name,

                -- Subject information
                sub.code as subject_code,
                sub.name as subject_name,
                sub.subject_category_new,
                
                -- Subject classification for trade-specific logic
                CASE 
                    WHEN sub.code IN ('1', '2') THEN 'Compulsory Language'
                    WHEN sub.code IN ('28', '52', '53') AND s.trade = 'Non-Medical' THEN 'Core Compulsory'
                    WHEN sub.code IN ('141', '142', '26') AND s.trade = 'Commerce' THEN 'Core Compulsory'
                    WHEN sub.code = '54' AND s.trade = 'Medical' THEN 'Core Compulsory'
                    WHEN sub.code = '54' AND s.trade = 'Non-Medical' THEN 'Additional Optional'
                    WHEN sub.code IN ('146', '49') THEN 'Additional Compulsory'
                    ELSE 'Other'
                END as subject_classification,
                
                -- Include in grand total flag
                CASE 
                    WHEN sub.code IN ('1', '2') THEN TRUE
                    WHEN sub.code IN ('28', '52', '53') AND s.trade = 'Non-Medical' THEN TRUE
                    WHEN sub.code IN ('141', '142', '26') AND s.trade = 'Commerce' THEN TRUE
                    WHEN sub.code = '54' AND s.trade = 'Medical' THEN TRUE
                    ELSE FALSE
                END as include_in_grand_total,
                
                -- Marks information
                COALESCE(ssm.theory_marks, 0) as theory_marks,
                COALESCE(ssm.practical_marks, 0) as practical_marks,
                COALESCE(ssm.internal_marks, 0) as internal_marks,
                COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0) as total_marks,
                COALESCE(ssm.max_marks, 100) as max_marks,
                CASE
                    WHEN COALESCE(ssm.max_marks, 0) > 0 THEN
                        ROUND(((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100, 2)
                    ELSE 0
                END as percentage,
                
                -- Grade calculation
                CASE
                    WHEN COALESCE(ssm.max_marks, 0) > 0 AND ((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100 >= 90 THEN 'A+'
                    WHEN COALESCE(ssm.max_marks, 0) > 0 AND ((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100 >= 80 THEN 'A'
                    WHEN COALESCE(ssm.max_marks, 0) > 0 AND ((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100 >= 70 THEN 'B+'
                    WHEN COALESCE(ssm.max_marks, 0) > 0 AND ((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100 >= 60 THEN 'B'
                    WHEN COALESCE(ssm.max_marks, 0) > 0 AND ((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100 >= 50 THEN 'C+'
                    WHEN COALESCE(ssm.max_marks, 0) > 0 AND ((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100 >= 40 THEN 'C'
                    WHEN COALESCE(ssm.max_marks, 0) > 0 AND ((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100 >= 33 THEN 'D'
                    ELSE 'F'
                END as grade,

                -- Result status (individual subject pass/fail)
                CASE
                    WHEN COALESCE(ssm.max_marks, 0) > 0 AND ((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100 >= 33 THEN 'PASS'
                    ELSE 'FAIL'
                END as result_status
                
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id AND ssm.academic_session = s.academic_session
            LEFT JOIN subjects sub ON ssm.subject_id = sub.id
            WHERE ${whereClause}
            ORDER BY s.class, s.section, s.name, 
                     CASE 
                         WHEN sub.code IN ('1', '2') THEN 1
                         WHEN sub.subject_category_new = 'science' OR sub.subject_category_new = 'commerce' THEN 2
                         ELSE 3
                     END,
                     CAST(sub.code AS UNSIGNED)
        `, queryParams);

        console.log(`Found ${studentResults.length} student result records for session ${selectedSession}`);
        if (studentResults.length > 0) {
            console.log('Sample student result:', studentResults[0]);
        } else {
            console.log('❌ NO STUDENT RESULTS FOUND - Checking individual tables...');

            // Check if we have students but no marks
            const [studentsOnly] = await db.query(`
                SELECT COUNT(*) as student_count FROM students WHERE academic_session = ?
            `, [selectedSession]);

            const [marksOnly] = await db.query(`
                SELECT COUNT(*) as marks_count FROM student_subject_marks WHERE academic_session = ?
            `, [selectedSession]);

            const [subjectsCount] = await db.query(`
                SELECT COUNT(*) as subject_count FROM subjects
            `);

            console.log(`📊 Data Check for session ${selectedSession}:`);
            console.log(`   Students: ${studentsOnly[0].student_count}`);
            console.log(`   Marks records: ${marksOnly[0].marks_count}`);
            console.log(`   Subjects: ${subjectsCount[0].subject_count}`);

            // Check if students exist but with different session
            const [allStudents] = await db.query(`
                SELECT academic_session, COUNT(*) as count
                FROM students
                GROUP BY academic_session
                ORDER BY count DESC
            `);
            console.log('📚 Students by session:', allStudents);

            // Check if marks exist but with different session
            const [allMarks] = await db.query(`
                SELECT academic_session, COUNT(*) as count
                FROM student_subject_marks
                GROUP BY academic_session
                ORDER BY count DESC
            `);
            console.log('📝 Marks by session:', allMarks);
        }

        // Get available classes and trades for class-specific trade tabs
        const [classTradeData] = await db.query(`
            SELECT DISTINCT s.class, s.trade as original_trade
            FROM students s
            WHERE s.academic_session = ? AND s.class IN ('10', '11', '12')
            ORDER BY s.class, s.trade
        `, [selectedSession]);

        console.log('🎯 Class-trade data in database:', classTradeData);

        // Class-specific trade mapping
        function getTradeCategory(originalTrade, studentClass) {
            if (studentClass === '10') {
                return 'General';
            }

            // For classes 11 & 12
            const tradeMapping = {
                'Biology': 'Medical',
                'Physics': 'Non Medical',
                'Chemistry': 'Non Medical',
                'Mathematics': 'Non Medical',
                'Computer Science': 'Non Medical',
                'Commerce': 'Commerce',
                'Economics': 'Commerce',
                'Accountancy': 'Commerce',
                'History': 'Humanities',
                'Geography': 'Humanities',
                'Political Science': 'Humanities',
                'Psychology': 'Humanities',
                'Sociology': 'Humanities',
                'Philosophy': 'Humanities'
            };

            return tradeMapping[originalTrade] || originalTrade;
        }

        // Create mapped trades for tabs based on available data
        const mappedTrades = classTradeData.map(item => ({
            original_trade: item.original_trade,
            trade_name: getTradeCategory(item.original_trade, item.class),
            trade_code: getTradeCategory(item.original_trade, item.class),
            class: item.class
        }));

        // Remove duplicates by trade_name
        const trades = mappedTrades.reduce((acc, trade) => {
            if (!acc.find(t => t.trade_name === trade.trade_name)) {
                acc.push({
                    original_trade: trade.original_trade,
                    trade_name: trade.trade_name,
                    trade_code: trade.trade_code
                });
            }
            return acc;
        }, []);

        console.log('🎯 Mapped trades for tabs:', trades);

        const [classes] = await db.query(`
            SELECT DISTINCT class FROM students
            WHERE academic_session = ? AND class IN ('10', '11', '12')
            ORDER BY CAST(class AS UNSIGNED)
        `, [selectedSession]);

        const [sections] = await db.query(`
            SELECT DISTINCT section FROM students WHERE academic_session = ? ORDER BY section
        `, [selectedSession]);

        // Group results by student
        const groupedResults = {};
        console.log(`Processing ${studentResults.length} student results for grouping`);

        studentResults.forEach(result => {
            const studentKey = result.student_id;
            if (!groupedResults[studentKey]) {
                // Map individual subject to trade category based on class
                function getTradeCategory(originalTrade, studentClass) {
                    if (studentClass === '10') {
                        return 'General';
                    }

                    // For classes 11 & 12
                    const tradeMapping = {
                        'Biology': 'Medical',
                        'Physics': 'Non Medical',
                        'Chemistry': 'Non Medical',
                        'Mathematics': 'Non Medical',
                        'Computer Science': 'Non Medical',
                        'Commerce': 'Commerce',
                        'Economics': 'Commerce',
                        'Accountancy': 'Commerce',
                        'History': 'Humanities',
                        'Geography': 'Humanities',
                        'Political Science': 'Humanities',
                        'Psychology': 'Humanities',
                        'Sociology': 'Humanities',
                        'Philosophy': 'Humanities'
                    };

                    return tradeMapping[originalTrade] || originalTrade;
                }

                groupedResults[studentKey] = {
                    student_info: {
                        student_id: result.student_id,
                        roll_number: result.roll_number,
                        student_name: result.student_name,
                        class: result.class,
                        section: result.section,
                        trade: result.trade,
                        trade_full_name: getTradeCategory(result.trade, result.class)
                    },
                    subjects: [],
                    grand_total_marks: 0,
                    grand_total_max: 0,
                    additional_marks: 0,
                    additional_max: 0
                };
            }

            if (result.subject_name) {
                groupedResults[studentKey].subjects.push(result);

                // Add to grand total if it's a core subject
                if (result.include_in_grand_total) {
                    groupedResults[studentKey].grand_total_marks += parseFloat(result.total_marks) || 0;
                    groupedResults[studentKey].grand_total_max += parseFloat(result.max_marks) || 0;
                } else {
                    groupedResults[studentKey].additional_marks += parseFloat(result.total_marks) || 0;
                    groupedResults[studentKey].additional_max += parseFloat(result.max_marks) || 0;
                }
            }
        });

        // Calculate overall percentages, grades, and pass/fail status
        Object.keys(groupedResults).forEach(studentKey => {
            const student = groupedResults[studentKey];

            if (student.grand_total_max > 0) {
                // Calculate overall percentage from core subjects only
                student.overall_percentage = Math.round((student.grand_total_marks / student.grand_total_max) * 100 * 100) / 100;

                // Calculate overall grade based on grand total percentage
                if (student.overall_percentage >= 90) student.overall_grade = 'A+';
                else if (student.overall_percentage >= 80) student.overall_grade = 'A';
                else if (student.overall_percentage >= 70) student.overall_grade = 'B+';
                else if (student.overall_percentage >= 60) student.overall_grade = 'B';
                else if (student.overall_percentage >= 50) student.overall_grade = 'C+';
                else if (student.overall_percentage >= 40) student.overall_grade = 'C';
                else if (student.overall_percentage >= 33) student.overall_grade = 'D';
                else student.overall_grade = 'F';

                // Calculate pass/fail status based on new criteria:
                // FAIL if student fails in 2 or more core subjects, otherwise PASS
                const coreSubjects = student.subjects.filter(s => s.include_in_grand_total);
                const failedCoreSubjects = coreSubjects.filter(s => s.result_status === 'FAIL');

                if (failedCoreSubjects.length >= 2) {
                    student.promotion_status = 'FAIL';
                } else {
                    student.promotion_status = 'PASS';
                }

                // Add additional statistics for detailed view
                student.core_subjects_count = coreSubjects.length;
                student.failed_core_subjects_count = failedCoreSubjects.length;
                student.additional_subjects_count = student.subjects.filter(s => !s.include_in_grand_total).length;
            } else {
                student.overall_percentage = 0;
                student.overall_grade = 'F';
                student.promotion_status = 'FAIL';
                student.core_subjects_count = 0;
                student.failed_core_subjects_count = 0;
                student.additional_subjects_count = 0;
            }
        });

        const finalResults = Object.values(groupedResults);
        console.log(`Final grouped results: ${finalResults.length} students`);
        if (finalResults.length > 0) {
            console.log('Sample final result:', {
                student_name: finalResults[0].student_info.student_name,
                subjects_count: finalResults[0].subjects.length,
                grand_total: finalResults[0].grand_total_marks,
                overall_percentage: finalResults[0].overall_percentage
            });
        }

        console.log('🎯 RENDERING STUDENTS PAGE WITH DATA:');
        console.log(`   - studentResults length: ${finalResults.length}`);
        console.log(`   - session: ${selectedSession}`);
        console.log(`   - filters:`, { trade, class: classLevel, section });
        console.log(`   - trades count: ${trades.length}`);
        console.log(`   - classes count: ${classes.length}`);
        console.log(`   - sections count: ${sections.length}`);

        res.render('exam-results/students', {
            title: 'Student Results',
            layout: 'layouts/exam-results',
            currentPage: 'students',
            studentResults: finalResults,
            filters: { trade, class: classLevel, section },
            trades,
            classes,
            sections,
            user: req.session.examResultsUser,
            examResultsSession: req.session.examResultsSession
        });
    } catch (error) {
        console.error('Error loading student results:', error);
        req.session.flashError = 'Error loading student results';
        res.redirect('/exam-results/dashboard');
    }
});

// Generate detailed score card PDF
router.get('/student/:studentId/scorecard', checkExamResultsAccess, async (req, res) => {
    try {
        const studentId = req.params.studentId;

        // Get complete student information
        const [studentInfo] = await db.query(`
            SELECT
                s.*,
                COALESCE(tm.correct_trade_name, s.trade) as trade_full_name
            FROM students s
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE s.id = ? AND s.academic_session = ?
        `, [studentId, req.session.examResultsSession]);

        if (studentInfo.length === 0) {
            return res.status(404).json({ error: 'Student not found' });
        }

        const student = studentInfo[0];

        // Get subject-wise marks with categorization
        const [subjectMarks] = await db.query(`
            SELECT
                sub.code as subject_code,
                sub.name as subject_name,
                sub.subject_category_new,

                -- Subject classification
                CASE
                    WHEN sub.code IN ('1', '2') THEN 'Compulsory Language'
                    WHEN sub.code IN ('28', '52', '53') AND ? = 'Non-Medical' THEN 'Core Compulsory'
                    WHEN sub.code IN ('141', '142', '26') AND ? = 'Commerce' THEN 'Core Compulsory'
                    WHEN sub.code = '54' AND ? = 'Medical' THEN 'Core Compulsory'
                    WHEN sub.code = '54' AND ? = 'Non-Medical' THEN 'Additional Optional'
                    WHEN sub.code IN ('146', '49') THEN 'Additional Compulsory'
                    ELSE 'Other'
                END as subject_classification,

                -- Include in grand total flag
                CASE
                    WHEN sub.code IN ('1', '2') THEN TRUE
                    WHEN sub.code IN ('28', '52', '53') AND ? = 'Non-Medical' THEN TRUE
                    WHEN sub.code IN ('141', '142', '26') AND ? = 'Commerce' THEN TRUE
                    WHEN sub.code = '54' AND ? = 'Medical' THEN TRUE
                    ELSE FALSE
                END as include_in_grand_total,

                ssm.theory_marks,
                ssm.practical_marks,
                ssm.internal_marks,
                ssm.total_marks,
                ssm.max_marks,
                ROUND((ssm.total_marks / ssm.max_marks) * 100, 2) as percentage,

                -- Grade calculation
                CASE
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 90 THEN 'A+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 80 THEN 'A'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 70 THEN 'B+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 60 THEN 'B'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 50 THEN 'C+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 40 THEN 'C'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 33 THEN 'D'
                    ELSE 'F'
                END as grade,

                CASE
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 33 THEN 'PASS'
                    ELSE 'FAIL'
                END as result_status

            FROM student_subject_marks ssm
            JOIN subjects sub ON ssm.subject_id = sub.id
            WHERE ssm.student_id = ? AND ssm.academic_session = ?
            ORDER BY
                CASE
                    WHEN sub.code IN ('1', '2') THEN 1
                    WHEN sub.subject_category_new IN ('science', 'commerce') THEN 2
                    ELSE 3
                END,
                CAST(sub.code AS UNSIGNED)
        `, [student.trade, student.trade, student.trade, student.trade, student.trade, student.trade, student.trade, studentId, req.session.examResultsSession]);

        // Calculate grand totals
        let grandTotalMarks = 0;
        let grandTotalMax = 0;
        let additionalMarks = 0;
        let additionalMax = 0;

        subjectMarks.forEach(subject => {
            if (subject.include_in_grand_total) {
                grandTotalMarks += subject.total_marks || 0;
                grandTotalMax += subject.max_marks || 0;
            } else {
                additionalMarks += subject.total_marks || 0;
                additionalMax += subject.max_marks || 0;
            }
        });

        const overallPercentage = grandTotalMax > 0 ? Math.round((grandTotalMarks / grandTotalMax) * 100 * 100) / 100 : 0;

        let overallGrade = 'F';
        if (overallPercentage >= 90) overallGrade = 'A+';
        else if (overallPercentage >= 80) overallGrade = 'A';
        else if (overallPercentage >= 70) overallGrade = 'B+';
        else if (overallPercentage >= 60) overallGrade = 'B';
        else if (overallPercentage >= 50) overallGrade = 'C+';
        else if (overallPercentage >= 40) overallGrade = 'C';
        else if (overallPercentage >= 33) overallGrade = 'D';

        const promotionStatus = overallPercentage >= 33 ? 'PROMOTED' : 'DETAINED';

        // Use common PDF generation method
        const { generatePDF } = require('../utils/pdf-generator');
        const filename = `scorecard_${student.student_id}_${Date.now()}.pdf`;
        const outputPath = path.join(__dirname, '../public/temp', filename);
        const templatePath = path.join(__dirname, '../views/exam-results/scorecard-template.ejs');

        // Ensure temp directory exists
        const tempDir = path.dirname(outputPath);
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }

        // Prepare data for template
        const templateData = {
            student,
            subjectMarks,
            grandTotalMarks,
            grandTotalMax,
            additionalMarks,
            additionalMax,
            overallPercentage,
            overallGrade,
            promotionStatus,
            academicSession: req.session.examResultsSession,
            generatedDate: new Date().toLocaleDateString()
        };

        try {
            // Generate PDF using common method
            await generatePDF(templateData, templatePath, outputPath);

            // Return success response
            const publicPath = `/temp/${filename}`;
            res.json({ success: true, url: publicPath });
        } catch (error) {
            console.error('Error generating PDF:', error);
            res.status(500).json({ error: 'Error generating PDF: ' + error.message });
        }

    } catch (error) {
        console.error('Error generating score card:', error);
        res.status(500).json({ error: 'Error generating score card' });
    }
});

// Academic Performance Analysis
router.get('/analysis', checkExamResultsAccess, async (req, res) => {
    try {
        // Class-wise Analysis
        const [classAnalysis] = await db.query(`
            SELECT
                s.class,
                COUNT(DISTINCT s.id) as student_count,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as average_percentage,
                MAX((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) as highest_percentage,
                MIN((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) as lowest_percentage,
                COUNT(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 33 THEN 1 END) as pass_count,
                COUNT(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 < 33 THEN 1 END) as fail_count
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id AND ssm.academic_session = s.academic_session
            WHERE s.academic_session = ?
            GROUP BY s.class
            ORDER BY s.class
        `, [req.session.examResultsSession]);

        // Trade-wise Analysis
        const [tradeAnalysis] = await db.query(`
            SELECT
                s.trade,
                COALESCE(tm.correct_trade_name, s.trade) as trade_full_name,
                COUNT(DISTINCT s.id) as student_count,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as average_percentage,
                MAX((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) as highest_percentage,
                MIN((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) as lowest_percentage,
                COUNT(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 >= 33 THEN 1 END) as pass_count,
                COUNT(CASE WHEN (ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100 < 33 THEN 1 END) as fail_count
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id AND ssm.academic_session = s.academic_session
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE s.academic_session = ?
            GROUP BY s.trade
            ORDER BY average_percentage DESC
        `, [req.session.examResultsSession]);

        // Section-wise Analysis
        const [sectionAnalysis] = await db.query(`
            SELECT
                CONCAT(s.class, '-', s.section) as class_section,
                s.class,
                s.section,
                COUNT(DISTINCT s.id) as student_count,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as average_percentage,
                MAX((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) as highest_percentage,
                MIN((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100) as lowest_percentage
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id AND ssm.academic_session = s.academic_session
            WHERE s.academic_session = ?
            GROUP BY s.class, s.section
            ORDER BY s.class, s.section
        `, [req.session.examResultsSession]);

        // Top Performers
        const [topPerformers] = await db.query(`
            SELECT
                s.id,
                s.student_id as roll_number,
                s.name,
                s.class,
                s.section,
                s.trade,
                COALESCE(tm.correct_trade_name, s.trade) as trade_full_name,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as average_percentage
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id AND ssm.academic_session = s.academic_session
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE s.academic_session = ?
            GROUP BY s.id
            ORDER BY average_percentage DESC
            LIMIT 10
        `, [req.session.examResultsSession]);

        res.render('exam-results/analysis', {
            title: 'Academic Performance Analysis',
            layout: 'layouts/exam-results',
            currentPage: 'analysis',
            classAnalysis,
            tradeAnalysis,
            sectionAnalysis,
            topPerformers,
            user: req.session.examResultsUser,
            examResultsSession: req.session.examResultsSession
        });
    } catch (error) {
        console.error('Error loading analysis:', error);
        req.session.flashError = 'Error loading analysis data';
        res.redirect('/exam-results/dashboard');
    }
});

// Score Distribution Analysis
router.get('/score-distribution', checkExamResultsAccess, async (req, res) => {
    try {
        const { trade = 'all', class: classLevel = 'all', section = 'all', customRanges } = req.query;

        const selectedSession = req.session.examResultsSession;
        let whereConditions = ["s.academic_session = ?"];
        let queryParams = [selectedSession];

        if (trade !== 'all') {
            whereConditions.push('s.trade = ?');
            queryParams.push(trade);
        }
        if (classLevel !== 'all') {
            whereConditions.push('s.class = ?');
            queryParams.push(classLevel);
        }
        if (section !== 'all') {
            whereConditions.push('s.section = ?');
            queryParams.push(section);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get student percentages for distribution analysis
        const [studentPercentages] = await db.query(`
            SELECT
                s.id,
                s.student_id as roll_number,
                s.name,
                s.class,
                s.section,
                s.trade,
                ROUND(AVG(COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / NULLIF(ssm.max_marks, 0) * 100, 2) as percentage
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id AND ssm.academic_session = s.academic_session
            WHERE ${whereClause}
            GROUP BY s.id
            HAVING percentage IS NOT NULL
            ORDER BY percentage DESC
        `, queryParams);

        // Get custom ranges from database for current session
        let ranges;
        let isCustomRanges = false;
        let customRangesData = null;

        try {
            // Check if custom ranges exist in database for this session
            const [customRangesFromDB] = await db.query(`
                SELECT
                    min_percentage as min,
                    max_percentage as max,
                    range_label as label,
                    display_order,
                    created_by
                FROM custom_score_ranges
                WHERE academic_session = ? AND is_active = TRUE
                ORDER BY display_order
            `, [selectedSession]);

            if (customRangesFromDB.length > 0) {
                // Check if any ranges are custom (not system default)
                const hasCustomRanges = customRangesFromDB.some(range => range.created_by !== 'system');

                if (hasCustomRanges) {
                    ranges = customRangesFromDB.map(range => ({
                        min: parseFloat(range.min),
                        max: parseFloat(range.max),
                        label: range.label
                    }));
                    isCustomRanges = true;
                    customRangesData = ranges;
                    console.log('Using custom ranges from database:', ranges);
                } else {
                    // Use system default ranges
                    ranges = customRangesFromDB.map(range => ({
                        min: parseFloat(range.min),
                        max: parseFloat(range.max),
                        label: range.label
                    }));
                    console.log('Using system default ranges from database:', ranges);
                }
            } else {
                // Fallback to hardcoded defaults if no database ranges exist
                ranges = [
                    { min: 90, max: 100, label: '90-100 (Excellent)' },
                    { min: 80, max: 89, label: '80-89 (Very Good)' },
                    { min: 70, max: 79, label: '70-79 (Good)' },
                    { min: 60, max: 69, label: '60-69 (Above Average)' },
                    { min: 50, max: 59, label: '50-59 (Average)' },
                    { min: 40, max: 49, label: '40-49 (Below Average)' },
                    { min: 0, max: 39, label: 'Below 40 (Poor)' }
                ];
                console.log('Using fallback hardcoded ranges');
            }
        } catch (error) {
            console.error('Error fetching custom ranges from database:', error);
            // Fallback to hardcoded defaults
            ranges = [
                { min: 90, max: 100, label: '90-100 (Excellent)' },
                { min: 80, max: 89, label: '80-89 (Very Good)' },
                { min: 70, max: 79, label: '70-79 (Good)' },
                { min: 60, max: 69, label: '60-69 (Above Average)' },
                { min: 50, max: 59, label: '50-59 (Average)' },
                { min: 40, max: 49, label: '40-49 (Below Average)' },
                { min: 0, max: 39, label: 'Below 40 (Poor)' }
            ];
        }

        // Calculate distribution
        const distribution = ranges.map(range => {
            const studentsInRange = studentPercentages.filter(student =>
                student.percentage >= range.min && student.percentage <= range.max
            );

            return {
                ...range,
                count: studentsInRange.length,
                percentage: studentPercentages.length > 0 ?
                    Math.round((studentsInRange.length / studentPercentages.length) * 100 * 100) / 100 : 0,
                students: studentsInRange
            };
        });

        // Get filter options with proper trade names
        const [trades] = await db.query(`
            SELECT
                s.trade as original_trade,
                COALESCE(tm.correct_trade_name, s.trade) as trade_name,
                COALESCE(tm.correct_trade_code, s.trade) as trade_code
            FROM students s
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE s.academic_session = ?
            GROUP BY s.trade, COALESCE(tm.correct_trade_name, s.trade), COALESCE(tm.correct_trade_code, s.trade)
            ORDER BY COALESCE(tm.correct_trade_name, s.trade)
        `, [selectedSession]);

        const [classes] = await db.query(`
            SELECT DISTINCT class FROM students WHERE academic_session = ? ORDER BY class
        `, [selectedSession]);

        const [sections] = await db.query(`
            SELECT DISTINCT section FROM students WHERE academic_session = ? ORDER BY section
        `, [selectedSession]);

        res.render('exam-results/score-distribution', {
            title: 'Score Distribution Analysis',
            layout: 'layouts/exam-results',
            currentPage: 'score-distribution',
            distribution,
            totalStudents: studentPercentages.length,
            filters: { trade, class: classLevel, section },
            trades,
            classes,
            sections,
            isCustomRanges,
            customRangesData,
            user: req.session.examResultsUser,
            examResultsSession: req.session.examResultsSession
        });
    } catch (error) {
        console.error('Error loading score distribution:', error);
        req.session.flashError = 'Error loading score distribution data';
        res.redirect('/exam-results/dashboard');
    }
});

// Excel export for student results
router.get('/export/excel', checkExamResultsAccess, async (req, res) => {
    try {
        const { trade = 'all', class: classLevel = 'all', section = 'all' } = req.query;

        const selectedSession = req.session.examResultsSession;
        let whereConditions = ["s.academic_session = ?"];
        let queryParams = [selectedSession];

        if (trade !== 'all') {
            whereConditions.push('s.trade = ?');
            queryParams.push(trade);
        }
        if (classLevel !== 'all') {
            whereConditions.push('s.class = ?');
            queryParams.push(classLevel);
        }
        if (section !== 'all') {
            whereConditions.push('s.section = ?');
            queryParams.push(section);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get student data for export
        const [studentData] = await db.query(`
            SELECT
                s.id,
                s.student_id as roll_number,
                s.name,
                s.class,
                s.section,
                s.trade,
                COALESCE(tm.correct_trade_name, s.trade) as trade_full_name,
                ROUND(AVG((ssm.theory_marks + ssm.practical_marks + ssm.internal_marks) / ssm.max_marks * 100), 2) as overall_percentage
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id AND ssm.academic_session = s.academic_session
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE ${whereClause}
            GROUP BY s.id
            ORDER BY s.class, s.section, s.name
        `, queryParams);

        // Create Excel workbook
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Student Results');

        // Add headers
        worksheet.columns = [
            { header: 'Student ID', key: 'student_id', width: 15 },
            { header: 'Roll Number', key: 'roll_number', width: 15 },
            { header: 'Student Name', key: 'name', width: 25 },
            { header: 'Class', key: 'class', width: 10 },
            { header: 'Section', key: 'section', width: 10 },
            { header: 'Trade', key: 'trade_full_name', width: 20 },
            { header: 'Overall Percentage', key: 'overall_percentage', width: 18 }
        ];

        // Add data
        studentData.forEach(student => {
            worksheet.addRow({
                student_id: student.id,
                roll_number: student.roll_number,
                name: student.name,
                class: student.class,
                section: student.section,
                trade_full_name: student.trade_full_name,
                overall_percentage: student.overall_percentage
            });
        });

        // Style the header row
        worksheet.getRow(1).font = { bold: true };
        worksheet.getRow(1).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '2563eb' }
        };

        // Set response headers for Excel download
        const filename = `student_results_${Date.now()}.xlsx`;
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

        // Write to response
        await workbook.xlsx.write(res);
        res.end();

    } catch (error) {
        console.error('Error exporting to Excel:', error);
        res.status(500).json({ error: 'Error exporting data to Excel' });
    }
});

// Score Cards Management
router.get('/score-cards', checkExamResultsAccess, async (req, res) => {
    try {
        const selectedSession = req.session.examResultsSession;
        const { trade = 'all', class: classLevel = 'all', section = 'all' } = req.query;

        let whereConditions = ["s.academic_session = ?"];
        let queryParams = [selectedSession];

        if (trade !== 'all') {
            whereConditions.push('s.trade = ?');
            queryParams.push(trade);
        }
        if (classLevel !== 'all') {
            whereConditions.push('s.class = ?');
            queryParams.push(classLevel);
        }
        if (section !== 'all') {
            whereConditions.push('s.section = ?');
            queryParams.push(section);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get student results with subject-wise marks (same as students page)
        const [studentResults] = await db.query(`
            SELECT
                s.id as student_id,
                s.student_id as roll_number,
                s.name as student_name,
                s.class,
                s.section,
                s.trade,
                COALESCE(tm.correct_trade_name, s.trade) as trade_full_name,

                -- Subject information
                sub.code as subject_code,
                sub.name as subject_name,
                sub.subject_category_new,

                -- Include in grand total flag
                CASE
                    WHEN sub.code IN ('1', '2') THEN TRUE
                    WHEN sub.code IN ('28', '52', '53') AND s.trade = 'Non-Medical' THEN TRUE
                    WHEN sub.code IN ('141', '142', '26') AND s.trade = 'Commerce' THEN TRUE
                    WHEN sub.code = '54' AND s.trade = 'Medical' THEN TRUE
                    ELSE FALSE
                END as include_in_grand_total,

                -- Marks information
                ssm.theory_marks,
                ssm.practical_marks,
                ssm.internal_marks,
                ssm.total_marks,
                ssm.max_marks,
                ROUND((ssm.total_marks / ssm.max_marks) * 100, 2) as percentage,

                -- Grade calculation
                CASE
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 90 THEN 'A+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 80 THEN 'A'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 70 THEN 'B+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 60 THEN 'B'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 50 THEN 'C+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 40 THEN 'C'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 33 THEN 'D'
                    ELSE 'F'
                END as grade,

                -- Result status
                CASE
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 33 THEN 'PASS'
                    ELSE 'FAIL'
                END as result_status

            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id AND ssm.academic_session = s.academic_session
            LEFT JOIN subjects sub ON ssm.subject_id = sub.id
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE ${whereClause}
            ORDER BY s.class, s.section, s.name,
                     CASE
                         WHEN sub.code IN ('1', '2') THEN 1
                         WHEN sub.subject_category_new = 'science' OR sub.subject_category_new = 'commerce' THEN 2
                         ELSE 3
                     END,
                     CAST(sub.code AS UNSIGNED)
        `, queryParams);

        // Get available filters with proper trade names
        const [trades] = await db.query(`
            SELECT
                s.trade as original_trade,
                COALESCE(tm.correct_trade_name, s.trade) as trade_name,
                COALESCE(tm.correct_trade_code, s.trade) as trade_code
            FROM students s
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE s.academic_session = ?
            GROUP BY s.trade, COALESCE(tm.correct_trade_name, s.trade), COALESCE(tm.correct_trade_code, s.trade)
            ORDER BY COALESCE(tm.correct_trade_name, s.trade)
        `, [selectedSession]);

        const [classes] = await db.query(`
            SELECT DISTINCT class FROM students WHERE academic_session = ? ORDER BY class
        `, [selectedSession]);

        const [sections] = await db.query(`
            SELECT DISTINCT section FROM students WHERE academic_session = ? ORDER BY section
        `, [selectedSession]);

        // Group results by student (same logic as students page)
        const groupedResults = {};
        studentResults.forEach(result => {
            const studentKey = result.student_id;
            if (!groupedResults[studentKey]) {
                groupedResults[studentKey] = {
                    student_info: {
                        student_id: result.student_id,
                        roll_number: result.roll_number,
                        student_name: result.student_name,
                        class: result.class,
                        section: result.section,
                        trade: result.trade,
                        trade_full_name: result.trade_full_name
                    },
                    subjects: [],
                    grand_total_marks: 0,
                    grand_total_max: 0,
                    additional_marks: 0,
                    additional_max: 0
                };
            }

            if (result.subject_name) {
                groupedResults[studentKey].subjects.push(result);

                // Add to grand total if it's a core subject
                if (result.include_in_grand_total) {
                    groupedResults[studentKey].grand_total_marks += result.total_marks || 0;
                    groupedResults[studentKey].grand_total_max += result.max_marks || 0;
                } else {
                    groupedResults[studentKey].additional_marks += result.total_marks || 0;
                    groupedResults[studentKey].additional_max += result.max_marks || 0;
                }
            }
        });

        // Calculate overall percentages and grades
        Object.keys(groupedResults).forEach(studentKey => {
            const student = groupedResults[studentKey];
            if (student.grand_total_max > 0) {
                student.overall_percentage = Math.round((student.grand_total_marks / student.grand_total_max) * 100 * 100) / 100;

                // Calculate overall grade
                if (student.overall_percentage >= 90) student.overall_grade = 'A+';
                else if (student.overall_percentage >= 80) student.overall_grade = 'A';
                else if (student.overall_percentage >= 70) student.overall_grade = 'B+';
                else if (student.overall_percentage >= 60) student.overall_grade = 'B';
                else if (student.overall_percentage >= 50) student.overall_grade = 'C+';
                else if (student.overall_percentage >= 40) student.overall_grade = 'C';
                else if (student.overall_percentage >= 33) student.overall_grade = 'D';
                else student.overall_grade = 'F';

                student.promotion_status = student.overall_percentage >= 33 ? 'PROMOTED' : 'DETAINED';
            }
        });

        res.render('exam-results/score-cards', {
            title: 'Score Cards',
            layout: 'layouts/exam-results',
            currentPage: 'score-cards',
            studentResults: Object.values(groupedResults),
            filters: { trade, class: classLevel, section },
            trades,
            classes,
            sections,
            user: req.session.examResultsUser,
            examResultsSession: req.session.examResultsSession
        });
    } catch (error) {
        console.error('Error loading score cards:', error);
        req.session.flashError = 'Error loading score cards';
        res.redirect('/exam-results/dashboard');
    }
});

// Student details view (for score cards page)
router.get('/student/:studentId/details', checkExamResultsAccess, async (req, res) => {
    try {
        const studentId = req.params.studentId;
        const selectedSession = req.session.examResultsSession;

        // Get complete student information
        const [studentInfo] = await db.query(`
            SELECT
                s.*,
                COALESCE(tm.correct_trade_name, s.trade) as trade_full_name
            FROM students s
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE s.id = ? AND s.academic_session = ?
        `, [studentId, selectedSession]);

        if (studentInfo.length === 0) {
            return res.status(404).json({ error: 'Student not found' });
        }

        const student = studentInfo[0];

        // Get subject-wise marks with categorization
        const [subjectMarks] = await db.query(`
            SELECT
                sub.code as subject_code,
                sub.name as subject_name,
                sub.subject_category_new,

                -- Subject classification
                CASE
                    WHEN sub.code IN ('1', '2') THEN 'Compulsory Language'
                    WHEN sub.code IN ('28', '52', '53') AND ? = 'Non-Medical' THEN 'Core Compulsory'
                    WHEN sub.code IN ('141', '142', '26') AND ? = 'Commerce' THEN 'Core Compulsory'
                    WHEN sub.code = '54' AND ? = 'Medical' THEN 'Core Compulsory'
                    WHEN sub.code = '54' AND ? = 'Non-Medical' THEN 'Additional Optional'
                    WHEN sub.code IN ('146', '49') THEN 'Additional Compulsory'
                    ELSE 'Other'
                END as subject_classification,

                -- Include in grand total flag
                CASE
                    WHEN sub.code IN ('1', '2') THEN TRUE
                    WHEN sub.code IN ('28', '52', '53') AND ? = 'Non-Medical' THEN TRUE
                    WHEN sub.code IN ('141', '142', '26') AND ? = 'Commerce' THEN TRUE
                    WHEN sub.code = '54' AND ? = 'Medical' THEN TRUE
                    ELSE FALSE
                END as include_in_grand_total,

                ssm.theory_marks,
                ssm.practical_marks,
                ssm.internal_marks,
                ssm.total_marks,
                ssm.max_marks,
                ROUND((ssm.total_marks / ssm.max_marks) * 100, 2) as percentage,

                -- Grade calculation
                CASE
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 90 THEN 'A+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 80 THEN 'A'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 70 THEN 'B+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 60 THEN 'B'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 50 THEN 'C+'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 40 THEN 'C'
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 33 THEN 'D'
                    ELSE 'F'
                END as grade,

                CASE
                    WHEN (ssm.total_marks / ssm.max_marks) * 100 >= 33 THEN 'PASS'
                    ELSE 'FAIL'
                END as result_status

            FROM student_subject_marks ssm
            JOIN subjects sub ON ssm.subject_id = sub.id
            WHERE ssm.student_id = ? AND ssm.academic_session = ?
            ORDER BY
                CASE
                    WHEN sub.code IN ('1', '2') THEN 1
                    WHEN sub.subject_category_new IN ('science', 'commerce') THEN 2
                    ELSE 3
                END,
                CAST(sub.code AS UNSIGNED)
        `, [student.trade, student.trade, student.trade, student.trade, student.trade, student.trade, student.trade, studentId, selectedSession]);

        // Calculate grand totals
        let grandTotalMarks = 0;
        let grandTotalMax = 0;
        let additionalMarks = 0;
        let additionalMax = 0;

        subjectMarks.forEach(subject => {
            if (subject.include_in_grand_total) {
                grandTotalMarks += subject.total_marks || 0;
                grandTotalMax += subject.max_marks || 0;
            } else {
                additionalMarks += subject.total_marks || 0;
                additionalMax += subject.max_marks || 0;
            }
        });

        const overallPercentage = grandTotalMax > 0 ? Math.round((grandTotalMarks / grandTotalMax) * 100 * 100) / 100 : 0;

        let overallGrade = 'F';
        if (overallPercentage >= 90) overallGrade = 'A+';
        else if (overallPercentage >= 80) overallGrade = 'A';
        else if (overallPercentage >= 70) overallGrade = 'B+';
        else if (overallPercentage >= 60) overallGrade = 'B';
        else if (overallPercentage >= 50) overallGrade = 'C+';
        else if (overallPercentage >= 40) overallGrade = 'C';
        else if (overallPercentage >= 33) overallGrade = 'D';

        const promotionStatus = overallPercentage >= 33 ? 'PROMOTED' : 'DETAINED';

        // Return JSON for AJAX requests or render page for direct access
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
            res.json({
                student,
                subjectMarks,
                grandTotalMarks,
                grandTotalMax,
                additionalMarks,
                additionalMax,
                overallPercentage,
                overallGrade,
                promotionStatus
            });
        } else {
            // Render a detailed student view page
            res.render('exam-results/student-details', {
                title: `${student.name} - Student Details`,
                layout: 'layouts/exam-results',
                currentPage: 'students',
                student,
                subjectMarks,
                grandTotalMarks,
                grandTotalMax,
                additionalMarks,
                additionalMax,
                overallPercentage,
                overallGrade,
                promotionStatus,
                user: req.session.examResultsUser,
                examResultsSession: req.session.examResultsSession
            });
        }

    } catch (error) {
        console.error('Error loading student details:', error);
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
            res.status(500).json({ error: 'Error loading student details' });
        } else {
            req.session.flashError = 'Error loading student details';
            res.redirect('/exam-results/score-cards');
        }
    }
});

// Bulk Score Cards Generation
router.get('/bulk-scorecards', checkExamResultsAccess, async (req, res) => {
    try {
        const selectedSession = req.session.examResultsSession;
        const { trade = 'all', class: classLevel = 'all', section = 'all' } = req.query;

        let whereConditions = ["s.academic_session = ?"];
        let queryParams = [selectedSession];

        if (trade !== 'all') {
            whereConditions.push('s.trade = ?');
            queryParams.push(trade);
        }
        if (classLevel !== 'all') {
            whereConditions.push('s.class = ?');
            queryParams.push(classLevel);
        }
        if (section !== 'all') {
            whereConditions.push('s.section = ?');
            queryParams.push(section);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get all students for bulk generation
        const [students] = await db.query(`
            SELECT
                s.id as student_id,
                s.student_id as roll_number,
                s.name as student_name,
                s.class,
                s.section,
                s.trade,
                COALESCE(tm.correct_trade_name, s.trade) as trade_full_name
            FROM students s
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE ${whereClause}
            ORDER BY s.class, s.section, s.name
        `, queryParams);

        // Get available filters with proper trade names
        const [trades] = await db.query(`
            SELECT
                s.trade as original_trade,
                COALESCE(tm.correct_trade_name, s.trade) as trade_name,
                COALESCE(tm.correct_trade_code, s.trade) as trade_code
            FROM students s
            LEFT JOIN trade_mapping tm ON s.trade = tm.old_trade_value
            WHERE s.academic_session = ?
            GROUP BY s.trade, COALESCE(tm.correct_trade_name, s.trade), COALESCE(tm.correct_trade_code, s.trade)
            ORDER BY COALESCE(tm.correct_trade_name, s.trade)
        `, [selectedSession]);

        const [classes] = await db.query(`
            SELECT DISTINCT class FROM students WHERE academic_session = ? ORDER BY class
        `, [selectedSession]);

        const [sections] = await db.query(`
            SELECT DISTINCT section FROM students WHERE academic_session = ? ORDER BY section
        `, [selectedSession]);

        res.render('exam-results/bulk-scorecards', {
            title: 'Bulk Score Cards Generation',
            layout: 'layouts/exam-results',
            currentPage: 'bulk-scorecards',
            students,
            filters: { trade, class: classLevel, section },
            trades,
            classes,
            sections,
            user: req.session.examResultsUser,
            examResultsSession: req.session.examResultsSession
        });
    } catch (error) {
        console.error('Error loading bulk scorecards:', error);
        req.session.flashError = 'Error loading bulk scorecards';
        res.redirect('/exam-results/dashboard');
    }
});

// Teacher-wise Analysis
router.get('/teacher-analysis', checkExamResultsAccess, async (req, res) => {
    try {
        const selectedSession = req.session.examResultsSession;

        // Get all teachers with their basic info
        const [teachers] = await db.query(`
            SELECT DISTINCT u.id, u.name, u.full_name, u.email, u.profile_image
            FROM users u
            WHERE u.role = 'teacher' AND u.is_active = 1
            ORDER BY u.name
        `);

        // Get teacher-subject assignments with class performance
        const [teacherSubjects] = await db.query(`
            SELECT
                u.id as teacher_id,
                u.name as teacher_name,
                s.id as subject_id,
                s.name as subject_name,
                s.code as subject_code,
                st.class_level,
                st.trade,
                st.section,
                COUNT(DISTINCT stud.id) as total_students,
                AVG(COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) as avg_marks,
                AVG(((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100) as avg_percentage,
                COUNT(CASE WHEN ((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100 >= 33 THEN 1 END) as passed_students,
                COUNT(CASE WHEN ((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100 >= 75 THEN 1 END) as distinction_students
            FROM subject_teachers st
            JOIN users u ON st.teacher_id = u.id
            JOIN subjects s ON st.subject_id = s.id
            LEFT JOIN students stud ON stud.class = st.class_level
                AND stud.trade = st.trade
                AND stud.section = st.section
                AND stud.academic_session = ?
            LEFT JOIN student_subject_marks ssm ON ssm.student_id = stud.id
                AND ssm.subject_id = s.id
            WHERE st.academic_session = ? AND st.is_active = 1
            GROUP BY u.id, s.id, st.class_level, st.trade, st.section
            ORDER BY u.name, s.name, st.class_level, st.trade, st.section
        `, [selectedSession, selectedSession]);

        // Get class incharge assignments
        const [classIncharge] = await db.query(`
            SELECT
                u.id as teacher_id,
                u.name as teacher_name,
                ci.class_level,
                ci.trade,
                ci.section,
                COUNT(DISTINCT s.id) as total_students,
                AVG(
                    (SELECT AVG(((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100)
                     FROM student_subject_marks ssm
                     WHERE ssm.student_id = s.id)
                ) as class_avg_percentage,
                COUNT(CASE WHEN
                    (SELECT AVG(((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100)
                     FROM student_subject_marks ssm
                     WHERE ssm.student_id = s.id) >= 33 THEN 1 END) as passed_students
            FROM class_incharge ci
            JOIN users u ON ci.teacher_id = u.id
            LEFT JOIN students s ON s.class = ci.class_level
                AND s.trade = ci.trade
                AND s.section = ci.section
                AND s.academic_session = ?
            WHERE ci.academic_session = ? AND ci.is_active = 1
            GROUP BY u.id, ci.class_level, ci.trade, ci.section
            ORDER BY u.name, ci.class_level, ci.trade, ci.section
        `, [selectedSession, selectedSession]);

        // Process data for better organization
        const teacherAnalysis = {};

        teachers.forEach(teacher => {
            teacherAnalysis[teacher.id] = {
                ...teacher,
                subjects: [],
                classes_incharge: [],
                overall_stats: {
                    total_students: 0,
                    avg_performance: 0,
                    subjects_taught: 0,
                    classes_managed: 0
                }
            };
        });

        // Add subject teaching data
        teacherSubjects.forEach(record => {
            if (teacherAnalysis[record.teacher_id]) {
                teacherAnalysis[record.teacher_id].subjects.push({
                    subject_name: record.subject_name,
                    subject_code: record.subject_code,
                    class_level: record.class_level,
                    trade: record.trade,
                    section: record.section,
                    total_students: record.total_students || 0,
                    avg_percentage: record.avg_percentage || 0,
                    passed_students: record.passed_students || 0,
                    distinction_students: record.distinction_students || 0,
                    pass_rate: record.total_students > 0 ? ((record.passed_students || 0) / record.total_students * 100) : 0
                });
            }
        });

        // Add class incharge data
        classIncharge.forEach(record => {
            if (teacherAnalysis[record.teacher_id]) {
                teacherAnalysis[record.teacher_id].classes_incharge.push({
                    class_level: record.class_level,
                    trade: record.trade,
                    section: record.section,
                    total_students: record.total_students || 0,
                    class_avg_percentage: record.class_avg_percentage || 0,
                    passed_students: record.passed_students || 0,
                    pass_rate: record.total_students > 0 ? ((record.passed_students || 0) / record.total_students * 100) : 0
                });
            }
        });

        // Calculate overall stats for each teacher
        Object.values(teacherAnalysis).forEach(teacher => {
            const allStudents = [...teacher.subjects, ...teacher.classes_incharge];
            teacher.overall_stats.total_students = allStudents.reduce((sum, item) => sum + (item.total_students || 0), 0);
            teacher.overall_stats.subjects_taught = teacher.subjects.length;
            teacher.overall_stats.classes_managed = teacher.classes_incharge.length;

            const avgPerformances = allStudents.map(item => item.avg_percentage || item.class_avg_percentage || 0).filter(p => p > 0);
            teacher.overall_stats.avg_performance = avgPerformances.length > 0 ?
                avgPerformances.reduce((sum, p) => sum + p, 0) / avgPerformances.length : 0;
        });

        res.render('exam-results/teacher-analysis', {
            title: 'Teacher-wise Analysis',
            layout: 'layouts/exam-results',
            currentPage: 'teacher-analysis',
            teacherAnalysis: Object.values(teacherAnalysis),
            user: req.session.examResultsUser,
            examResultsSession: req.session.examResultsSession
        });
    } catch (error) {
        console.error('Error loading teacher analysis:', error);
        req.session.flashError = 'Error loading teacher analysis data';
        res.redirect('/exam-results/dashboard');
    }
});

// Apply custom ranges for score distribution
router.post('/apply-custom-ranges', checkExamResultsAccess, async (req, res) => {
    try {
        const { ranges, trade, class: classLevel, section } = req.body;
        const selectedSession = req.session.examResultsSession;

        // Validate ranges
        if (!ranges || !Array.isArray(ranges) || ranges.length === 0) {
            return res.status(400).json({ error: 'Invalid ranges provided' });
        }

        // Validate each range
        for (const range of ranges) {
            if (!range.label || typeof range.min !== 'number' || typeof range.max !== 'number') {
                return res.status(400).json({ error: 'Invalid range format' });
            }
            if (range.min >= range.max || range.min < 0 || range.max > 100) {
                return res.status(400).json({ error: 'Invalid range values' });
            }
        }

        // Save custom ranges to database
        try {
            // Start transaction
            const connection = await db.getConnection();
            await connection.beginTransaction();

            try {
                // Deactivate existing ranges for this session
                await connection.query(`
                    UPDATE custom_score_ranges
                    SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
                    WHERE academic_session = ?
                `, [selectedSession]);

                // Insert new custom ranges
                for (let i = 0; i < ranges.length; i++) {
                    const range = ranges[i];
                    await connection.query(`
                        INSERT INTO custom_score_ranges (
                            academic_session,
                            range_name,
                            min_percentage,
                            max_percentage,
                            range_label,
                            display_order,
                            is_active,
                            created_by
                        ) VALUES (?, ?, ?, ?, ?, ?, TRUE, 'user')
                    `, [
                        selectedSession,
                        `custom_${i + 1}`,
                        range.min,
                        range.max,
                        range.label,
                        i + 1
                    ]);
                }

                // Commit transaction
                await connection.commit();
                connection.release();

                console.log(`Successfully saved ${ranges.length} custom ranges to database for session ${selectedSession}`);
            } catch (error) {
                // Rollback transaction on error
                await connection.rollback();
                connection.release();
                throw error;
            }
        } catch (dbError) {
            console.error('Error saving custom ranges to database:', dbError);
            return res.status(500).json({ error: 'Error saving custom ranges to database' });
        }

        // Build query conditions
        let whereConditions = ['s.academic_session = ?'];
        let queryParams = [selectedSession];

        if (trade && trade !== 'all') {
            whereConditions.push('s.trade = ?');
            queryParams.push(trade);
        }

        if (classLevel && classLevel !== 'all') {
            whereConditions.push('s.class = ?');
            queryParams.push(classLevel);
        }

        if (section && section !== 'all') {
            whereConditions.push('s.section = ?');
            queryParams.push(section);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get student marks data
        const [studentMarks] = await db.query(`
            SELECT
                s.id,
                s.name,
                s.class,
                s.trade,
                s.section,
                AVG(((COALESCE(ssm.theory_marks, 0) + COALESCE(ssm.practical_marks, 0) + COALESCE(ssm.internal_marks, 0)) / ssm.max_marks) * 100) as percentage
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
            WHERE ${whereClause}
            GROUP BY s.id
            HAVING percentage IS NOT NULL
            ORDER BY percentage DESC
        `, queryParams);

        // Calculate distribution based on custom ranges
        const distribution = ranges.map(range => {
            const studentsInRange = studentMarks.filter(student =>
                student.percentage >= range.min && student.percentage <= range.max
            );

            return {
                min: range.min,
                max: range.max,
                label: range.label,
                count: studentsInRange.length,
                percentage: studentMarks.length > 0 ? (studentsInRange.length / studentMarks.length * 100) : 0,
                students: studentsInRange
            };
        });

        res.json({
            success: true,
            distribution: distribution,
            totalStudents: studentMarks.length
        });

    } catch (error) {
        console.error('Error applying custom ranges:', error);
        res.status(500).json({ error: 'Error processing custom ranges' });
    }
});

// Reset to default ranges
router.post('/reset-to-default-ranges', checkExamResultsAccess, async (req, res) => {
    try {
        const selectedSession = req.session.examResultsSession;

        // Reset to system default ranges by deactivating custom ranges
        await db.query(`
            UPDATE custom_score_ranges
            SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
            WHERE academic_session = ? AND created_by = 'user'
        `, [selectedSession]);

        // Reactivate system default ranges if they exist
        const [systemRanges] = await db.query(`
            SELECT COUNT(*) as count
            FROM custom_score_ranges
            WHERE academic_session = ? AND created_by = 'system' AND is_active = FALSE
        `, [selectedSession]);

        if (systemRanges[0].count > 0) {
            await db.query(`
                UPDATE custom_score_ranges
                SET is_active = TRUE, updated_at = CURRENT_TIMESTAMP
                WHERE academic_session = ? AND created_by = 'system'
            `, [selectedSession]);
        }

        console.log(`Successfully reset to default ranges for session ${selectedSession}`);

        res.json({
            success: true,
            message: 'Successfully reset to default ranges'
        });

    } catch (error) {
        console.error('Error resetting to default ranges:', error);
        res.status(500).json({ error: 'Error resetting to default ranges' });
    }
});

// Change academic session
router.post('/change-session', checkExamResultsAccess, (req, res) => {
    try {
        const { session } = req.body;

        // Validate session format
        const validSessions = ['2023-2024', '2022-2023', '2021-2022', '2020-2021'];
        if (!validSessions.includes(session)) {
            return res.status(400).json({ error: 'Invalid session selected' });
        }

        // Update session in user's session
        req.session.examResultsSession = session;

        res.json({ success: true, session: session });
    } catch (error) {
        console.error('Error changing session:', error);
        res.status(500).json({ error: 'Error changing session' });
    }
});

// Logout
router.post('/logout', (req, res) => {
    req.session.examResultsAccess = false;
    req.session.examResultsUser = null;
    req.session.flashSuccess = 'Successfully logged out';
    res.redirect('/exam-results/login');
});

module.exports = router;
