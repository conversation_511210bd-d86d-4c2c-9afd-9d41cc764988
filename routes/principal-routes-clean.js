/**
 * Principal Routes - Clean Version
 * Handles all routes for the Principal dashboard and functionality
 */

const express = require('express');
const router = express.Router();
const principalController = require('../controllers/principal-controller');
const { checkAuthenticated } = require('../middleware/auth');

// Middleware to check if user is principal
const checkPrincipal = (req, res, next) => {
  if (req.user && req.user.role === 'principal') {
    next();
  } else {
    res.status(403).render('error', { 
      message: 'Access denied. Principal access required.',
      layout: 'layouts/main'
    });
  }
};

// Apply authentication and principal check to all routes
router.use(checkAuthenticated);
router.use(checkPrincipal);

// Middleware to ensure principal layout is used
router.use((req, res, next) => {
  res.locals.layout = 'layouts/principal';
  next();
});

// Dashboard routes
router.get('/', principalController.getDashboard);
router.get('/dashboard', principalController.getDashboard);

// Student management routes
router.get('/students', principalController.getStudents);
router.get('/students/export', principalController.exportStudents);

// Teacher management routes
router.get('/teachers', principalController.getTeachers);
router.get('/teachers/export', principalController.exportTeachers);

// Academic management routes
router.get('/academics', principalController.getAcademics);
router.get('/academics/classes', principalController.getClasses);
router.get('/academics/subjects', principalController.getSubjects);
router.get('/academics/timetable', principalController.getTimetable);

// Infrastructure management routes
router.get('/infrastructure', principalController.getInfrastructure);
router.get('/infrastructure/rooms', principalController.getRooms);
router.get('/infrastructure/equipment', principalController.getEquipment);
router.get('/infrastructure/maintenance', principalController.getMaintenance);

// Reports routes
router.get('/reports', principalController.getReports);
router.get('/reports/academic', principalController.getAcademicReports);
router.get('/reports/attendance', principalController.getAttendanceReports);
router.get('/reports/performance', principalController.getPerformanceReports);

// Settings routes
router.get('/settings', principalController.getSettings);
router.get('/settings/school', principalController.getSchoolSettings);
router.get('/settings/users', principalController.getUserSettings);
router.get('/settings/system', principalController.getSystemSettings);

// Profile routes
router.get('/profile', principalController.getProfile);
router.post('/profile/update', principalController.updateProfile);

// API routes for AJAX requests
router.get('/api/dashboard-stats', principalController.getDashboardStats);
router.get('/api/academic-progress', principalController.getAcademicProgress);
router.get('/api/teacher-performance', principalController.getTeacherPerformance);

// PDF generation routes
router.post('/api/generate-teacher-cv-pdf', principalController.generateTeacherPDF);
router.post('/api/generate-student-profile-pdf', principalController.generateStudentPDF);

// Export routes
router.get('/api/export-students', principalController.exportStudentsAPI);
router.get('/api/export-teachers', principalController.exportTeachersAPI);

// Database test route (for development only)
router.get('/api/test-db', async (req, res) => {
  try {
    const db = require('../config/database');
    const [result] = await db.query('SELECT 1 as test');
    res.json({
      success: true,
      message: 'Database connection successful',
      result: result[0]
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Database connection failed',
      error: error.message
    });
  }
});

module.exports = router;
