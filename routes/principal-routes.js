/**
 * Principal Routes
 * Handles all routes for the Principal dashboard and functionality
 */

const express = require('express');
const router = express.Router();
const principalController = require('../controllers/principal-controller');
const { checkAuthenticated } = require('../middleware/auth');

// Middleware to check if user is principal or admin
const checkPrincipal = (req, res, next) => {
  if (req.session.userRole === 'admin' || req.session.userRole === 'principal') {
    return next();
  }

  res.status(403).render('error', {
    title: 'Access Denied',
    message: 'You do not have permission to access the Principal dashboard. This feature requires principal or admin privileges.',
    error: { status: 403 },
    layout: 'layouts/main'
  });
};

// Apply authentication middleware to all routes
router.use(checkAuthenticated);
router.use(checkPrincipal);

// Middleware to ensure principal layout is used
router.use((req, res, next) => {
  res.locals.layout = 'layouts/principal';
  next();
});

// Dashboard
router.get('/dashboard', principalController.getDashboard);
router.get('/', principalController.getDashboard);

// Academic Progress
router.get('/academic-progress', principalController.getAcademicProgress);

// Teacher Details
router.get('/teacher-details', principalController.getTeacherManagement);
router.get('/teacher-timetables', principalController.getTeacherTimetables);
router.get('/teacher/:id', principalController.getTeacherDetails);

// Enhanced teacher profile API (accessible to principal)
const teacherProfileEnhancedApi = require('./api/teacher-profile-enhanced-api');
router.use('/api/teacher', teacherProfileEnhancedApi);

// PDF Generation APIs
const PDFDocument = require('pdfkit');
const path = require('path');
const fs = require('fs');

// Test PDF generation endpoint
router.post('/api/generate-test-pdf', async (req, res) => {
  try {
    console.log('🔄 Generating test PDF...');
    const { data } = req.body;

    // Generate unique filename
    const timestamp = Date.now();
    const filename = `test_pdf_${timestamp}.pdf`;
    const outputDir = path.join(__dirname, '../public/uploads/pdf');
    const outputPath = path.join(outputDir, filename);

    // Ensure directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Create PDF document
    const doc = new PDFDocument({ margin: 50 });
    const writeStream = fs.createWriteStream(outputPath);
    doc.pipe(writeStream);

    // Add content
    doc.fontSize(20).text('PDF Generation Test', { align: 'center' });
    doc.moveDown();
    doc.fontSize(14).text(`Generated at: ${data.generatedAt}`);
    doc.moveDown();
    doc.fontSize(12).text(data.content);
    doc.moveDown();
    doc.text(`System: ${data.system}`);
    doc.moveDown(2);
    doc.fontSize(10).text('This PDF was generated using PDFKit on the server-side, following the same pattern as other PDF generation in the application.');

    // Finalize PDF
    doc.end();

    // Wait for PDF to be written
    await new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });

    // Return public URL
    const publicUrl = `/uploads/pdf/${filename}`;
    console.log('✅ Test PDF generated:', publicUrl);

    res.json({ success: true, url: publicUrl });
  } catch (error) {
    console.error('❌ Error generating test PDF:', error);
    res.status(500).json({ success: false, message: 'Failed to generate test PDF' });
  }
});

// Teacher CV PDF generation endpoint
router.post('/api/generate-teacher-cv-pdf', async (req, res) => {
  try {
    console.log('🔄 Generating teacher CV PDF...');
    const { teacherId, teacherData } = req.body;

    // Generate unique filename
    const timestamp = Date.now();
    const teacherName = (teacherData.name || 'Teacher').replace(/[^a-zA-Z0-9]/g, '_');
    const filename = `${teacherName}_CV_${timestamp}.pdf`;
    const outputDir = path.join(__dirname, '../public/uploads/pdf');
    const outputPath = path.join(outputDir, filename);

    // Ensure directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Create PDF document
    const doc = new PDFDocument({ margin: 50 });
    const writeStream = fs.createWriteStream(outputPath);
    doc.pipe(writeStream);

    // Add header
    doc.fontSize(24).text('CURRICULUM VITAE', { align: 'center' });
    doc.moveDown();
    doc.fontSize(18).text(teacherData.name || 'Teacher Name', { align: 'center' });
    doc.fontSize(14).text(teacherData.designation || 'Teacher', { align: 'center' });
    doc.fontSize(12).text(teacherData.department || 'Academic Department', { align: 'center' });
    doc.moveDown(2);

    // Personal Information Section
    doc.fontSize(16).text('PERSONAL INFORMATION', { underline: true });
    doc.moveDown();
    doc.fontSize(12);
    doc.text(`Employee ID: ${teacherData.employee_id || 'N/A'}`);
    doc.text(`Email: ${teacherData.email || 'N/A'}`);
    doc.text(`Phone: ${teacherData.phone || 'N/A'}`);
    doc.text(`Date of Birth: ${teacherData.date_of_birth || 'N/A'}`);
    doc.text(`Gender: ${teacherData.gender || 'N/A'}`);
    doc.moveDown();

    // Professional Experience Section
    doc.fontSize(16).text('PROFESSIONAL EXPERIENCE', { underline: true });
    doc.moveDown();
    doc.fontSize(12);
    doc.text(`Total Experience: ${teacherData.total_experience_years || 0} years`);
    doc.text(`Teaching Experience: ${teacherData.teaching_experience_years || 0} years`);
    doc.text(`Subjects Taught: ${teacherData.subjects_taught || 'N/A'}`);
    doc.moveDown();

    // Administrative Details Section
    doc.fontSize(16).text('ADMINISTRATIVE DETAILS', { underline: true });
    doc.moveDown();
    doc.fontSize(12);
    doc.text(`Joining Date: ${teacherData.joining_date || 'N/A'}`);
    doc.text(`Employment Type: ${teacherData.employment_type || 'N/A'}`);
    doc.text(`Office Location: ${teacherData.office_location || 'N/A'}`);
    doc.text(`Performance Rating: ${teacherData.performance_rating || 'N/A'}`);
    doc.text(`Account Status: ${teacherData.account_status || 'N/A'}`);
    doc.moveDown();

    // Add achievements if available
    if (teacherData.totalAchievements && teacherData.totalAchievements > 0) {
      doc.fontSize(16).text('ACHIEVEMENTS', { underline: true });
      doc.moveDown();
      doc.fontSize(12);
      doc.text(`Total Achievements: ${teacherData.totalAchievements}`);
      doc.moveDown();
    }

    // Add certifications if available
    if (teacherData.totalCertifications && teacherData.totalCertifications > 0) {
      doc.fontSize(16).text('CERTIFICATIONS', { underline: true });
      doc.moveDown();
      doc.fontSize(12);
      doc.text(`Total Certifications: ${teacherData.totalCertifications}`);
      doc.moveDown();
    }

    // Footer
    doc.moveDown(2);
    doc.fontSize(10).text(`Generated on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, { align: 'center' });
    doc.text('Teacher Management System - Senior Secondary Residential School for Meritorious Students', { align: 'center' });

    // Finalize PDF
    doc.end();

    // Wait for PDF to be written
    await new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });

    // Return public URL
    const publicUrl = `/uploads/pdf/${filename}`;
    console.log('✅ Teacher CV PDF generated:', publicUrl);

    res.json({ success: true, url: publicUrl });
  } catch (error) {
    console.error('❌ Error generating teacher CV PDF:', error);
    res.status(500).json({ success: false, message: 'Failed to generate teacher CV PDF' });
  }
});

// Enhanced CV PDF generation endpoint using EJS template
router.post('/api/generate-enhanced-cv-pdf', async (req, res) => {
  try {
    console.log('🔄 Generating enhanced CV PDF...');
    console.log('Request body keys:', Object.keys(req.body));
    const { teacherId, teacherData } = req.body;

    if (!teacherData) {
      console.error('❌ No teacher data provided in request');
      return res.status(400).json({
        success: false,
        message: 'Teacher data is required'
      });
    }

    console.log('📋 Teacher data summary:');
    console.log(`- Name: ${teacherData.name || 'N/A'}`);
    console.log(`- Education entries: ${teacherData.educationTimeline?.length || 0}`);
    console.log(`- Experience entries: ${teacherData.experienceTimeline?.length || 0}`);
    console.log(`- Skill categories: ${Object.keys(teacherData.skillsByCategory || {}).length}`);
    console.log(`- Achievement categories: ${Object.keys(teacherData.achievementsByCategory || {}).length}`);

    // Generate unique filename
    const timestamp = Date.now();
    const teacherName = (teacherData.name || 'Teacher').replace(/[^a-zA-Z0-9]/g, '_');
    const filename = `${teacherName}_Enhanced_CV_${timestamp}.pdf`;
    const outputDir = path.join(__dirname, '../public/uploads/pdf');
    const outputPath = path.join(outputDir, filename);
    const templatePath = path.join(__dirname, '../views/cv/cv_template.ejs');

    console.log('📁 File paths:');
    console.log(`- Template: ${templatePath}`);
    console.log(`- Output: ${outputPath}`);

    // Ensure directory exists
    if (!fs.existsSync(outputDir)) {
      console.log('📁 Creating output directory...');
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Check if template exists
    if (!fs.existsSync(templatePath)) {
      console.error('❌ CV template not found at:', templatePath);
      return res.status(500).json({
        success: false,
        message: 'CV template file not found'
      });
    }

    // Use the PDF generator utility
    const { generatePDF } = require('../utils/pdf-generator');

    // Prepare data for template with comprehensive defaults
    const templateData = {
      teacher: {
        ...teacherData,
        // Ensure required fields have defaults
        name: teacherData.name || 'Teacher Name',
        designation: teacherData.designation || 'Teacher',
        department: teacherData.department || 'Academic Department',
        employee_id: teacherData.employee_id || `EMP${String(teacherId || '0000').padStart(4, '0')}`,
        email: teacherData.email || 'Not provided',
        phone: teacherData.phone || 'Not provided',
        // Format dates properly
        date_of_birth: teacherData.date_of_birth ? new Date(teacherData.date_of_birth) : null,
        joining_date: teacherData.joining_date ? new Date(teacherData.joining_date) : null,
        confirmation_date: teacherData.confirmation_date ? new Date(teacherData.confirmation_date) : null,
        // Ensure arrays exist
        experienceTimeline: teacherData.experienceTimeline || [],
        educationTimeline: teacherData.educationTimeline || [],
        achievementsByCategory: teacherData.achievementsByCategory || {},
        skillsByCategory: teacherData.skillsByCategory || {},
        certifications: teacherData.certifications || [],
        // Additional fields with defaults
        total_experience_years: teacherData.total_experience_years || 0,
        teaching_experience_years: teacherData.teaching_experience_years || 0,
        subjects_taught: teacherData.subjects_taught || 'Not specified',
        office_location: teacherData.office_location || 'Not specified',
        employment_type: teacherData.employment_type || 'Not specified',
        performance_rating: teacherData.performance_rating || 'Not rated',
        account_status: teacherData.account_status || 'Active'
      }
    };

    console.log('🔄 Generating PDF with template...');

    // Generate PDF using the template
    await generatePDF(templateData, templatePath, outputPath);

    // Return success response
    const pdfUrl = `/uploads/pdf/${filename}`;
    console.log('✅ Enhanced CV PDF generated successfully:', pdfUrl);

    res.json({
      success: true,
      message: 'Enhanced CV PDF generated successfully',
      url: pdfUrl,
      filename: filename
    });

  } catch (error) {
    console.error('❌ Error generating enhanced CV PDF:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Error generating enhanced CV PDF',
      error: error.message
    });
  }
});

// Student Profile PDF generation endpoint using EJS template
router.post('/api/generate-student-profile-pdf', async (req, res) => {
  try {
    console.log('🔄 Generating student profile PDF...');
    console.log('Request body keys:', Object.keys(req.body));
    const { studentId, studentData } = req.body;

    if (!studentData) {
      console.error('❌ No student data provided in request');
      return res.status(400).json({
        success: false,
        message: 'Student data is required'
      });
    }

    console.log('📋 Student data summary:');
    console.log(`- Name: ${studentData.name || 'N/A'}`);
    console.log(`- Student ID: ${studentData.student_id || 'N/A'}`);
    console.log(`- Class: ${studentData.class || 'N/A'}`);

    // Generate unique filename
    const timestamp = Date.now();
    const studentName = (studentData.name || 'Student').replace(/[^a-zA-Z0-9]/g, '_');
    const filename = `${studentName}_Profile_${timestamp}.pdf`;
    const outputDir = path.join(__dirname, '../public/uploads/pdf');
    const outputPath = path.join(outputDir, filename);
    const templatePath = path.join(__dirname, '../views/cv/student_profile_template.ejs');

    console.log('📁 File paths:');
    console.log(`- Template: ${templatePath}`);
    console.log(`- Output: ${outputPath}`);

    // Ensure directory exists
    if (!fs.existsSync(outputDir)) {
      console.log('📁 Creating output directory...');
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Check if template exists
    if (!fs.existsSync(templatePath)) {
      console.error('❌ Student profile template not found at:', templatePath);
      return res.status(500).json({
        success: false,
        message: 'Student profile template file not found'
      });
    }

    // Use the PDF generator utility
    const { generatePDF } = require('../utils/pdf-generator');

    // Prepare data for template with comprehensive defaults
    const templateData = {
      student: {
        ...studentData,
        // Ensure required fields have defaults
        name: studentData.name || 'Student Name',
        student_id: studentData.student_id || 'N/A',
        class: studentData.class || 'N/A',
        section: studentData.section || 'N/A',
        session: studentData.session || 'N/A',
        // Format dates properly
        dob: studentData.dob ? new Date(studentData.dob) : null,
        admission_date: studentData.admission_date ? new Date(studentData.admission_date) : null,
        // Ensure all fields have defaults
        father_name: studentData.father_name || 'N/A',
        mother_name: studentData.mother_name || 'N/A',
        gender: studentData.gender || 'N/A',
        religion_name: studentData.religion_name || 'N/A',
        caste_category_name: studentData.caste_category_name || 'N/A',
        contact_no: studentData.contact_no || 'N/A',
        cur_address: studentData.cur_address || 'N/A',
        village_ward: studentData.village_ward || 'N/A',
        district_name: studentData.district_name || 'N/A',
        state_name: studentData.state_name || 'N/A'
      }
    };

    console.log('🔄 Generating PDF with template...');

    // Generate PDF using the template
    await generatePDF(templateData, templatePath, outputPath);

    // Return success response
    const pdfUrl = `/uploads/pdf/${filename}`;
    console.log('✅ Student profile PDF generated successfully:', pdfUrl);

    res.json({
      success: true,
      message: 'Student profile PDF generated successfully',
      url: pdfUrl,
      filename: filename
    });

  } catch (error) {
    console.error('❌ Error generating student profile PDF:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Error generating student profile PDF',
      error: error.message
    });
  }
});

// Test database connection endpoint
router.get('/api/test-db', async (req, res) => {
  try {
    const db = require('../config/database');
    console.log('🔍 Testing database connection...');

    const testQuery = 'SELECT COUNT(*) as count FROM students WHERE is_active = 1';
    console.log('Test query:', testQuery);

    const result = await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        console.error('❌ Test query timeout');
        reject(new Error('Test query timeout'));
      }, 10000);

      db.query(testQuery, [], (err, results) => {
        clearTimeout(timeout);
        if (err) {
          console.error('❌ Test query error:', err);
          reject(err);
        } else {
          console.log('✅ Test query successful:', results);
          resolve(results);
        }
      });
    });

    res.json({
      success: true,
      message: 'Database connection test successful',
      count: result[0].count
    });

  } catch (error) {
    console.error('❌ Database test failed:', error);
    res.status(500).json({
      success: false,
      message: 'Database test failed: ' + error.message
    });
  }
});

// Test student data endpoint
router.get('/api/test-student/:id', async (req, res) => {
  try {
    const db = require('../config/database');
    const studentId = req.params.id;

    console.log(`🔍 Testing student data for ID: ${studentId}`);

    // Test basic student query
    const [students] = await db.query('SELECT * FROM students WHERE id = ? LIMIT 1', [studentId]);

    // Check what tables exist
    const [tables] = await db.query("SHOW TABLES LIKE '%subject%'");
    const [studentTables] = await db.query("SHOW TABLES LIKE '%student%'");

    // Test different possible subject relationship queries
    let subjectQueries = {};

    // Try student_subject_marks table without session
    try {
      const [ssm] = await db.query(`
        SELECT sub.id, sub.code, sub.name, ssm.total_marks, ssm.max_marks, ssm.academic_session
        FROM student_subject_marks ssm
        JOIN subjects sub ON ssm.subject_id = sub.id
        WHERE ssm.student_id = ?
        LIMIT 5
      `, [studentId]);
      subjectQueries.student_subject_marks_no_session = ssm;
    } catch (e) {
      subjectQueries.student_subject_marks_no_session = { error: e.message };
    }

    // Try student_subject_marks table with session
    try {
      const studentSession = students[0]?.academic_session || students[0]?.session || '2023-2024';
      const [ssm] = await db.query(`
        SELECT sub.id, sub.code, sub.name, ssm.total_marks, ssm.max_marks, ssm.academic_session
        FROM student_subject_marks ssm
        JOIN subjects sub ON ssm.subject_id = sub.id
        WHERE ssm.student_id = ? AND ssm.academic_session = ?
        LIMIT 5
      `, [studentId, studentSession]);
      subjectQueries.student_subject_marks_with_session = {
        session_used: studentSession,
        results: ssm
      };
    } catch (e) {
      subjectQueries.student_subject_marks_with_session = { error: e.message };
    }

    // Try student_trade_subject table if it exists
    try {
      const [sts] = await db.query(`
        SELECT sub.id, sub.code, sub.name, sts.*
        FROM student_trade_subject sts
        JOIN subjects sub ON sts.subject_id = sub.id
        WHERE sts.student_id = ?
        LIMIT 5
      `, [studentId]);
      subjectQueries.student_trade_subject = sts;
    } catch (e) {
      subjectQueries.student_trade_subject = { error: e.message };
    }

    // Try student_subjects table if it exists
    try {
      const [ss] = await db.query(`
        SELECT sub.id, sub.code, sub.name, ss.*
        FROM student_subjects ss
        JOIN subjects sub ON ss.subject_id = sub.id
        WHERE ss.student_id = ?
        LIMIT 5
      `, [studentId]);
      subjectQueries.student_subjects = ss;
    } catch (e) {
      subjectQueries.student_subjects = { error: e.message };
    }

    // Check subjects table structure
    const [subjectColumns] = await db.query("DESCRIBE subjects");

    // Check if there are any subjects at all
    const [allSubjects] = await db.query("SELECT id, code, name FROM subjects LIMIT 10");

    // Check available academic sessions in student_subject_marks
    const [availableSessions] = await db.query(`
      SELECT academic_session, COUNT(*) as count
      FROM student_subject_marks
      GROUP BY academic_session
      ORDER BY count DESC
    `);

    // Check what sessions this specific student has
    const [studentSessions] = await db.query(`
      SELECT DISTINCT academic_session, COUNT(*) as subject_count
      FROM student_subject_marks
      WHERE student_id = ?
      GROUP BY academic_session
    `, [studentId]);

    // Get detailed subject info for this student
    const [studentSubjectDetails] = await db.query(`
      SELECT
        sub.id, sub.code, sub.name, sub.stream, sub.subject_category_new,
        ssm.theory_marks, ssm.practical_marks, ssm.internal_marks,
        ssm.total_marks, ssm.max_marks, ssm.academic_session
      FROM student_subject_marks ssm
      JOIN subjects sub ON ssm.subject_id = sub.id
      WHERE ssm.student_id = ?
      ORDER BY sub.code
    `, [studentId]);

    res.json({
      success: true,
      student: students[0] || null,
      tables: {
        subject_related: tables,
        student_related: studentTables
      },
      subject_queries: subjectQueries,
      subject_table_structure: subjectColumns,
      sample_subjects: allSubjects,
      available_sessions: availableSessions,
      student_sessions: studentSessions,
      student_subject_details: studentSubjectDetails,
      message: `Found ${students.length} student(s). Tested multiple subject relationship queries.`
    });

  } catch (error) {
    console.error('❌ Test student data failed:', error);
    res.status(500).json({
      success: false,
      message: 'Test failed',
      error: error.message
    });
  }
});

// Test subject classification for a specific student
router.get('/api/test-classification/:id', async (req, res) => {
  try {
    const db = require('../config/database');
    const studentId = req.params.id;

    // Get student info
    const [students] = await db.query('SELECT * FROM students WHERE id = ?', [studentId]);
    if (students.length === 0) {
      return res.json({ error: 'Student not found' });
    }

    const student = students[0];
    const academicSession = student.academic_session || student.session || '2023-2024';

    // Get subjects
    const [subjects] = await db.query(`
      SELECT sub.id, sub.code, sub.name, sub.stream, ssm.total_marks, ssm.max_marks
      FROM student_subject_marks ssm
      JOIN subjects sub ON ssm.subject_id = sub.id
      WHERE ssm.student_id = ? AND ssm.academic_session = ?
    `, [studentId, academicSession]);

    // Apply classification logic
    const classifiedSubjects = subjects.map(subject => {
      const code = subject.code;
      const subjectName = subject.name.toLowerCase();
      const stream = subject.stream;

      let subjectClassification = 'Other';
      let includeInGrandTotal = false;

      // Language subjects
      if (['1', '2'].includes(code) ||
          subjectName.includes('english') ||
          subjectName.includes('punjabi') ||
          subjectName.includes('hindi')) {
        subjectClassification = 'Compulsory Language';
        includeInGrandTotal = true;
      }
      // Science subjects
      else if (['28', '52', '53', '54'].includes(code) ||
               subjectName.includes('physics') ||
               subjectName.includes('chemistry') ||
               subjectName.includes('mathematics') ||
               subjectName.includes('biology')) {
        subjectClassification = 'Core Science';
        includeInGrandTotal = true;
      }
      // Commerce subjects
      else if (['141', '142', '26'].includes(code) ||
               subjectName.includes('commerce') ||
               subjectName.includes('economics') ||
               subjectName.includes('accountancy')) {
        subjectClassification = 'Core Commerce';
        includeInGrandTotal = true;
      }
      // Default: if stream indicates main subject
      else if (stream && (stream.includes('Science') || stream.includes('Commerce') || stream.includes('Humanities') || stream.includes('All Streams'))) {
        subjectClassification = 'Core Subject';
        includeInGrandTotal = true;
      }

      return {
        ...subject,
        classification: subjectClassification,
        is_core: includeInGrandTotal
      };
    });

    const coreSubjects = classifiedSubjects.filter(s => s.is_core);
    const additionalSubjects = classifiedSubjects.filter(s => !s.is_core);

    res.json({
      success: true,
      student: { name: student.name, trade: student.trade, class: student.class },
      session_used: academicSession,
      all_subjects: classifiedSubjects,
      core_subjects: coreSubjects,
      additional_subjects: additionalSubjects,
      summary: {
        total_subjects: classifiedSubjects.length,
        core_count: coreSubjects.length,
        additional_count: additionalSubjects.length
      }
    });

  } catch (error) {
    console.error('❌ Classification test failed:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Display all students with their trades and subjects
router.get('/api/all-students-subjects', async (req, res) => {
  try {
    const db = require('../config/database');

    console.log('🔍 Fetching all students with their trades and subjects...');

    // Get all students with their subjects
    const [studentSubjects] = await db.query(`
      SELECT
        s.id as student_id,
        s.student_id as roll_number,
        s.name as student_name,
        s.class,
        s.section,
        s.trade as original_trade,
        s.academic_session as student_session,
        sub.id as subject_id,
        sub.code as subject_code,
        sub.name as subject_name,
        sub.stream,
        sub.subject_category_new,
        ssm.theory_marks,
        ssm.practical_marks,
        ssm.internal_marks,
        ssm.total_marks,
        ssm.max_marks,
        ssm.academic_session as marks_session,
        CASE
          WHEN s.class = '10' THEN 'General'
          WHEN s.trade = 'Biology' THEN 'Medical'
          WHEN s.trade IN ('Physics', 'Chemistry', 'Mathematics', 'Computer Science') THEN 'Non Medical'
          WHEN s.trade IN ('Commerce', 'Economics', 'Accountancy') THEN 'Commerce'
          WHEN s.trade IN ('History', 'Geography', 'Political Science', 'Psychology', 'Sociology', 'Philosophy') THEN 'Humanities'
          ELSE s.trade
        END as trade_category
      FROM students s
      LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
      LEFT JOIN subjects sub ON ssm.subject_id = sub.id
      WHERE s.is_active = 1 AND s.class IN ('10', '11', '12')
      ORDER BY s.class, s.section, s.name, sub.code
    `);

    // Group by students
    const studentsMap = {};
    studentSubjects.forEach(row => {
      const studentKey = row.student_id;

      if (!studentsMap[studentKey]) {
        studentsMap[studentKey] = {
          student_id: row.student_id,
          roll_number: row.roll_number,
          student_name: row.student_name,
          class: row.class,
          section: row.section,
          original_trade: row.original_trade,
          trade_category: row.trade_category,
          student_session: row.student_session,
          subjects: []
        };
      }

      // Add subject if it exists
      if (row.subject_id) {
        studentsMap[studentKey].subjects.push({
          subject_id: row.subject_id,
          subject_code: row.subject_code,
          subject_name: row.subject_name,
          stream: row.stream,
          subject_category_new: row.subject_category_new,
          theory_marks: row.theory_marks,
          practical_marks: row.practical_marks,
          internal_marks: row.internal_marks,
          total_marks: row.total_marks,
          max_marks: row.max_marks,
          marks_session: row.marks_session
        });
      }
    });

    const students = Object.values(studentsMap);

    // Generate summary statistics
    const summary = {
      total_students: students.length,
      students_with_subjects: students.filter(s => s.subjects.length > 0).length,
      students_without_subjects: students.filter(s => s.subjects.length === 0).length,
      trade_distribution: {},
      subject_codes_found: new Set(),
      sessions_found: new Set()
    };

    students.forEach(student => {
      // Trade distribution
      if (!summary.trade_distribution[student.trade_category]) {
        summary.trade_distribution[student.trade_category] = 0;
      }
      summary.trade_distribution[student.trade_category]++;

      // Collect subject codes and sessions
      student.subjects.forEach(subject => {
        summary.subject_codes_found.add(subject.subject_code);
        summary.sessions_found.add(subject.marks_session);
      });

      if (student.student_session) {
        summary.sessions_found.add(student.student_session);
      }
    });

    // Convert sets to arrays for JSON response
    summary.subject_codes_found = Array.from(summary.subject_codes_found).sort();
    summary.sessions_found = Array.from(summary.sessions_found).sort();

    console.log(`✅ Found ${students.length} students`);
    console.log(`📊 Students with subjects: ${summary.students_with_subjects}`);
    console.log(`📊 Students without subjects: ${summary.students_without_subjects}`);
    console.log(`📚 Subject codes found: ${summary.subject_codes_found.join(', ')}`);
    console.log(`📅 Sessions found: ${summary.sessions_found.join(', ')}`);

    res.json({
      success: true,
      summary,
      students: students.slice(0, 20), // Limit to first 20 for readability
      total_students: students.length,
      message: `Found ${students.length} students. Showing first 20 with their trades and subjects.`
    });

  } catch (error) {
    console.error('❌ Error fetching students and subjects:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching data',
      error: error.message
    });
  }
});

// Simple summary of students, trades and subjects
router.get('/api/students-trades-summary', async (req, res) => {
  try {
    const db = require('../config/database');

    console.log('📋 Generating students-trades-subjects summary...');

    // Get summary data
    const [studentTradeSubjects] = await db.query(`
      SELECT
        s.name as student_name,
        s.class,
        s.section,
        s.trade,
        s.academic_session,
        COUNT(DISTINCT ssm.subject_id) as subject_count,
        GROUP_CONCAT(DISTINCT CONCAT(sub.code, ':', sub.name) ORDER BY sub.code SEPARATOR ' | ') as subjects_list
      FROM students s
      LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
      LEFT JOIN subjects sub ON ssm.subject_id = sub.id
      WHERE s.is_active = 1 AND s.class IN ('10', '11', '12')
      GROUP BY s.id, s.name, s.class, s.section, s.trade, s.academic_session
      ORDER BY s.class, s.section, s.name
    `);

    // Get all unique subject codes with names
    const [allSubjects] = await db.query(`
      SELECT DISTINCT sub.code, sub.name, sub.stream, sub.subject_category_new
      FROM subjects sub
      JOIN student_subject_marks ssm ON sub.id = ssm.subject_id
      ORDER BY CAST(sub.code AS UNSIGNED)
    `);

    // Get trade distribution
    const [tradeDistribution] = await db.query(`
      SELECT
        trade,
        COUNT(*) as student_count,
        GROUP_CONCAT(DISTINCT class ORDER BY class) as classes
      FROM students
      WHERE is_active = 1 AND class IN ('10', '11', '12')
      GROUP BY trade
      ORDER BY student_count DESC
    `);

    res.json({
      success: true,
      summary: {
        total_students: studentTradeSubjects.length,
        students_with_subjects: studentTradeSubjects.filter(s => s.subject_count > 0).length,
        students_without_subjects: studentTradeSubjects.filter(s => s.subject_count === 0).length
      },
      trade_distribution: tradeDistribution,
      all_subject_codes: allSubjects,
      student_details: studentTradeSubjects,
      message: 'Complete summary of students, trades, and subjects'
    });

  } catch (error) {
    console.error('❌ Error generating summary:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating summary',
      error: error.message
    });
  }
});

// Execute schema migration and create views
router.get('/api/execute-schema-migration', async (req, res) => {
  try {
    const db = require('../config/database');

    console.log('🔄 Starting schema migration...');

    const migrationSteps = [];

    // Step 1: Create TRADES table
    try {
      await db.query(`
        CREATE TABLE IF NOT EXISTS trades (
          id INT AUTO_INCREMENT PRIMARY KEY,
          trade_code VARCHAR(20) UNIQUE NOT NULL,
          trade_name VARCHAR(100) NOT NULL,
          trade_category ENUM('Medical', 'Non Medical', 'Commerce', 'Humanities', 'General', 'Vocational') NOT NULL,
          applicable_classes VARCHAR(50) NOT NULL DEFAULT '11,12',
          description TEXT,
          is_active BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

          INDEX idx_trade_code (trade_code),
          INDEX idx_trade_category (trade_category),
          INDEX idx_applicable_classes (applicable_classes)
        )
      `);
      migrationSteps.push('✅ Created TRADES table');
    } catch (error) {
      migrationSteps.push(`❌ TRADES table error: ${error.message}`);
    }

    // Step 2: Create TRADE_SUBJECTS table
    try {
      await db.query(`
        CREATE TABLE IF NOT EXISTS trade_subjects (
          id INT AUTO_INCREMENT PRIMARY KEY,
          trade_id INT NOT NULL,
          subject_id INT NOT NULL,
          subject_classification ENUM('Compulsory Language', 'Core Science', 'Core Medical', 'Core Commerce', 'Core Humanities', 'Additional Compulsory', 'Elective', 'Optional') NOT NULL,
          is_compulsory BOOLEAN DEFAULT TRUE,
          include_in_grand_total BOOLEAN DEFAULT TRUE,
          display_order INT DEFAULT 0,
          academic_session VARCHAR(20) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

          UNIQUE KEY unique_trade_subject_session (trade_id, subject_id, academic_session),
          INDEX idx_trade_id (trade_id),
          INDEX idx_subject_id (subject_id),
          INDEX idx_academic_session (academic_session),
          INDEX idx_classification (subject_classification)
        )
      `);
      migrationSteps.push('✅ Created TRADE_SUBJECTS table');
    } catch (error) {
      migrationSteps.push(`❌ TRADE_SUBJECTS table error: ${error.message}`);
    }

    // Step 3: Create STUDENT_ELECTIVE_OPTIONAL table
    try {
      await db.query(`
        CREATE TABLE IF NOT EXISTS student_elective_optional (
          id INT AUTO_INCREMENT PRIMARY KEY,
          student_id INT NOT NULL,
          subject_id INT NOT NULL,
          selection_type ENUM('Elective', 'Optional', 'Additional') NOT NULL,
          academic_session VARCHAR(20) NOT NULL,
          selected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

          UNIQUE KEY unique_student_subject_session (student_id, subject_id, academic_session),
          INDEX idx_student_id (student_id),
          INDEX idx_subject_id (subject_id),
          INDEX idx_selection_type (selection_type),
          INDEX idx_academic_session (academic_session)
        )
      `);
      migrationSteps.push('✅ Created STUDENT_ELECTIVE_OPTIONAL table');
    } catch (error) {
      migrationSteps.push(`❌ STUDENT_ELECTIVE_OPTIONAL table error: ${error.message}`);
    }

    res.json({
      success: true,
      message: 'Schema migration executed',
      steps: migrationSteps
    });

  } catch (error) {
    console.error('❌ Schema migration failed:', error);
    res.status(500).json({
      success: false,
      message: 'Schema migration failed',
      error: error.message
    });
  }
});

// Insert sample trade data
router.get('/api/insert-sample-trades', async (req, res) => {
  try {
    const db = require('../config/database');

    console.log('🔄 Inserting sample trade data...');

    const trades = [
      ['MED', 'Medical', 'Medical', '11,12', 'Medical stream with Biology as core subject'],
      ['NM', 'Non Medical', 'Non Medical', '11,12', 'Non Medical stream with Physics, Chemistry, Mathematics'],
      ['COM', 'Commerce', 'Commerce', '11,12', 'Commerce stream with business subjects'],
      ['HUM', 'Humanities', 'Humanities', '11,12', 'Humanities stream with arts subjects'],
      ['GEN', 'General', 'General', '10', 'General education for class 10'],
      ['CS', 'Computer Science', 'Non Medical', '11,12', 'Non Medical with Computer Science'],
      ['BIO_NM', 'Biology Non Medical', 'Non Medical', '11,12', 'Non Medical with Biology as additional']
    ];

    const insertResults = [];

    for (const trade of trades) {
      try {
        await db.query(`
          INSERT IGNORE INTO trades (trade_code, trade_name, trade_category, applicable_classes, description)
          VALUES (?, ?, ?, ?, ?)
        `, trade);
        insertResults.push(`✅ Inserted trade: ${trade[1]}`);
      } catch (error) {
        insertResults.push(`❌ Failed to insert ${trade[1]}: ${error.message}`);
      }
    }

    res.json({
      success: true,
      message: 'Sample trades inserted',
      results: insertResults
    });

  } catch (error) {
    console.error('❌ Trade insertion failed:', error);
    res.status(500).json({
      success: false,
      message: 'Trade insertion failed',
      error: error.message
    });
  }
});

// Create trade-subject mappings
router.get('/api/create-trade-subject-mappings', async (req, res) => {
  try {
    const db = require('../config/database');

    console.log('🔄 Creating trade-subject mappings...');

    const mappingResults = [];

    // Medical Trade Subjects
    try {
      await db.query(`
        INSERT IGNORE INTO trade_subjects (trade_id, subject_id, subject_classification, is_compulsory, include_in_grand_total, academic_session, display_order)
        SELECT
          t.id,
          s.id,
          CASE
            WHEN s.code IN ('1', '2') THEN 'Compulsory Language'
            WHEN s.code = '54' THEN 'Core Medical'
            WHEN s.code IN ('28', '52') THEN 'Core Science'
            WHEN s.code IN ('146', '49') THEN 'Additional Compulsory'
            ELSE 'Core Science'
          END,
          CASE WHEN s.code IN ('1', '2', '54', '28', '52') THEN TRUE ELSE FALSE END,
          CASE WHEN s.code IN ('1', '2', '54', '28', '52') THEN TRUE ELSE FALSE END,
          '2023-2024',
          CASE
            WHEN s.code = '1' THEN 1
            WHEN s.code = '2' THEN 2
            WHEN s.code = '28' THEN 3
            WHEN s.code = '52' THEN 4
            WHEN s.code = '54' THEN 5
            ELSE 10
          END
        FROM trades t
        CROSS JOIN subjects s
        WHERE t.trade_code = 'MED'
          AND s.code IN ('1', '2', '28', '52', '54', '146', '49')
      `);
      mappingResults.push('✅ Created Medical trade mappings');
    } catch (error) {
      mappingResults.push(`❌ Medical mappings error: ${error.message}`);
    }

    // Non Medical Trade Subjects
    try {
      await db.query(`
        INSERT IGNORE INTO trade_subjects (trade_id, subject_id, subject_classification, is_compulsory, include_in_grand_total, academic_session, display_order)
        SELECT
          t.id,
          s.id,
          CASE
            WHEN s.code IN ('1', '2') THEN 'Compulsory Language'
            WHEN s.code IN ('28', '52', '53') THEN 'Core Science'
            WHEN s.code IN ('146', '49') THEN 'Additional Compulsory'
            ELSE 'Core Science'
          END,
          CASE WHEN s.code IN ('1', '2', '28', '52', '53') THEN TRUE ELSE FALSE END,
          CASE WHEN s.code IN ('1', '2', '28', '52', '53') THEN TRUE ELSE FALSE END,
          '2023-2024',
          CASE
            WHEN s.code = '1' THEN 1
            WHEN s.code = '2' THEN 2
            WHEN s.code = '28' THEN 3
            WHEN s.code = '52' THEN 4
            WHEN s.code = '53' THEN 5
            ELSE 10
          END
        FROM trades t
        CROSS JOIN subjects s
        WHERE t.trade_code = 'NM'
          AND s.code IN ('1', '2', '28', '52', '53', '146', '49')
      `);
      mappingResults.push('✅ Created Non Medical trade mappings');
    } catch (error) {
      mappingResults.push(`❌ Non Medical mappings error: ${error.message}`);
    }

    // Commerce Trade Subjects
    try {
      await db.query(`
        INSERT IGNORE INTO trade_subjects (trade_id, subject_id, subject_classification, is_compulsory, include_in_grand_total, academic_session, display_order)
        SELECT
          t.id,
          s.id,
          CASE
            WHEN s.code IN ('1', '2') THEN 'Compulsory Language'
            WHEN s.code IN ('141', '142', '26') THEN 'Core Commerce'
            WHEN s.code IN ('146', '49') THEN 'Additional Compulsory'
            ELSE 'Core Commerce'
          END,
          CASE WHEN s.code IN ('1', '2', '141', '142', '26') THEN TRUE ELSE FALSE END,
          CASE WHEN s.code IN ('1', '2', '141', '142', '26') THEN TRUE ELSE FALSE END,
          '2023-2024',
          CASE
            WHEN s.code = '1' THEN 1
            WHEN s.code = '2' THEN 2
            WHEN s.code = '141' THEN 3
            WHEN s.code = '142' THEN 4
            WHEN s.code = '26' THEN 5
            ELSE 10
          END
        FROM trades t
        CROSS JOIN subjects s
        WHERE t.trade_code = 'COM'
          AND s.code IN ('1', '2', '141', '142', '26', '146', '49')
      `);
      mappingResults.push('✅ Created Commerce trade mappings');
    } catch (error) {
      mappingResults.push(`❌ Commerce mappings error: ${error.message}`);
    }

    res.json({
      success: true,
      message: 'Trade-subject mappings created',
      results: mappingResults
    });

  } catch (error) {
    console.error('❌ Trade-subject mapping failed:', error);
    res.status(500).json({
      success: false,
      message: 'Trade-subject mapping failed',
      error: error.message
    });
  }
});

// View new schema structure
router.get('/api/view-new-schema', async (req, res) => {
  try {
    const db = require('../config/database');

    console.log('🔍 Viewing new schema structure...');

    // Get all trades
    const [trades] = await db.query(`
      SELECT * FROM trades ORDER BY trade_category, trade_name
    `);

    // Get trade-subject mappings
    const [tradeMappings] = await db.query(`
      SELECT
        t.trade_code,
        t.trade_name,
        t.trade_category,
        s.code as subject_code,
        s.name as subject_name,
        ts.subject_classification,
        ts.is_compulsory,
        ts.include_in_grand_total,
        ts.display_order
      FROM trade_subjects ts
      JOIN trades t ON ts.trade_id = t.id
      JOIN subjects s ON ts.subject_id = s.id
      ORDER BY t.trade_category, t.trade_name, ts.display_order, s.code
    `);

    // Get sample students with current trade field
    const [currentStudents] = await db.query(`
      SELECT
        id, name, class, section, trade, academic_session
      FROM students
      WHERE is_active = 1
      ORDER BY class, section, name
      LIMIT 10
    `);

    // Check if trade_id column exists
    const [studentColumns] = await db.query(`
      DESCRIBE students
    `);

    const hasTradeId = studentColumns.some(col => col.Field === 'trade_id');

    res.json({
      success: true,
      schema_info: {
        trades_count: trades.length,
        trade_mappings_count: tradeMappings.length,
        has_trade_id_column: hasTradeId
      },
      trades: trades,
      trade_subject_mappings: tradeMappings,
      sample_current_students: currentStudents,
      student_table_structure: studentColumns,
      message: 'New schema structure overview'
    });

  } catch (error) {
    console.error('❌ Schema view failed:', error);
    res.status(500).json({
      success: false,
      message: 'Schema view failed',
      error: error.message
    });
  }
});

// Add trade_id column to students and migrate data
router.get('/api/migrate-student-trades', async (req, res) => {
  try {
    const db = require('../config/database');

    console.log('🔄 Migrating student trade data...');

    const migrationSteps = [];

    // Step 1: Add trade_id column if it doesn't exist
    try {
      await db.query(`
        ALTER TABLE students
        ADD COLUMN IF NOT EXISTS trade_id INT NULL AFTER section
      `);
      migrationSteps.push('✅ Added trade_id column to students table');
    } catch (error) {
      migrationSteps.push(`❌ Add trade_id column error: ${error.message}`);
    }

    // Step 2: Migrate existing trade data to trade_id
    try {
      const updateResults = [];

      // Medical students
      const [medicalUpdate] = await db.query(`
        UPDATE students s
        JOIN trades t ON t.trade_code = 'MED'
        SET s.trade_id = t.id
        WHERE s.trade = 'Biology' AND s.trade_id IS NULL
      `);
      updateResults.push(`Medical: ${medicalUpdate.affectedRows} students`);

      // Non Medical students
      const [nmUpdate] = await db.query(`
        UPDATE students s
        JOIN trades t ON t.trade_code = 'NM'
        SET s.trade_id = t.id
        WHERE s.trade IN ('Physics', 'Chemistry', 'Mathematics') AND s.trade_id IS NULL
      `);
      updateResults.push(`Non Medical: ${nmUpdate.affectedRows} students`);

      // Computer Science students
      const [csUpdate] = await db.query(`
        UPDATE students s
        JOIN trades t ON t.trade_code = 'CS'
        SET s.trade_id = t.id
        WHERE s.trade = 'Computer Science' AND s.trade_id IS NULL
      `);
      updateResults.push(`Computer Science: ${csUpdate.affectedRows} students`);

      // Commerce students
      const [comUpdate] = await db.query(`
        UPDATE students s
        JOIN trades t ON t.trade_code = 'COM'
        SET s.trade_id = t.id
        WHERE s.trade IN ('Commerce', 'Economics', 'Accountancy') AND s.trade_id IS NULL
      `);
      updateResults.push(`Commerce: ${comUpdate.affectedRows} students`);

      // General students (Class 10)
      const [genUpdate] = await db.query(`
        UPDATE students s
        JOIN trades t ON t.trade_code = 'GEN'
        SET s.trade_id = t.id
        WHERE s.class = '10' AND s.trade_id IS NULL
      `);
      updateResults.push(`General (Class 10): ${genUpdate.affectedRows} students`);

      migrationSteps.push(`✅ Migrated student trades: ${updateResults.join(', ')}`);

    } catch (error) {
      migrationSteps.push(`❌ Trade migration error: ${error.message}`);
    }

    res.json({
      success: true,
      message: 'Student trade migration completed',
      steps: migrationSteps
    });

  } catch (error) {
    console.error('❌ Student trade migration failed:', error);
    res.status(500).json({
      success: false,
      message: 'Student trade migration failed',
      error: error.message
    });
  }
});

// View complete student-trade-subject data with new schema
router.get('/api/new-schema-student-view', async (req, res) => {
  try {
    const db = require('../config/database');

    console.log('🔍 Generating complete view with new schema...');

    // Get complete student-trade-subject data using new schema
    const [studentData] = await db.query(`
      SELECT
        -- Student Information
        s.id as student_id,
        s.student_id as roll_number,
        s.name as student_name,
        s.class,
        s.section,
        s.academic_session,
        s.trade as old_trade_field,

        -- Trade Information (from new schema)
        t.id as trade_id,
        t.trade_code,
        t.trade_name,
        t.trade_category,
        t.description as trade_description,

        -- Subject Information
        sub.id as subject_id,
        sub.code as subject_code,
        sub.name as subject_name,
        sub.stream as subject_stream,

        -- Subject Classification (from trade mapping)
        ts.subject_classification,
        ts.is_compulsory,
        ts.include_in_grand_total,
        ts.display_order,

        -- Enrollment Source
        CASE
          WHEN ts.id IS NOT NULL THEN 'Trade Compulsory'
          WHEN seo.id IS NOT NULL THEN CONCAT('Student Selected - ', seo.selection_type)
          ELSE 'Not Enrolled'
        END as enrollment_source,

        -- Marks Information
        ssm.theory_marks,
        ssm.practical_marks,
        ssm.internal_marks,
        ssm.total_marks,
        ssm.max_marks,

        -- Performance Calculations
        CASE
          WHEN ssm.max_marks > 0 THEN ROUND((ssm.total_marks / ssm.max_marks) * 100, 2)
          ELSE NULL
        END as percentage,

        CASE
          WHEN ssm.max_marks > 0 AND (ssm.total_marks / ssm.max_marks) * 100 >= 33 THEN 'PASS'
          WHEN ssm.max_marks > 0 THEN 'FAIL'
          ELSE NULL
        END as result_status

      FROM students s
      LEFT JOIN trades t ON s.trade_id = t.id
      LEFT JOIN trade_subjects ts ON t.id = ts.trade_id AND ts.academic_session = s.academic_session
      LEFT JOIN student_elective_optional seo ON s.id = seo.student_id AND seo.academic_session = s.academic_session
      LEFT JOIN subjects sub ON (ts.subject_id = sub.id OR seo.subject_id = sub.id)
      LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
        AND sub.id = ssm.subject_id
        AND ssm.academic_session = s.academic_session

      WHERE s.is_active = TRUE
        AND (t.is_active = TRUE OR t.id IS NULL)
        AND (sub.is_active = TRUE OR sub.id IS NULL)

      ORDER BY
        s.class,
        s.section,
        s.name,
        COALESCE(ts.display_order, 999),
        sub.code

      LIMIT 50
    `);

    // Get summary statistics
    const [summaryStats] = await db.query(`
      SELECT
        COUNT(DISTINCT s.id) as total_students,
        COUNT(DISTINCT CASE WHEN s.trade_id IS NOT NULL THEN s.id END) as students_with_trade_id,
        COUNT(DISTINCT CASE WHEN s.trade_id IS NULL THEN s.id END) as students_without_trade_id,
        COUNT(DISTINCT t.id) as total_trades,
        COUNT(DISTINCT ts.id) as total_trade_subject_mappings,
        COUNT(DISTINCT seo.id) as total_elective_selections
      FROM students s
      LEFT JOIN trades t ON s.trade_id = t.id
      LEFT JOIN trade_subjects ts ON t.id = ts.trade_id
      LEFT JOIN student_elective_optional seo ON s.id = seo.student_id
      WHERE s.is_active = TRUE
    `);

    // Get trade distribution
    const [tradeDistribution] = await db.query(`
      SELECT
        t.trade_code,
        t.trade_name,
        t.trade_category,
        COUNT(s.id) as student_count
      FROM trades t
      LEFT JOIN students s ON t.id = s.trade_id AND s.is_active = TRUE
      GROUP BY t.id, t.trade_code, t.trade_name, t.trade_category
      ORDER BY student_count DESC
    `);

    res.json({
      success: true,
      summary: summaryStats[0],
      trade_distribution: tradeDistribution,
      student_data: studentData,
      message: `Complete view with new schema. Showing first 50 records of ${summaryStats[0].total_students} total students.`
    });

  } catch (error) {
    console.error('❌ New schema view failed:', error);
    res.status(500).json({
      success: false,
      message: 'New schema view failed',
      error: error.message
    });
  }
});

// Create student elective selections from existing data
router.get('/api/create-elective-selections', async (req, res) => {
  try {
    const db = require('../config/database');

    console.log('🔄 Creating student elective selections...');

    // Find subjects that students have but are not compulsory for their trade
    const [electiveInserts] = await db.query(`
      INSERT IGNORE INTO student_elective_optional (student_id, subject_id, selection_type, academic_session)
      SELECT DISTINCT
        ssm.student_id,
        ssm.subject_id,
        CASE
          WHEN sub.code = '54' AND t.trade_code != 'MED' THEN 'Optional'
          WHEN sub.code IN ('146', '49') THEN 'Additional'
          ELSE 'Elective'
        END,
        ssm.academic_session
      FROM student_subject_marks ssm
      JOIN students s ON ssm.student_id = s.id
      JOIN trades t ON s.trade_id = t.id
      JOIN subjects sub ON ssm.subject_id = sub.id
      WHERE NOT EXISTS (
        SELECT 1 FROM trade_subjects ts
        WHERE ts.trade_id = t.id
          AND ts.subject_id = ssm.subject_id
          AND ts.is_compulsory = TRUE
          AND ts.academic_session = ssm.academic_session
      )
      AND s.is_active = TRUE
      AND t.is_active = TRUE
    `);

    res.json({
      success: true,
      message: `Created ${electiveInserts.affectedRows} elective selections`,
      affected_rows: electiveInserts.affectedRows
    });

  } catch (error) {
    console.error('❌ Elective selection creation failed:', error);
    res.status(500).json({
      success: false,
      message: 'Elective selection creation failed',
      error: error.message
    });
  }
});

// Final comprehensive view of new schema
router.get('/api/final-schema-overview', async (req, res) => {
  try {
    const db = require('../config/database');

    console.log('📊 Generating final comprehensive schema overview...');

    // 1. Schema Structure Overview
    const [tableInfo] = await db.query(`
      SELECT
        'trades' as table_name,
        COUNT(*) as record_count,
        'Trade definitions and categories' as description
      FROM trades
      UNION ALL
      SELECT
        'trade_subjects' as table_name,
        COUNT(*) as record_count,
        'Trade-subject mappings (compulsory subjects)' as description
      FROM trade_subjects
      UNION ALL
      SELECT
        'student_elective_optional' as table_name,
        COUNT(*) as record_count,
        'Student elective/optional selections' as description
      FROM student_elective_optional
      UNION ALL
      SELECT
        'students_with_trade_id' as table_name,
        COUNT(*) as record_count,
        'Students with new trade_id assigned' as description
      FROM students WHERE trade_id IS NOT NULL
    `);

    // 2. Trade-wise student distribution
    const [tradeStudentDistribution] = await db.query(`
      SELECT
        t.trade_code,
        t.trade_name,
        t.trade_category,
        COUNT(s.id) as student_count,
        GROUP_CONCAT(DISTINCT s.class ORDER BY s.class) as classes,
        COUNT(DISTINCT ts.subject_id) as compulsory_subjects_count
      FROM trades t
      LEFT JOIN students s ON t.id = s.trade_id AND s.is_active = TRUE
      LEFT JOIN trade_subjects ts ON t.id = ts.trade_id AND ts.is_compulsory = TRUE
      GROUP BY t.id, t.trade_code, t.trade_name, t.trade_category
      ORDER BY student_count DESC
    `);

    // 3. Subject classification overview
    const [subjectClassification] = await db.query(`
      SELECT
        ts.subject_classification,
        COUNT(DISTINCT ts.subject_id) as unique_subjects,
        COUNT(DISTINCT ts.trade_id) as trades_using,
        COUNT(*) as total_mappings,
        GROUP_CONCAT(DISTINCT t.trade_name ORDER BY t.trade_name) as trade_names
      FROM trade_subjects ts
      JOIN trades t ON ts.trade_id = t.id
      GROUP BY ts.subject_classification
      ORDER BY total_mappings DESC
    `);

    // 4. Sample student records with complete data
    const [sampleStudents] = await db.query(`
      SELECT
        s.name as student_name,
        s.class,
        s.section,
        t.trade_name,
        t.trade_category,

        -- Compulsory subjects
        (SELECT COUNT(*)
         FROM trade_subjects ts
         WHERE ts.trade_id = t.id
           AND ts.is_compulsory = TRUE
           AND ts.academic_session = s.academic_session) as compulsory_subjects,

        -- Elective subjects
        (SELECT COUNT(*)
         FROM student_elective_optional seo
         WHERE seo.student_id = s.id
           AND seo.academic_session = s.academic_session) as elective_subjects,

        -- Total subjects with marks
        (SELECT COUNT(*)
         FROM student_subject_marks ssm
         WHERE ssm.student_id = s.id
           AND ssm.academic_session = s.academic_session) as subjects_with_marks,

        -- Core subjects for grand total
        (SELECT COUNT(*)
         FROM trade_subjects ts
         WHERE ts.trade_id = t.id
           AND ts.include_in_grand_total = TRUE
           AND ts.academic_session = s.academic_session) as core_subjects_count

      FROM students s
      JOIN trades t ON s.trade_id = t.id
      WHERE s.is_active = TRUE
      ORDER BY s.class, s.section, s.name
      LIMIT 15
    `);

    // 5. Data quality check
    const [dataQuality] = await db.query(`
      SELECT
        'Total Active Students' as metric,
        COUNT(*) as count,
        'All active students in system' as description
      FROM students WHERE is_active = TRUE

      UNION ALL
      SELECT
        'Students with Trade ID' as metric,
        COUNT(*) as count,
        'Students migrated to new schema' as description
      FROM students WHERE is_active = TRUE AND trade_id IS NOT NULL

      UNION ALL
      SELECT
        'Students with Subject Marks' as metric,
        COUNT(DISTINCT student_id) as count,
        'Students who have academic performance data' as description
      FROM student_subject_marks

      UNION ALL
      SELECT
        'Trade-Subject Mappings' as metric,
        COUNT(*) as count,
        'Compulsory subject assignments per trade' as description
      FROM trade_subjects WHERE is_compulsory = TRUE

      UNION ALL
      SELECT
        'Elective Selections' as metric,
        COUNT(*) as count,
        'Student-chosen elective/optional subjects' as description
      FROM student_elective_optional
    `);

    res.json({
      success: true,
      schema_overview: {
        implementation_status: 'Complete',
        migration_status: 'Successful',
        data_integrity: 'Verified'
      },
      table_information: tableInfo,
      trade_student_distribution: tradeStudentDistribution,
      subject_classification_summary: subjectClassification,
      sample_student_records: sampleStudents,
      data_quality_metrics: dataQuality,
      message: 'New schema implementation complete with proper trade-subject relationships'
    });

  } catch (error) {
    console.error('❌ Final schema overview failed:', error);
    res.status(500).json({
      success: false,
      message: 'Final schema overview failed',
      error: error.message
    });
  }
});

// Student Export API endpoint
router.post('/api/export-students', async (req, res) => {
  try {
    console.log('🔄 Starting student export...');
    console.log('Request body:', req.body);
    const { format, type, columns, studentIds, filters } = req.body;

    console.log('Export parameters:', {
      format,
      type,
      columnsCount: columns ? columns.length : 'all',
      studentIdsCount: studentIds ? studentIds.length : 'all',
      filters: Object.keys(filters || {})
    });

    const db = require('../config/database');
    const path = require('path');
    const fs = require('fs');

    // Test if ExcelJS is available
    let ExcelJS;
    try {
      ExcelJS = require('exceljs');
      console.log('✅ ExcelJS loaded successfully');
    } catch (excelError) {
      console.error('❌ ExcelJS not available:', excelError.message);
      return res.status(500).json({
        success: false,
        message: 'Excel export functionality not available'
      });
    }

    // Build database query to fetch real student data
    console.log('🔍 Fetching students from database...');

    // Build WHERE clause for filters
    let whereConditions = ['s.is_active = 1'];
    let queryParams = [];

    // Add filters to WHERE clause
    if (filters) {
      if (filters.search) {
        whereConditions.push('(s.name LIKE ? OR s.student_id LIKE ? OR s.father_name LIKE ? OR s.roll_no LIKE ?)');
        const searchTerm = `%${filters.search}%`;
        queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
      }
      if (filters.class) {
        whereConditions.push('s.class = ?');
        queryParams.push(filters.class);
      }
      if (filters.section) {
        whereConditions.push('s.section = ?');
        queryParams.push(filters.section);
      }
      if (filters.session) {
        whereConditions.push('s.session = ?');
        queryParams.push(filters.session);
      }
      if (filters.gender) {
        whereConditions.push('s.gender = ?');
        queryParams.push(filters.gender);
      }
      if (filters.stream) {
        whereConditions.push('s.stream = ?');
        queryParams.push(filters.stream);
      }
      if (filters.bpl) {
        whereConditions.push('s.bpl = ?');
        queryParams.push(filters.bpl);
      }
      if (filters.disability && filters.disability !== '') {
        if (filters.disability === 'Yes') {
          whereConditions.push('(s.disability IS NOT NULL AND s.disability != "" AND s.disability != "No")');
        } else {
          whereConditions.push('(s.disability IS NULL OR s.disability = "" OR s.disability = "No")');
        }
      }
    }

    // Add specific student IDs if exporting selected
    if (type === 'selected' && studentIds && studentIds.length > 0) {
      const placeholders = studentIds.map(() => '?').join(',');
      whereConditions.push(`s.id IN (${placeholders})`);
      queryParams.push(...studentIds);
    }

    const whereClause = whereConditions.join(' AND ');

    // Execute database query to get students
    const query = `
      SELECT
        s.id, s.sno, s.student_id, s.udise_code, s.name, s.father_name, s.mother_name,
        s.dob, s.gender, s.class, s.section, s.stream, s.trade, s.caste_category_name,
        s.bpl, s.disability, s.religion_name, s.medium_name, s.height, s.weight,
        s.admission_no, s.admission_date, s.state_name, s.district_name, s.cur_address,
        s.village_ward, s.gram_panchayat, s.pin_code, s.roll_no, s.contact_no,
        s.ifsc_code, s.bank_name, s.account_holder, s.account_holder_name,
        s.account_holder_code, s.session, s.room_number, s.created_at, s.updated_at
      FROM students s
      WHERE ${whereClause}
      ORDER BY s.class, s.section, s.roll_no, s.name
    `;

    console.log('🔍 Executing query with', queryParams.length, 'parameters');
    const [students] = await db.query(query, queryParams);
    console.log('✅ Retrieved', students.length, 'students from database');

    // Students are already filtered by the database query
    const studentsToExport = students;

    if (studentsToExport.length === 0) {
      return res.json({
        success: false,
        message: 'No students found matching the criteria'
      });
    }

    // Define all available columns based on actual database table structure
    const allColumns = {
      // Basic Information
      sno: 'S.No',
      student_id: 'Student ID',
      name: 'Name',
      father_name: "Father's Name",
      mother_name: "Mother's Name",
      dob: 'Date of Birth',
      gender: 'Gender',

      // Academic Information
      class: 'Class',
      section: 'Section',
      session: 'Session',
      stream: 'Stream',
      trade: 'Trade',
      roll_no: 'Roll Number',
      medium_name: 'Medium',
      room_number: 'Room Number',

      // Contact & Address
      contact_no: 'Contact Number',
      cur_address: 'Current Address',
      village_ward: 'Village/Ward',
      gram_panchayat: 'Gram Panchayat',
      pin_code: 'Pin Code',
      district_name: 'District',
      state_name: 'State',

      // Administrative
      udise_code: 'UDISE Code',
      admission_no: 'Admission Number',
      admission_date: 'Admission Date',
      religion_name: 'Religion',
      caste_category_name: 'Caste Category',
      is_active: 'Active Status',

      // Health & Welfare
      bpl: 'BPL Status',
      disability: 'Disability Status',
      height: 'Height (cm)',
      weight: 'Weight (kg)',

      // Bank Information
      bank_name: 'Bank Name',
      ifsc_code: 'IFSC Code',
      account_holder: 'Account Holder',
      account_holder_name: 'Account Holder Name',
      account_holder_code: 'Account Holder Code',

      // System Information
      created_at: 'Created Date',
      updated_at: 'Last Updated'
    };

    // No column mapping needed since we're using actual database column names
    const columnMapping = {};

    // Determine which columns to export, mapping names as needed
    const requestedColumns = columns && columns.length > 0 ? columns : Object.keys(allColumns);
    const exportColumns = requestedColumns.map(col => columnMapping[col] || col).filter(col => allColumns[col]);

    console.log('Exporting columns:', exportColumns);

    // Generate filename
    const timestamp = Date.now();
    const typeLabel = type === 'selected' ? 'Selected' : 'All';
    const filename = `Students_${typeLabel}_Export_${timestamp}.${format === 'pdf' ? 'pdf' : 'xlsx'}`;
    const outputDir = path.join(__dirname, '../public/uploads/exports');
    const outputPath = path.join(outputDir, filename);

    // Ensure directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    if (format === 'excel') {
      // Generate Excel file
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Students');

      // Add headers
      const headers = exportColumns.map(col => allColumns[col]);
      worksheet.addRow(headers);

      // Style headers
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF366092' }
      };
      headerRow.font = { bold: true, color: { argb: 'FFFFFFFF' } };

      // Add data rows
      studentsToExport.forEach(student => {
        const rowData = exportColumns.map(col => {
          let value = student[col];

          // Format dates
          if ((col === 'dob' || col === 'admission_date' || col === 'created_at' || col === 'updated_at') && value) {
            value = new Date(value).toLocaleDateString('en-IN');
          }

          // Format boolean values
          if (col === 'is_active') {
            value = value ? 'Active' : 'Inactive';
          }

          // Format height and weight with units
          if (col === 'height' && value) {
            value = value + ' cm';
          }
          if (col === 'weight' && value) {
            value = value + ' kg';
          }

          // Handle null/undefined values
          return value || '';
        });
        worksheet.addRow(rowData);
      });

      // Auto-fit columns
      worksheet.columns.forEach(column => {
        let maxLength = 0;
        column.eachCell({ includeEmpty: true }, cell => {
          const columnLength = cell.value ? cell.value.toString().length : 10;
          if (columnLength > maxLength) {
            maxLength = columnLength;
          }
        });
        column.width = Math.min(maxLength + 2, 50);
      });

      // Save Excel file
      await workbook.xlsx.writeFile(outputPath);

    } else if (format === 'pdf') {
      // Generate PDF file using the PDF generator utility
      const { generatePDF } = require('../utils/pdf-generator');

      // Create a simple template for student list
      const templateData = {
        title: `Student Export - ${typeLabel}`,
        students: studentsToExport,
        columns: exportColumns,
        columnHeaders: allColumns,
        exportDate: new Date().toLocaleDateString('en-IN'),
        totalCount: studentsToExport.length
      };

      // Create a responsive HTML template for the PDF that accommodates all columns
      const columnCount = exportColumns.length;
      const isWideTable = columnCount > 8;

      const htmlTemplate = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Student Export</title>
          <style>
            @page {
              size: ${isWideTable ? 'A3 landscape' : 'A4 portrait'};
              margin: 10mm;
            }
            body {
              font-family: Arial, sans-serif;
              font-size: ${isWideTable ? '8px' : '10px'};
              margin: 0;
              padding: 0;
            }
            .header {
              text-align: center;
              margin-bottom: 15px;
              border-bottom: 2px solid #000;
              padding-bottom: 8px;
            }
            .header h1 {
              margin: 0;
              font-size: ${isWideTable ? '14px' : '16px'};
              font-weight: bold;
            }
            .header p {
              margin: 3px 0;
              color: #666;
              font-size: ${isWideTable ? '7px' : '9px'};
            }
            .table-container {
              width: 100%;
              overflow-x: auto;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 10px;
              table-layout: ${isWideTable ? 'auto' : 'fixed'};
            }
            th, td {
              border: 1px solid #ccc;
              padding: ${isWideTable ? '3px' : '5px'};
              text-align: left;
              vertical-align: top;
              word-wrap: break-word;
              overflow-wrap: break-word;
            }
            th {
              background-color: #f0f0f0;
              font-weight: bold;
              font-size: ${isWideTable ? '7px' : '9px'};
            }
            td {
              font-size: ${isWideTable ? '6px' : '8px'};
            }
            tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            .footer {
              margin-top: 15px;
              text-align: center;
              font-size: ${isWideTable ? '6px' : '8px'};
              color: #666;
              page-break-inside: avoid;
            }
            /* Column width adjustments for better fit */
            ${isWideTable ? `
              th:nth-child(1), td:nth-child(1) { width: 3%; } /* S.No */
              th:nth-child(2), td:nth-child(2) { width: 6%; } /* Student ID */
              th:nth-child(3), td:nth-child(3) { width: 8%; } /* Name */
              th, td { max-width: 50px; }
            ` : ''}
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${templateData.title}</h1>
            <p>Generated on ${templateData.exportDate}</p>
            <p>Total Records: ${templateData.totalCount} | Columns: ${columnCount}</p>
          </div>
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  ${exportColumns.map(col => `<th title="${allColumns[col]}">${allColumns[col]}</th>`).join('')}
                </tr>
              </thead>
              <tbody>
                ${studentsToExport.map(student => `
                  <tr>
                    ${exportColumns.map(col => {
                      let value = student[col];

                      // Format dates
                      if ((col === 'dob' || col === 'admission_date' || col === 'created_at' || col === 'updated_at') && value) {
                        value = new Date(value).toLocaleDateString('en-IN');
                      }

                      // Format boolean values
                      if (col === 'is_active') {
                        value = value ? 'Active' : 'Inactive';
                      }

                      // Format height and weight with units
                      if (col === 'height' && value && value !== '0.00') {
                        value = value + ' cm';
                      }
                      if (col === 'weight' && value && value !== '0.00') {
                        value = value + ' kg';
                      }

                      // Truncate long text for better fit
                      const displayValue = value || '';
                      const maxLength = isWideTable ? 15 : 25;
                      const truncatedValue = displayValue.toString().length > maxLength
                        ? displayValue.toString().substring(0, maxLength) + '...'
                        : displayValue;

                      return `<td title="${displayValue}">${truncatedValue}</td>`;
                    }).join('')}
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
          <div class="footer">
            <p>Student Management System - Generated by Principal Dashboard</p>
            <p>Note: Long text values are truncated for display. Hover over cells for full content.</p>
          </div>
        </body>
        </html>
      `;

      // Write HTML to temp file and convert to PDF
      const tempHtmlPath = outputPath.replace('.pdf', '.html');
      fs.writeFileSync(tempHtmlPath, htmlTemplate);

      try {
        await generatePDF({ html: htmlTemplate }, tempHtmlPath, outputPath);
        // Clean up temp HTML file
        if (fs.existsSync(tempHtmlPath)) {
          fs.unlinkSync(tempHtmlPath);
        }
      } catch (pdfError) {
        console.error('PDF generation error:', pdfError);
        // Clean up temp HTML file on error
        if (fs.existsSync(tempHtmlPath)) {
          fs.unlinkSync(tempHtmlPath);
        }
        throw pdfError;
      }
    }

    // Return success response
    const fileUrl = `/uploads/exports/${filename}`;
    console.log('✅ Export generated successfully:', fileUrl);

    res.json({
      success: true,
      message: `${format.toUpperCase()} export generated successfully`,
      url: fileUrl,
      filename: filename,
      recordCount: studentsToExport.length
    });

  } catch (error) {
    console.error('❌ Error generating export:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating export: ' + error.message
    });
  }
});

// Teacher Export API endpoint
router.post('/api/export-teachers', async (req, res) => {
  try {
    console.log('🔄 Starting teacher export...');
    console.log('Request body:', req.body);
    const { format, type, columns, teacherIds, filters } = req.body;

    console.log('Export parameters:', {
      format,
      type,
      columnsCount: columns ? columns.length : 'all',
      teacherIdsCount: teacherIds ? teacherIds.length : 'all',
      filters: Object.keys(filters || {})
    });

    const db = require('../config/database');
    const path = require('path');
    const fs = require('fs');

    // Test if ExcelJS is available
    let ExcelJS;
    try {
      ExcelJS = require('exceljs');
      console.log('✅ ExcelJS loaded successfully');
    } catch (excelError) {
      console.error('❌ ExcelJS not available:', excelError.message);
      return res.status(500).json({
        success: false,
        message: 'Excel export functionality not available'
      });
    }

    // Build database query to fetch real teacher data
    console.log('🔍 Fetching teachers from database...');

    // Build WHERE clause for filters
    let whereConditions = ['u.role = ?', 'u.is_active = 1'];
    let queryParams = ['teacher'];

    // Add filters to WHERE clause
    if (filters) {
      if (filters.search) {
        whereConditions.push('(u.name LIKE ? OR u.email LIKE ? OR u.username LIKE ? OR s.employee_id LIKE ?)');
        const searchTerm = `%${filters.search}%`;
        queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
      }
      if (filters.department) {
        whereConditions.push('s.department = ?');
        queryParams.push(filters.department);
      }
      if (filters.designation) {
        whereConditions.push('s.designation = ?');
        queryParams.push(filters.designation);
      }
      if (filters.employment_type) {
        whereConditions.push('s.employment_type = ?');
        queryParams.push(filters.employment_type);
      }
      if (filters.status) {
        if (filters.status === 'active') {
          whereConditions.push('u.is_active = 1');
        } else if (filters.status === 'inactive') {
          whereConditions.push('u.is_active = 0');
        }
      }
    }

    // Add specific teacher IDs if exporting selected
    if (type === 'selected' && teacherIds && teacherIds.length > 0) {
      const placeholders = teacherIds.map(() => '?').join(',');
      whereConditions.push(`u.id IN (${placeholders})`);
      queryParams.push(...teacherIds);
    }

    const whereClause = whereConditions.join(' AND ');

    // Execute database query to get teachers
    const query = `
      SELECT
        u.id, u.username, u.name, u.full_name, u.email, u.profile_image, u.subjects as user_subjects,
        u.bio, u.date_of_birth, u.created_at, u.last_login, u.is_active,
        s.id as staff_id, s.employee_id, s.designation, s.department, s.current_school,
        s.joining_date, s.employment_type, s.phone, s.alternate_phone, s.emergency_contact,
        s.address, s.city, s.state, s.pincode, s.gender, s.current_salary,
        s.subjects_taught, s.classes_handled, s.total_experience_years, s.teaching_experience_years,
        s.administrative_experience_years, s.performance_rating, s.awards_received, s.publications,
        s.research_papers, s.conferences_attended, s.training_programs, s.special_skills,
        s.languages_known, s.office_location, s.probation_period_months, s.confirmation_date,
        s.last_promotion_date, s.is_on_leave, s.leave_start_date, s.leave_end_date,
        s.class_10_board, s.class_10_year, s.class_10_percentage, s.class_10_school,
        s.class_12_board, s.class_12_year, s.class_12_percentage, s.class_12_school, s.class_12_stream,
        s.graduation_degree, s.graduation_university, s.graduation_year, s.graduation_percentage, s.graduation_specialization,
        s.post_graduation_degree, s.post_graduation_university, s.post_graduation_year, s.post_graduation_percentage, s.post_graduation_specialization,
        s.phd_subject, s.phd_university, s.phd_year, s.phd_thesis_title,
        s.other_qualifications, s.professional_certifications, s.previous_organizations,
        s.notes, s.created_at as staff_created_at, s.updated_at as staff_updated_at
      FROM users u
      LEFT JOIN staff s ON u.id = s.user_id
      WHERE ${whereClause}
      ORDER BY s.department, s.designation, u.name
    `;

    console.log('🔍 Executing query with', queryParams.length, 'parameters');
    const [teachers] = await db.query(query, queryParams);
    console.log('✅ Retrieved', teachers.length, 'teachers from database');

    // Teachers are already filtered by the database query
    const teachersToExport = teachers;

    if (teachersToExport.length === 0) {
      return res.json({
        success: false,
        message: 'No teachers found matching the criteria'
      });
    }

    // Define all available columns based on actual database table structure
    const allColumns = {
      // Basic Information
      id: 'ID',
      employee_id: 'Employee ID',
      name: 'Name',
      full_name: 'Full Name',
      username: 'Username',
      email: 'Email',
      date_of_birth: 'Date of Birth',
      gender: 'Gender',

      // Professional Information
      designation: 'Designation',
      department: 'Department',
      current_school: 'Current School',
      joining_date: 'Joining Date',
      employment_type: 'Employment Type',
      subjects_taught: 'Subjects Taught',
      classes_handled: 'Classes Handled',
      office_location: 'Office Location',

      // Contact Information
      phone: 'Phone',
      alternate_phone: 'Alternate Phone',
      emergency_contact: 'Emergency Contact',
      address: 'Address',
      city: 'City',
      state: 'State',
      pincode: 'Pin Code',

      // Experience & Performance
      total_experience_years: 'Total Experience (Years)',
      teaching_experience_years: 'Teaching Experience (Years)',
      administrative_experience_years: 'Administrative Experience (Years)',
      performance_rating: 'Performance Rating',
      current_salary: 'Current Salary',
      previous_organizations: 'Previous Organizations',

      // Education - Class 10
      class_10_board: 'Class 10 Board',
      class_10_year: 'Class 10 Year',
      class_10_percentage: 'Class 10 Percentage',
      class_10_school: 'Class 10 School',

      // Education - Class 12
      class_12_board: 'Class 12 Board',
      class_12_year: 'Class 12 Year',
      class_12_percentage: 'Class 12 Percentage',
      class_12_school: 'Class 12 School',
      class_12_stream: 'Class 12 Stream',

      // Education - Graduation
      graduation_degree: 'Graduation Degree',
      graduation_university: 'Graduation University',
      graduation_year: 'Graduation Year',
      graduation_percentage: 'Graduation Percentage',
      graduation_specialization: 'Graduation Specialization',

      // Education - Post Graduation
      post_graduation_degree: 'Post Graduation Degree',
      post_graduation_university: 'Post Graduation University',
      post_graduation_year: 'Post Graduation Year',
      post_graduation_percentage: 'Post Graduation Percentage',
      post_graduation_specialization: 'Post Graduation Specialization',

      // Education - PhD
      phd_subject: 'PhD Subject',
      phd_university: 'PhD University',
      phd_year: 'PhD Year',
      phd_thesis_title: 'PhD Thesis Title',

      // Additional Information
      other_qualifications: 'Other Qualifications',
      professional_certifications: 'Professional Certifications',
      special_skills: 'Special Skills',
      languages_known: 'Languages Known',
      awards_received: 'Awards Received',
      publications: 'Publications',
      research_papers: 'Research Papers',
      conferences_attended: 'Conferences Attended',
      training_programs: 'Training Programs',

      // Employment Details
      probation_period_months: 'Probation Period (Months)',
      confirmation_date: 'Confirmation Date',
      last_promotion_date: 'Last Promotion Date',
      is_on_leave: 'On Leave',
      leave_start_date: 'Leave Start Date',
      leave_end_date: 'Leave End Date',

      // System Information
      is_active: 'Active Status',
      last_login: 'Last Login',
      bio: 'Bio',
      notes: 'Notes',
      created_at: 'Created Date',
      staff_updated_at: 'Last Updated'
    };

    // No column mapping needed since we're using actual database column names
    const columnMapping = {};

    // Determine which columns to export, mapping names as needed
    const requestedColumns = columns && columns.length > 0 ? columns : Object.keys(allColumns);
    const exportColumns = requestedColumns.map(col => columnMapping[col] || col).filter(col => allColumns[col]);

    console.log('Exporting columns:', exportColumns);

    // Generate filename
    const timestamp = Date.now();
    const typeLabel = type === 'selected' ? 'Selected' : 'All';
    const filename = `Teachers_${typeLabel}_Export_${timestamp}.${format === 'pdf' ? 'pdf' : 'xlsx'}`;
    const outputDir = path.join(__dirname, '../public/uploads/exports');
    const outputPath = path.join(outputDir, filename);

    // Ensure directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    if (format === 'excel') {
      // Generate Excel file
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Teachers');

      // Add headers
      const headers = exportColumns.map(col => allColumns[col]);
      worksheet.addRow(headers);

      // Style headers
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF366092' }
      };
      headerRow.font = { bold: true, color: { argb: 'FFFFFFFF' } };

      // Add data rows
      teachersToExport.forEach(teacher => {
        const rowData = exportColumns.map(col => {
          let value = teacher[col];

          // Format dates
          if ((col === 'date_of_birth' || col === 'joining_date' || col === 'confirmation_date' ||
               col === 'last_promotion_date' || col === 'leave_start_date' || col === 'leave_end_date' ||
               col === 'created_at' || col === 'staff_updated_at' || col === 'last_login') && value) {
            value = new Date(value).toLocaleDateString('en-IN');
          }

          // Format boolean values
          if (col === 'is_active' || col === 'is_on_leave') {
            value = value ? 'Yes' : 'No';
          }

          // Format salary
          if (col === 'current_salary' && value) {
            value = '₹' + parseFloat(value).toLocaleString('en-IN');
          }

          // Format percentages
          if ((col.includes('percentage') || col.includes('rating')) && value) {
            value = value + '%';
          }

          // Handle null/undefined values
          return value || '';
        });
        worksheet.addRow(rowData);
      });

      // Auto-fit columns
      worksheet.columns.forEach(column => {
        let maxLength = 0;
        column.eachCell({ includeEmpty: true }, cell => {
          const columnLength = cell.value ? cell.value.toString().length : 10;
          if (columnLength > maxLength) {
            maxLength = columnLength;
          }
        });
        column.width = Math.min(maxLength + 2, 50);
      });

      // Save Excel file
      await workbook.xlsx.writeFile(outputPath);

    } else if (format === 'pdf') {
      // Generate PDF file using the PDF generator utility
      const { generatePDF } = require('../utils/pdf-generator');

      // Create a simple template for teacher list
      const templateData = {
        title: `Teacher Export - ${typeLabel}`,
        teachers: teachersToExport,
        columns: exportColumns,
        columnHeaders: allColumns,
        exportDate: new Date().toLocaleDateString('en-IN'),
        totalCount: teachersToExport.length
      };

      // Create a responsive HTML template for the PDF that accommodates all columns
      const columnCount = exportColumns.length;
      const isWideTable = columnCount > 8;

      const htmlTemplate = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Teacher Export</title>
          <style>
            @page {
              size: ${isWideTable ? 'A3 landscape' : 'A4 portrait'};
              margin: 10mm;
            }
            body {
              font-family: Arial, sans-serif;
              font-size: ${isWideTable ? '8px' : '10px'};
              margin: 0;
              padding: 0;
            }
            .header {
              text-align: center;
              margin-bottom: 15px;
              border-bottom: 2px solid #000;
              padding-bottom: 8px;
            }
            .header h1 {
              margin: 0;
              font-size: ${isWideTable ? '14px' : '16px'};
              font-weight: bold;
            }
            .header p {
              margin: 3px 0;
              color: #666;
              font-size: ${isWideTable ? '7px' : '9px'};
            }
            .table-container {
              width: 100%;
              overflow-x: auto;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 10px;
              table-layout: ${isWideTable ? 'auto' : 'fixed'};
            }
            th, td {
              border: 1px solid #ccc;
              padding: ${isWideTable ? '3px' : '5px'};
              text-align: left;
              vertical-align: top;
              word-wrap: break-word;
              overflow-wrap: break-word;
            }
            th {
              background-color: #f0f0f0;
              font-weight: bold;
              font-size: ${isWideTable ? '7px' : '9px'};
            }
            td {
              font-size: ${isWideTable ? '6px' : '8px'};
            }
            tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            .footer {
              margin-top: 15px;
              text-align: center;
              font-size: ${isWideTable ? '6px' : '8px'};
              color: #666;
              page-break-inside: avoid;
            }
            /* Column width adjustments for better fit */
            ${isWideTable ? `
              th:nth-child(1), td:nth-child(1) { width: 3%; } /* ID */
              th:nth-child(2), td:nth-child(2) { width: 6%; } /* Employee ID */
              th:nth-child(3), td:nth-child(3) { width: 8%; } /* Name */
              th, td { max-width: 50px; }
            ` : ''}
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${templateData.title}</h1>
            <p>Generated on ${templateData.exportDate}</p>
            <p>Total Records: ${templateData.totalCount} | Columns: ${columnCount}</p>
          </div>
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  ${exportColumns.map(col => `<th title="${allColumns[col]}">${allColumns[col]}</th>`).join('')}
                </tr>
              </thead>
              <tbody>
                ${teachersToExport.map(teacher => `
                  <tr>
                    ${exportColumns.map(col => {
                      let value = teacher[col];

                      // Format dates
                      if ((col === 'date_of_birth' || col === 'joining_date' || col === 'confirmation_date' ||
                           col === 'last_promotion_date' || col === 'leave_start_date' || col === 'leave_end_date' ||
                           col === 'created_at' || col === 'staff_updated_at' || col === 'last_login') && value) {
                        value = new Date(value).toLocaleDateString('en-IN');
                      }

                      // Format boolean values
                      if (col === 'is_active' || col === 'is_on_leave') {
                        value = value ? 'Yes' : 'No';
                      }

                      // Format salary
                      if (col === 'current_salary' && value) {
                        value = '₹' + parseFloat(value).toLocaleString('en-IN');
                      }

                      // Format percentages
                      if ((col.includes('percentage') || col.includes('rating')) && value) {
                        value = value + '%';
                      }

                      // Truncate long text for better fit
                      const displayValue = value || '';
                      const maxLength = isWideTable ? 15 : 25;
                      const truncatedValue = displayValue.toString().length > maxLength
                        ? displayValue.toString().substring(0, maxLength) + '...'
                        : displayValue;

                      return `<td title="${displayValue}">${truncatedValue}</td>`;
                    }).join('')}
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
          <div class="footer">
            <p>Teacher Management System - Generated by Principal Dashboard</p>
            <p>Note: Long text values are truncated for display. Hover over cells for full content.</p>
          </div>
        </body>
        </html>
      `;

      // Write HTML to temp file and convert to PDF
      const tempHtmlPath = outputPath.replace('.pdf', '.html');
      fs.writeFileSync(tempHtmlPath, htmlTemplate);

      try {
        await generatePDF({ html: htmlTemplate }, tempHtmlPath, outputPath);
        // Clean up temp HTML file
        if (fs.existsSync(tempHtmlPath)) {
          fs.unlinkSync(tempHtmlPath);
        }
      } catch (pdfError) {
        console.error('PDF generation error:', pdfError);
        // Clean up temp HTML file on error
        if (fs.existsSync(tempHtmlPath)) {
          fs.unlinkSync(tempHtmlPath);
        }
        throw pdfError;
      }
    }

    // Return success response
    const fileUrl = `/uploads/exports/${filename}`;
    console.log('✅ Export generated successfully:', fileUrl);

    res.json({
      success: true,
      message: `${format.toUpperCase()} export generated successfully`,
      url: fileUrl,
      filename: filename,
      recordCount: teachersToExport.length
    });

  } catch (error) {
    console.error('❌ Error generating export:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating export: ' + error.message
    });
  }
});

// Student Analytics
router.get('/student-analytics', principalController.getStudentAnalytics);

// Infrastructure Overview
router.get('/infrastructure', principalController.getInfrastructure);

// Profile
router.get('/profile', principalController.getProfile);

// Principal Profile API endpoint
router.get('/api/profile', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get basic principal information from users table only (to avoid column issues)
    const [users] = await db.query(`
      SELECT
        u.id, u.username, u.name, u.email, u.profile_image, u.bio,
        u.date_of_birth, u.created_at, u.last_login, u.is_active
      FROM users u
      WHERE u.id = ? AND u.role = 'principal'
    `, [req.session.userId]);

    // Try to get staff information separately with error handling
    let staffInfo = null;
    if (users.length > 0) {
      try {
        const [staffResult] = await db.query(`
          SELECT
            s.id as staff_id, s.employee_id, s.designation, s.department,
            s.joining_date, s.employment_type, s.phone, s.alternate_phone,
            s.emergency_contact, s.address, s.city, s.state, s.pincode,
            s.class_10_board, s.class_10_year, s.class_10_percentage, s.class_10_school,
            s.class_12_board, s.class_12_year, s.class_12_percentage, s.class_12_school, s.class_12_stream,
            s.graduation_degree, s.graduation_university, s.graduation_year, s.graduation_percentage, s.graduation_specialization,
            s.post_graduation_degree, s.post_graduation_university, s.post_graduation_year, s.post_graduation_percentage, s.post_graduation_specialization,
            s.phd_subject, s.phd_university, s.phd_year, s.phd_thesis_title,
            s.other_qualifications, s.professional_certifications,
            s.total_experience_years, s.teaching_experience_years, s.administrative_experience_years,
            s.previous_organizations, s.current_salary, s.subjects_taught, s.classes_handled,
            s.awards_received, s.publications, s.research_papers, s.conferences_attended, s.training_programs,
            s.special_skills, s.languages_known, s.office_location, s.performance_rating
          FROM staff s
          WHERE s.user_id = ?
        `, [req.session.userId]);

        if (staffResult.length > 0) {
          staffInfo = staffResult[0];
        }
      } catch (staffError) {
        console.log('Staff table query failed, using defaults:', staffError.message);
        staffInfo = {
          staff_id: null,
          employee_id: `EMP${String(req.session.userId).padStart(4, '0')}`,
          designation: 'Principal',
          department: 'Administration',
          joining_date: null,
          employment_type: 'permanent',
          phone: 'Not provided',
          alternate_phone: null,
          emergency_contact: null,
          address: null,
          city: null,
          state: null,
          pincode: null
        };
      }
    }

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Principal profile not found'
      });
    }

    const user = users[0];

    // Combine user and staff information
    const principal = {
      ...user,
      ...staffInfo
    };

    // Get educational qualifications from staff table data
    let educationTimeline = [];
    if (staffInfo) {
      // Build education timeline from staff table columns
      if (staffInfo.class_10_board) {
        educationTimeline.push({
          qualification_level: '10th',
          qualification_name: 'Class 10 (Secondary)',
          institution_name: staffInfo.class_10_school || 'Not specified',
          university_board: staffInfo.class_10_board,
          completion_year: staffInfo.class_10_year,
          percentage: staffInfo.class_10_percentage,
          specialization: null
        });
      }

      if (staffInfo.class_12_board) {
        educationTimeline.push({
          qualification_level: '12th',
          qualification_name: 'Class 12 (Higher Secondary)',
          institution_name: staffInfo.class_12_school || 'Not specified',
          university_board: staffInfo.class_12_board,
          completion_year: staffInfo.class_12_year,
          percentage: staffInfo.class_12_percentage,
          specialization: staffInfo.class_12_stream
        });
      }

      if (staffInfo.graduation_degree) {
        educationTimeline.push({
          qualification_level: 'graduation',
          qualification_name: staffInfo.graduation_degree,
          institution_name: staffInfo.graduation_university || 'Not specified',
          university_board: staffInfo.graduation_university,
          completion_year: staffInfo.graduation_year,
          percentage: staffInfo.graduation_percentage,
          specialization: staffInfo.graduation_specialization
        });
      }

      if (staffInfo.post_graduation_degree) {
        educationTimeline.push({
          qualification_level: 'post_graduation',
          qualification_name: staffInfo.post_graduation_degree,
          institution_name: staffInfo.post_graduation_university || 'Not specified',
          university_board: staffInfo.post_graduation_university,
          completion_year: staffInfo.post_graduation_year,
          percentage: staffInfo.post_graduation_percentage,
          specialization: staffInfo.post_graduation_specialization
        });
      }

      if (staffInfo.phd_subject) {
        educationTimeline.push({
          qualification_level: 'phd',
          qualification_name: 'Doctor of Philosophy (PhD)',
          institution_name: staffInfo.phd_university || 'Not specified',
          university_board: staffInfo.phd_university,
          completion_year: staffInfo.phd_year,
          percentage: null,
          specialization: staffInfo.phd_subject,
          thesis_title: staffInfo.phd_thesis_title
        });
      }

      // Sort by completion year
      educationTimeline.sort((a, b) => (a.completion_year || 0) - (b.completion_year || 0));
    }

    // Get professional experience if staff_id exists (with error handling)
    let experienceTimeline = [];
    if (staffInfo && staffInfo.staff_id) {
      try {
        const [experienceResult] = await db.query(`
          SELECT
            pe.job_title,
            pe.department,
            pe.employment_type,
            pe.job_category,
            pe.organization_name,
            pe.organization_type,
            pe.location,
            pe.start_date,
            pe.end_date,
            pe.is_current,
            pe.job_description,
            pe.key_achievements,
            pe.salary_range,
            pe.reporting_to,
            pe.team_size_managed
          FROM staff_professional_experience pe
          WHERE pe.staff_id = ?
          ORDER BY pe.start_date DESC
        `, [staffInfo.staff_id]);
        experienceTimeline = experienceResult;
      } catch (expError) {
        console.log('Professional experience table not found, using fallback data');
        experienceTimeline = [];
      }
    }

    // Enhanced principal profile response
    const enhancedPrincipal = {
      ...principal,
      educationTimeline,
      experienceTimeline,
      employee_id: principal.employee_id || `EMP${String(principal.id).padStart(4, '0')}`,
      designation: principal.designation || "Principal",
      department: principal.department || "Administration",
      phone: principal.phone || "Not provided",
      joining_date: principal.joining_date || null,
      employment_type: principal.employment_type || "permanent",
      special_skills: principal.special_skills || "Educational Leadership, Administration, Strategic Planning",
      languages_known: principal.languages_known || "English, Hindi"
    };

    res.json({
      success: true,
      principal: enhancedPrincipal
    });
  } catch (error) {
    console.error('Error fetching principal profile:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching principal profile',
      error: error.message
    });
  }
});

// Principal CV Generation API endpoint
router.post('/api/generate-cv-pdf', async (req, res) => {
  try {
    console.log('🔄 Generating principal CV PDF...');
    const { principalData } = req.body;

    // Generate unique filename
    const timestamp = Date.now();
    const filename = `principal_cv_${timestamp}.pdf`;
    const filePath = path.join(__dirname, '../public/temp', filename);

    // Ensure temp directory exists
    const tempDir = path.join(__dirname, '../public/temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Create PDF document
    const doc = new PDFDocument({
      size: 'A4',
      margins: { top: 50, bottom: 50, left: 50, right: 50 }
    });

    // Pipe to file
    doc.pipe(fs.createWriteStream(filePath));

    // Add content to PDF
    doc.fontSize(20).font('Helvetica-Bold').text('CURRICULUM VITAE', { align: 'center' });
    doc.moveDown(1);

    // Personal Information
    doc.fontSize(16).font('Helvetica-Bold').text('PERSONAL INFORMATION', { underline: true });
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Name: ${principalData.name || 'N/A'}`);
    doc.text(`Email: ${principalData.email || 'N/A'}`);
    doc.text(`Phone: ${principalData.phone || 'N/A'}`);
    doc.text(`Employee ID: ${principalData.employee_id || 'N/A'}`);
    doc.text(`Designation: ${principalData.designation || 'Principal'}`);
    doc.text(`Department: ${principalData.department || 'Administration'}`);
    if (principalData.joining_date) {
      doc.text(`Joining Date: ${new Date(principalData.joining_date).toLocaleDateString()}`);
    }
    doc.moveDown(1);

    // Educational Qualifications
    if (principalData.educationTimeline && principalData.educationTimeline.length > 0) {
      doc.fontSize(16).font('Helvetica-Bold').text('EDUCATIONAL QUALIFICATIONS', { underline: true });
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica');

      principalData.educationTimeline.forEach(edu => {
        doc.font('Helvetica-Bold').text(`${edu.qualification_name || 'N/A'}`);
        doc.font('Helvetica').text(`Institution: ${edu.institution_name || 'N/A'}`);
        if (edu.university_board) doc.text(`University/Board: ${edu.university_board}`);
        if (edu.specialization) doc.text(`Specialization: ${edu.specialization}`);
        if (edu.completion_year) doc.text(`Year: ${edu.completion_year}`);
        if (edu.percentage) doc.text(`Percentage: ${edu.percentage}%`);
        doc.moveDown(0.5);
      });
      doc.moveDown(0.5);
    }

    // Professional Experience
    if (principalData.experienceTimeline && principalData.experienceTimeline.length > 0) {
      doc.fontSize(16).font('Helvetica-Bold').text('PROFESSIONAL EXPERIENCE', { underline: true });
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica');

      principalData.experienceTimeline.forEach(exp => {
        doc.font('Helvetica-Bold').text(`${exp.job_title || 'N/A'}`);
        doc.font('Helvetica').text(`Organization: ${exp.organization_name || 'N/A'}`);
        if (exp.department) doc.text(`Department: ${exp.department}`);
        if (exp.location) doc.text(`Location: ${exp.location}`);
        const startDate = exp.start_date ? new Date(exp.start_date).toLocaleDateString() : 'N/A';
        const endDate = exp.is_current ? 'Present' : (exp.end_date ? new Date(exp.end_date).toLocaleDateString() : 'N/A');
        doc.text(`Duration: ${startDate} - ${endDate}`);
        if (exp.job_description) doc.text(`Description: ${exp.job_description}`);
        doc.moveDown(0.5);
      });
      doc.moveDown(0.5);
    }

    // Skills and Achievements
    if (principalData.special_skills) {
      doc.fontSize(16).font('Helvetica-Bold').text('SKILLS', { underline: true });
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica').text(principalData.special_skills);
      doc.moveDown(1);
    }

    if (principalData.awards_received) {
      doc.fontSize(16).font('Helvetica-Bold').text('AWARDS & ACHIEVEMENTS', { underline: true });
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica').text(principalData.awards_received);
      doc.moveDown(1);
    }

    // Additional Information
    if (principalData.languages_known) {
      doc.fontSize(16).font('Helvetica-Bold').text('LANGUAGES', { underline: true });
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica').text(principalData.languages_known);
      doc.moveDown(1);
    }

    // Finalize PDF
    doc.end();

    // Wait for PDF to be written
    doc.on('end', () => {
      console.log('✅ Principal CV PDF generated successfully');
      res.json({
        success: true,
        message: 'Principal CV PDF generated successfully',
        filename: filename,
        downloadUrl: `/temp/${filename}`
      });
    });

  } catch (error) {
    console.error('❌ Error generating principal CV PDF:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating principal CV PDF',
      error: error.message
    });
  }
});

// Reports and Analytics
router.get('/reports', principalController.getReports);
router.get('/reports/academic', principalController.getAcademicReports);
router.get('/reports/attendance', principalController.getAttendanceReports);
router.get('/reports/performance', principalController.getPerformanceReports);

// API endpoints for real-time data
router.get('/api/dashboard-stats', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get real-time statistics
    const [stats] = await db.query(`
      SELECT
        (SELECT COUNT(*) FROM users WHERE role = 'teacher' AND is_active = 1) as total_teachers,
        (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 1) as total_students,
        (SELECT COUNT(*) FROM classes) as total_classes,
        (SELECT COUNT(*) FROM subjects) as total_subjects
    `);

    // Get today's lecture statistics
    const [lectureStats] = await db.query(`
      SELECT
        COUNT(*) as total_lectures_today,
        SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_lectures,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_lectures
      FROM teacher_lectures
      WHERE date = CURDATE()
    `);

    // Get syllabus progress
    const [syllabusProgress] = await db.query(`
      SELECT
        AVG(CASE WHEN status = 'completed' THEN 100 ELSE 0 END) as avg_completion
      FROM teacher_lectures
    `);

    res.json({
      success: true,
      data: {
        ...stats[0],
        ...lectureStats[0],
        syllabus_completion: syllabusProgress[0]?.avg_completion || 0
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch statistics' });
  }
});

// API endpoint for academic progress data
router.get('/api/academic-progress', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get class-wise syllabus completion
    const [classProgress] = await db.query(`
      SELECT
        c.name as class_name,
        s.name as subject_name,
        COUNT(tl.id) as total_topics,
        SUM(CASE WHEN tl.status = 'completed' THEN 1 ELSE 0 END) as completed_topics,
        ROUND((SUM(CASE WHEN tl.status = 'completed' THEN 1 ELSE 0 END) / COUNT(tl.id)) * 100, 2) as completion_percentage
      FROM classes c
      LEFT JOIN teacher_lectures tl ON c.name = tl.class_name
      LEFT JOIN subjects s ON s.name = tl.subject_name
      GROUP BY c.name, s.name
      ORDER BY c.name, s.name
    `);

    res.json({
      success: true,
      data: classProgress
    });
  } catch (error) {
    console.error('Error fetching academic progress:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch academic progress' });
  }
});

// API endpoint for teacher performance data
router.get('/api/teacher-performance', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get teacher performance metrics
    const [teacherPerformance] = await db.query(`
      SELECT
        u.name as teacher_name,
        u.email as teacher_email,
        COUNT(tl.id) as total_lectures,
        SUM(CASE WHEN tl.status = 'delivered' THEN 1 ELSE 0 END) as delivered_lectures,
        SUM(CASE WHEN tl.status = 'pending' AND tl.date < CURDATE() THEN 1 ELSE 0 END) as overdue_lectures,
        ROUND((SUM(CASE WHEN tl.status = 'delivered' THEN 1 ELSE 0 END) / COUNT(tl.id)) * 100, 2) as completion_rate
      FROM users u
      LEFT JOIN teacher_lectures tl ON u.id = tl.teacher_id
      WHERE u.role = 'teacher' AND u.is_active = 1
      GROUP BY u.id, u.name, u.email
      ORDER BY completion_rate DESC
    `);

    res.json({
      success: true,
      data: teacherPerformance
    });
  } catch (error) {
    console.error('Error fetching teacher performance:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch teacher performance' });
  }
});

// API endpoint for classroom details
router.get('/api/classroom/:roomId', async (req, res) => {
  try {
    const { roomId } = req.params;
    const db = require('../config/database');

    console.log('🔍 API: Fetching classroom details for roomId:', roomId);

    // Get detailed classroom information from rooms table
    // Try to find by ID first, then by room_number
    const [roomInfo] = await db.query(`
      SELECT
        id,
        room_number,
        floor,
        capacity,
        building
      FROM rooms
      WHERE id = ? OR room_number = ? OR room_number = ?
    `, [roomId, roomId, `Room ${roomId}`]);

    console.log('🔍 API: Room query result:', roomInfo);

    if (roomInfo.length === 0) {
      console.log('❌ API: Room not found for roomId:', roomId);
      return res.status(404).json({ success: false, error: 'Room not found' });
    }

    const classroom = roomInfo[0];

    // Get students assigned to this room through student_classrooms relationship
    // First try the students table, if it doesn't exist, use users table
    let students = [];
    try {
      const [studentsResult] = await db.query(`
        SELECT
          id,
          student_id,
          name,
          father_name,
          mother_name,
          gender,
          class,
          section,
          session,
          roll_no,
          contact_no,
          admission_no,
          dob,
          height,
          weight,
          caste_category_name,
          religion_name,
          medium_name,
          bpl,
          disability,
          cur_address,
          village_ward,
          pin_code
        FROM students
        WHERE (room_number = ? OR room_number = ?) AND is_active = 1
        ORDER BY class, section, roll_no, name
      `, [classroom.room_number, `Room ${roomId}`]);
      students = studentsResult;
      console.log('🔍 API: Students query result from students table:', students.length, 'students found');
    } catch (error) {
      console.log('🔍 API: Students table not found, trying student_classrooms approach...');

      // Try to get students through student_classrooms and classrooms relationship
      try {
        const [studentsResult] = await db.query(`
          SELECT DISTINCT
            u.id,
            u.username as student_id,
            u.name,
            u.full_name as father_name,
            '' as mother_name,
            'Male' as gender,
            cl.grade as class,
            cl.section,
            c.session,
            '' as roll_no,
            '' as contact_no,
            '' as admission_no,
            u.date_of_birth as dob,
            0 as height,
            0 as weight,
            '' as caste_category_name,
            '' as religion_name,
            '' as medium_name,
            'No' as bpl,
            'No' as disability,
            '' as cur_address,
            '' as village_ward,
            '' as pin_code
          FROM student_classrooms sc
          JOIN users u ON sc.student_id = u.id
          JOIN classrooms c ON sc.classroom_id = c.id
          JOIN classes cl ON c.class_id = cl.id
          JOIN rooms r ON c.room_id = r.id
          WHERE r.id = ? AND u.role = 'student' AND sc.status = 'active'
          ORDER BY cl.grade, cl.section, u.name
        `, [classroom.id]);
        students = studentsResult;
        console.log('🔍 API: Students query result from student_classrooms:', students.length, 'students found');
      } catch (error2) {
        console.log('🔍 API: No student_classrooms table either, trying users table directly...');

        // Final fallback: try to get students from users table directly
        try {
          const [studentsResult] = await db.query(`
            SELECT DISTINCT
              u.id,
              u.username as student_id,
              u.name,
              u.full_name as father_name,
              '' as mother_name,
              'Unknown' as gender,
              '' as class,
              '' as section,
              '' as session,
              '' as roll_no,
              '' as contact_no,
              '' as admission_no,
              u.date_of_birth as dob,
              0 as height,
              0 as weight,
              '' as caste_category_name,
              '' as religion_name,
              '' as medium_name,
              'No' as bpl,
              'No' as disability,
              '' as cur_address,
              '' as village_ward,
              '' as pin_code
            FROM users u
            WHERE u.role = 'student'
            ORDER BY u.name
            LIMIT 5
          `);
          students = studentsResult;
          console.log('🔍 API: Students query result from users table (sample):', students.length, 'students found');
        } catch (error3) {
          console.log('🔍 API: No student data available, using empty array');
          students = [];
        }
      }
    }

    // Get IT equipment for this room using foreign key relationship from inventory_items
    const [itEquipment] = await db.query(`
      SELECT
        i.name as item_name,
        CASE
          WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN 'projector'
          WHEN i.name LIKE '%UPS%' THEN 'other'
          WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN 'desktop'
          WHEN i.name LIKE '%Laptop%' THEN 'laptop'
          WHEN i.name LIKE '%PANEL%' THEN 'other'
          WHEN i.name LIKE '%CAMERA%' THEN 'other'
          WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN 'printer'
          WHEN i.name LIKE '%Router%' THEN 'network'
          ELSE 'other'
        END as item_type,
        i.serial_number,
        i.status,
        i.manufacturer,
        i.model,
        i.purchase_date,
        i.warranty_expiry,
        i.notes,
        'good' as condition_status,
        NULL as mac_address,
        NULL as ip_address,
        NULL as hostname,
        i.description,
        i.purchase_cost
      FROM inventory_items i
      WHERE i.room_id = ?
      ORDER BY
        CASE
          WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN 1
          WHEN i.name LIKE '%UPS%' THEN 2
          WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN 3
          WHEN i.name LIKE '%Laptop%' THEN 4
          WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN 5
          ELSE 6
        END, i.name
    `, [classroom.id]);

    // Get electrical equipment for this room with detailed breakdown
    const [electricalEquipment] = await db.query(`
      SELECT
        item_name,
        item_type,
        serial_number,
        status,
        manufacturer,
        model,
        wattage,
        installation_date,
        notes
      FROM electrical_inventory
      WHERE room_number = ? OR room_number = ?
      ORDER BY item_type, item_name
    `, [classroom.room_number, `Room ${roomId}`]);

    console.log('🔍 API: Equipment query results:', {
      it: itEquipment.length,
      electrical: electricalEquipment.length
    });

    // Calculate gender breakdown from the new students table
    const boys = students.filter(s => s.gender === 'Male');
    const girls = students.filter(s => s.gender === 'Female');
    const others = students.filter(s => s.gender === 'Other');

    // Get class distribution
    const classDistribution = students.reduce((acc, student) => {
      const classKey = `${student.class}-${student.section || 'N/A'}`;
      if (!acc[classKey]) {
        acc[classKey] = { class: student.class, section: student.section, count: 0, students: [] };
      }
      acc[classKey].count++;
      acc[classKey].students.push(student);
      return acc;
    }, {});

    // Determine classroom assignment based on students
    let classroomName = 'Unassigned';
    if (students.length > 0) {
      const mostCommonClass = Object.values(classDistribution)
        .sort((a, b) => b.count - a.count)[0];
      if (mostCommonClass) {
        classroomName = `Class ${mostCommonClass.class}${mostCommonClass.section ? ` - ${mostCommonClass.section}` : ''}`;
      }
    }

    const responseData = {
      success: true,
      data: {
        classroom: {
          room_number: classroom.room_number,
          floor: classroom.floor,
          capacity: classroom.capacity,
          building: classroom.building,
          full_name: classroomName,
          incharge_name: null // Will be updated when teacher assignment is implemented
        },
        students: {
          total: students.length,
          boys: boys.length,
          girls: girls.length,
          others: others.length,
          list: students,
          boys_list: boys,
          girls_list: girls,
          others_list: others,
          class_distribution: Object.values(classDistribution)
        },
        equipment: {
          it: itEquipment,
          electrical: electricalEquipment,
          summary: {
            total_it: itEquipment.length,
            total_electrical: electricalEquipment.length,
            working_it: itEquipment.filter(item => item.status === 'working').length,
            working_electrical: electricalEquipment.filter(item => item.status === 'working').length,
            faulty_it: itEquipment.filter(item => item.status === 'faulty').length,
            faulty_electrical: electricalEquipment.filter(item => item.status === 'faulty').length
          }
        }
      }
    };

    console.log('✅ API: Sending response for room', roomId, ':', {
      room_number: classroom.room_number,
      students: students.length,
      it_equipment: itEquipment.length,
      electrical_equipment: electricalEquipment.length
    });

    res.json(responseData);
  } catch (error) {
    console.error('Error fetching classroom details:', error);

    // Return a more user-friendly response with basic room info
    res.json({
      success: true,
      data: {
        classroom: {
          room_number: `Room ${req.params.roomId}`,
          floor: 1,
          capacity: 50,
          building: null,
          full_name: 'Classroom Information',
          incharge_name: null
        },
        students: {
          total: 0,
          boys: 0,
          girls: 0,
          others: 0,
          list: [],
          boys_list: [],
          girls_list: [],
          others_list: [],
          class_distribution: []
        },
        equipment: {
          it: [],
          electrical: [],
          summary: {
            total_it: 0,
            total_electrical: 0,
            working_it: 0,
            working_electrical: 0,
            faulty_it: 0,
            faulty_electrical: 0
          }
        }
      },
      message: 'Classroom data partially available. Some features may be limited due to database configuration.'
    });
  }
});

// Student-Subject-Trade Analysis
router.get('/student-trade-analysis', async (req, res) => {
    try {
        const db = require('../config/database');

        // Get student-subject-trade relationships
        const [studentSubjectTrades] = await db.query(`
            SELECT
                s.id as student_id,
                s.student_id as roll_number,
                s.name as student_name,
                s.class,
                s.section,
                s.trade as original_trade,
                sub.id as subject_id,
                sub.code as subject_code,
                sub.name as subject_name,
                sub.stream,
                sub.subject_category_new,
                ssm.theory_marks,
                ssm.practical_marks,
                ssm.internal_marks,
                ssm.total_marks,
                ssm.max_marks,
                ssm.academic_session,
                CASE
                    WHEN s.class = '10' THEN 'General'
                    WHEN s.trade = 'Biology' THEN 'Medical'
                    WHEN s.trade IN ('Physics', 'Chemistry', 'Mathematics', 'Computer Science') THEN 'Non Medical'
                    WHEN s.trade IN ('Commerce', 'Economics', 'Accountancy') THEN 'Commerce'
                    WHEN s.trade IN ('History', 'Geography', 'Political Science', 'Psychology', 'Sociology', 'Philosophy') THEN 'Humanities'
                    ELSE s.trade
                END as trade_category
            FROM students s
            LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id
            LEFT JOIN subjects sub ON ssm.subject_id = sub.id
            WHERE s.class IN ('10', '11', '12') AND s.is_active = 1
            ORDER BY s.class, s.section, s.name, sub.code
        `);

        // Get trade distribution statistics
        const [tradeStats] = await db.query(`
            SELECT
                CASE
                    WHEN s.class = '10' THEN 'General'
                    WHEN s.trade = 'Biology' THEN 'Medical'
                    WHEN s.trade IN ('Physics', 'Chemistry', 'Mathematics', 'Computer Science') THEN 'Non Medical'
                    WHEN s.trade IN ('Commerce', 'Economics', 'Accountancy') THEN 'Commerce'
                    WHEN s.trade IN ('History', 'Geography', 'Political Science', 'Psychology', 'Sociology', 'Philosophy') THEN 'Humanities'
                    ELSE s.trade
                END as trade_category,
                s.class,
                COUNT(DISTINCT s.id) as student_count,
                GROUP_CONCAT(DISTINCT s.trade ORDER BY s.trade) as original_trades
            FROM students s
            WHERE s.class IN ('10', '11', '12') AND s.is_active = 1
            GROUP BY trade_category, s.class
            ORDER BY s.class, trade_category
        `);

        // Get subject distribution by trade
        const [subjectStats] = await db.query(`
            SELECT
                sub.name as subject_name,
                sub.code as subject_code,
                sub.stream,
                sub.subject_category_new,
                COUNT(DISTINCT ssm.student_id) as enrolled_students,
                CASE
                    WHEN s.class = '10' THEN 'General'
                    WHEN s.trade = 'Biology' THEN 'Medical'
                    WHEN s.trade IN ('Physics', 'Chemistry', 'Mathematics', 'Computer Science') THEN 'Non Medical'
                    WHEN s.trade IN ('Commerce', 'Economics', 'Accountancy') THEN 'Commerce'
                    WHEN s.trade IN ('History', 'Geography', 'Political Science', 'Psychology', 'Sociology', 'Philosophy') THEN 'Humanities'
                    ELSE s.trade
                END as trade_category
            FROM subjects sub
            LEFT JOIN student_subject_marks ssm ON sub.id = ssm.subject_id
            LEFT JOIN students s ON ssm.student_id = s.id
            WHERE s.class IN ('10', '11', '12') AND s.is_active = 1
            GROUP BY sub.id, trade_category
            HAVING enrolled_students > 0
            ORDER BY trade_category, sub.code
        `);

        res.render('principal/student-trade-analysis', {
            title: 'Student-Subject-Trade Analysis',
            layout: 'layouts/principal',
            currentPage: 'student-trade-analysis',
            studentSubjectTrades,
            tradeStats,
            subjectStats
        });

    } catch (error) {
        console.error('Error loading student-trade analysis:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Error loading student-trade analysis data',
            error: { status: 500 },
            layout: 'layouts/principal'
        });
    }
});

// Student Data Management (Read-only for Principal)
router.get('/students', async (req, res) => {
    try {
        const db = require('../config/database');

        // Get pagination parameters
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 25;
        const offset = (page - 1) * limit;

        // Get filter parameters
        const filters = {
            class: req.query.class || '',
            section: req.query.section || '',
            session: req.query.session || '',
            gender: req.query.gender || '',
            stream: req.query.stream || '',
            bpl: req.query.bpl || '',
            disability: req.query.disability || '',
            search: req.query.search || ''
        };

        // Build WHERE clause
        let whereConditions = ['is_active = 1'];
        let queryParams = [];

        if (filters.class) {
            whereConditions.push('class = ?');
            queryParams.push(filters.class);
        }

        if (filters.section) {
            whereConditions.push('section = ?');
            queryParams.push(filters.section);
        }

        if (filters.session) {
            whereConditions.push('session = ?');
            queryParams.push(filters.session);
        }

        if (filters.gender) {
            whereConditions.push('gender = ?');
            queryParams.push(filters.gender);
        }

        if (filters.stream) {
            whereConditions.push('stream = ?');
            queryParams.push(filters.stream);
        }

        if (filters.bpl) {
            whereConditions.push('bpl = ?');
            queryParams.push(filters.bpl);
        }

        if (filters.disability) {
            whereConditions.push('disability = ?');
            queryParams.push(filters.disability);
        }

        if (filters.search) {
            whereConditions.push('(name LIKE ? OR student_id LIKE ? OR father_name LIKE ? OR roll_no LIKE ? OR admission_no LIKE ?)');
            const searchPattern = `%${filters.search}%`;
            queryParams.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get total count
        const [countResult] = await db.query(
            `SELECT COUNT(*) as total FROM students WHERE ${whereClause}`,
            queryParams
        );
        const totalStudents = countResult[0].total;
        const totalPages = Math.ceil(totalStudents / limit);

        // Get students with pagination
        const [students] = await db.query(
            `SELECT * FROM students
             WHERE ${whereClause}
             ORDER BY class, section, roll_no, name
             LIMIT ? OFFSET ?`,
            [...queryParams, limit, offset]
        );

        // Check if export is requested
        if (req.query.export === 'excel') {
            const XLSX = require('xlsx');

            // Prepare data for export
            const exportData = students.map(student => ({
                'S.No': student.sno || '',
                'Student ID': student.student_id || '',
                'UdiseCode': student.udise_code || '',
                'Name': student.name || '',
                'Father Name': student.father_name || '',
                'Mother Name': student.mother_name || '',
                'DOB': student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : '',
                'Gender': student.gender || '',
                'Class': student.class || '',
                'Section': student.section || '',
                'Stream': student.stream || '',
                'Trade': student.trade || '',
                'Caste Category': student.caste_category_name || '',
                'BPL': student.bpl || '',
                'Disability': student.disability || '',
                'Religion': student.religion_name || '',
                'Medium': student.medium_name || '',
                'Height': student.height || '',
                'Weight': student.weight || '',
                'Admission No': student.admission_no || '',
                'Admission Date': student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : '',
                'State': student.state_name || '',
                'District': student.district_name || '',
                'Address': student.cur_address || '',
                'Village/Ward': student.village_ward || '',
                'Gram Panchayat': student.gram_panchayat || '',
                'Pin Code': student.pin_code || '',
                'Roll No': student.roll_no || '',
                'Contact No': student.contact_no || '',
                'IFSC Code': student.ifsc_code || '',
                'Bank Name': student.bank_name || '',
                'Column1': student.column1 || '',
                'Account Holder Code': student.account_holder_code || '',
                'Account Holder': student.account_holder || '',
                'Account Holder Name': student.account_holder_name || ''
            }));

            // Create workbook and worksheet
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(exportData);

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Students');

            // Generate buffer
            const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

            // Set headers for download
            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="students_export_${new Date().toISOString().split('T')[0]}.xlsx"`);

            return res.send(buffer);
        }

        // Get filter options
        const [classes] = await db.query('SELECT DISTINCT class FROM students WHERE class IS NOT NULL AND class != "" ORDER BY class');
        const [sections] = await db.query('SELECT DISTINCT section FROM students WHERE section IS NOT NULL AND section != "" ORDER BY section');
        const [sessions] = await db.query('SELECT DISTINCT session FROM students WHERE session IS NOT NULL AND session != "" ORDER BY session');
        const [streams] = await db.query('SELECT DISTINCT stream FROM students WHERE stream IS NOT NULL AND stream != "" ORDER BY stream');

        res.render('principal/students', {
            title: 'Student Data Overview',
            pageTitle: 'Student Data Overview',
            layout: 'layouts/principal',
            currentPage: 'students',
            students,
            classes: classes.map(c => c.class),
            sections: sections.map(s => s.section),
            sessions: sessions.map(s => s.session),
            streams: streams.map(s => s.stream),
            pagination: {
                currentPage: page,
                totalPages,
                totalStudents,
                limit,
                hasNext: page < totalPages,
                hasPrev: page > 1
            },
            filters,
            flashSuccess: req.session.flashSuccess,
            flashError: req.session.flashError,
            flashInfo: req.session.flashInfo
        });

        // Clear flash messages
        delete req.session.flashSuccess;
        delete req.session.flashError;
        delete req.session.flashInfo;

    } catch (error) {
        console.error('Error loading student data page:', error);
        req.session.flashError = 'Error loading student data';
        res.redirect('/principal/dashboard');
    }
});

// Get trash data for modal (must come before /:id route)
router.get('/students/trash-data', async (req, res) => {
    try {
        const db = require('../config/database');

        // Get soft deleted students
        const [students] = await db.query(
            `SELECT
                id, student_id, name, class, section, contact_no, updated_at
             FROM students
             WHERE is_active = 0
             ORDER BY updated_at DESC
             LIMIT 50`
        );

        res.json({
            success: true,
            students: students
        });

    } catch (error) {
        console.error('Error fetching trash data:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching trash data'
        });
    }
});

// Get single student data for modal
router.get('/students/:id', async (req, res) => {
    try {
        const db = require('../config/database');
        const studentId = req.params.id;

        console.log(`🔍 Fetching student data for ID: ${studentId}`);

        const [students] = await db.query(
            'SELECT * FROM students WHERE id = ? AND is_active = 1',
            [studentId]
        );

        if (students.length === 0) {
            console.log(`❌ Student not found with ID: ${studentId}`);
            return res.status(404).json({
                success: false,
                message: 'Student not found'
            });
        }

        const student = students[0];
        console.log(`✅ Found student: ${student.name} (Class: ${student.class}, Trade: ${student.trade})`);

        // Get student's subjects and marks with academic session
        let studentSubjects = [];
        try {
            console.log(`📚 Fetching subjects for student ${studentId} with session ${student.academic_session || student.session}`);

            // Use the student's academic session or session field
            const academicSession = student.academic_session || student.session || '2023-2024';

            const [subjectResults] = await db.query(`
                SELECT
                    sub.id as subject_id,
                    sub.code as subject_code,
                    sub.name as subject_name,
                    sub.stream,
                    COALESCE(ssm.theory_marks, 0) as theory_marks,
                    COALESCE(ssm.practical_marks, 0) as practical_marks,
                    COALESCE(ssm.internal_marks, 0) as internal_marks,
                    COALESCE(ssm.total_marks, 0) as total_marks,
                    COALESCE(ssm.max_marks, 100) as max_marks,
                    ssm.academic_session
                FROM student_subject_marks ssm
                JOIN subjects sub ON ssm.subject_id = sub.id
                WHERE ssm.student_id = ? AND ssm.academic_session = ?
                ORDER BY sub.code
            `, [studentId, academicSession]);

            console.log(`📊 Found ${subjectResults.length} subjects for student ${studentId} in session ${academicSession}`);

            // Process subjects and add calculated fields
            studentSubjects = subjectResults.map(subject => {
                const totalMarks = subject.total_marks || 0;
                const maxMarks = subject.max_marks || 100;
                const percentage = maxMarks > 0 ? Math.round((totalMarks / maxMarks) * 100 * 100) / 100 : 0;

                // Determine grade
                let grade = 'F';
                if (percentage >= 90) grade = 'A+';
                else if (percentage >= 80) grade = 'A';
                else if (percentage >= 70) grade = 'B+';
                else if (percentage >= 60) grade = 'B';
                else if (percentage >= 50) grade = 'C+';
                else if (percentage >= 40) grade = 'C';
                else if (percentage >= 33) grade = 'D';

                // Determine pass/fail
                const resultStatus = percentage >= 33 ? 'PASS' : 'FAIL';

                // Determine subject classification and if it's included in grand total
                let subjectClassification = 'Other';
                let includeInGrandTotal = false;

                const code = subject.subject_code;
                const subjectName = subject.subject_name.toLowerCase();
                const stream = subject.stream;

                // Core subject identification based on actual database subject codes
                // Language subjects (codes 1, 2) - Always core
                if (['1', '2'].includes(code)) {
                    subjectClassification = 'Compulsory Language';
                    includeInGrandTotal = true;
                }
                // Science subjects (codes 28, 52, 53, 54) - Core for science streams
                else if (['28', '52', '53'].includes(code)) {
                    // Physics (28), Chemistry (52), Mathematics (53) - Core for Non-Medical
                    subjectClassification = 'Core Science';
                    includeInGrandTotal = true;
                }
                else if (code === '54') {
                    // Biology (54) - Core for Medical, Additional for Non-Medical
                    if (student.trade === 'Biology') {
                        subjectClassification = 'Core Medical';
                        includeInGrandTotal = true;
                    } else {
                        subjectClassification = 'Additional Optional';
                        includeInGrandTotal = false;
                    }
                }
                // Commerce subjects (codes 141, 142, 26) - Core for commerce streams
                else if (['141', '142', '26'].includes(code)) {
                    subjectClassification = 'Core Commerce';
                    includeInGrandTotal = true;
                }
                // Additional/Optional subjects (codes 146, 49) - Not included in grand total
                else if (['146', '49'].includes(code)) {
                    subjectClassification = 'Additional Compulsory';
                    includeInGrandTotal = false;
                }
                // For any other subjects, check by name and stream
                else if (subjectName.includes('english') || subjectName.includes('punjabi') || subjectName.includes('hindi')) {
                    subjectClassification = 'Compulsory Language';
                    includeInGrandTotal = true;
                }
                else if (subjectName.includes('physics') || subjectName.includes('chemistry') || subjectName.includes('mathematics')) {
                    subjectClassification = 'Core Science';
                    includeInGrandTotal = true;
                }
                else if (subjectName.includes('biology')) {
                    if (student.trade === 'Biology') {
                        subjectClassification = 'Core Medical';
                        includeInGrandTotal = true;
                    } else {
                        subjectClassification = 'Additional Optional';
                        includeInGrandTotal = false;
                    }
                }
                else if (subjectName.includes('commerce') || subjectName.includes('economics') || subjectName.includes('accountancy')) {
                    subjectClassification = 'Core Commerce';
                    includeInGrandTotal = true;
                }
                else if (subjectName.includes('computer')) {
                    if (student.trade === 'Computer Science') {
                        subjectClassification = 'Core Science';
                        includeInGrandTotal = true;
                    } else {
                        subjectClassification = 'Additional Optional';
                        includeInGrandTotal = false;
                    }
                }
                else if (subjectName.includes('history') || subjectName.includes('geography') || subjectName.includes('political')) {
                    subjectClassification = 'Core Humanities';
                    includeInGrandTotal = true;
                }
                // Default: if it's a main stream subject, include it as core
                else if (stream && (stream.includes('Science') || stream.includes('Commerce') || stream.includes('Humanities') || stream.includes('All Streams'))) {
                    subjectClassification = 'Core Subject';
                    includeInGrandTotal = true;
                }
                // Everything else is additional
                else {
                    subjectClassification = 'Additional';
                    includeInGrandTotal = false;
                }

                console.log(`📚 Subject: ${subject.subject_name} (Code: ${code}) → ${subjectClassification} (Core: ${includeInGrandTotal})`);

                return {
                    ...subject,
                    percentage,
                    grade,
                    result_status: resultStatus,
                    subject_classification: subjectClassification,
                    include_in_grand_total: includeInGrandTotal
                };
            });

            console.log(`📚 Found ${studentSubjects.length} subjects for student ${studentId}`);
        } catch (subjectError) {
            console.error('Error fetching student subjects:', subjectError);
            studentSubjects = []; // Continue with empty subjects if query fails
        }

        // Calculate trade category
        function getTradeCategory(originalTrade, studentClass) {
            if (studentClass === '10') {
                return 'General';
            }

            const tradeMapping = {
                'Biology': 'Medical',
                'Physics': 'Non Medical',
                'Chemistry': 'Non Medical',
                'Mathematics': 'Non Medical',
                'Computer Science': 'Non Medical',
                'Commerce': 'Commerce',
                'Economics': 'Commerce',
                'Accountancy': 'Commerce',
                'History': 'Humanities',
                'Geography': 'Humanities',
                'Political Science': 'Humanities',
                'Psychology': 'Humanities',
                'Sociology': 'Humanities',
                'Philosophy': 'Humanities'
            };

            return tradeMapping[originalTrade] || originalTrade;
        }

        // Calculate academic performance
        const coreSubjects = studentSubjects.filter(s => s.include_in_grand_total);
        const additionalSubjects = studentSubjects.filter(s => !s.include_in_grand_total);

        let grandTotalMarks = 0;
        let grandTotalMax = 0;
        let additionalMarks = 0;
        let additionalMax = 0;

        coreSubjects.forEach(subject => {
            grandTotalMarks += subject.total_marks || 0;
            grandTotalMax += subject.max_marks || 0;
        });

        additionalSubjects.forEach(subject => {
            additionalMarks += subject.total_marks || 0;
            additionalMax += subject.max_marks || 0;
        });

        const overallPercentage = grandTotalMax > 0 ? Math.round((grandTotalMarks / grandTotalMax) * 100 * 100) / 100 : 0;

        let overallGrade = 'F';
        if (overallPercentage >= 90) overallGrade = 'A+';
        else if (overallPercentage >= 80) overallGrade = 'A';
        else if (overallPercentage >= 70) overallGrade = 'B+';
        else if (overallPercentage >= 60) overallGrade = 'B';
        else if (overallPercentage >= 50) overallGrade = 'C+';
        else if (overallPercentage >= 40) overallGrade = 'C';
        else if (overallPercentage >= 33) overallGrade = 'D';

        const failedCoreSubjects = coreSubjects.filter(s => s.result_status === 'FAIL');
        const promotionStatus = failedCoreSubjects.length >= 2 ? 'FAIL' : 'PASS';

        // Calculate profile completion and rating for student
        const profileCompletion = calculateStudentProfileCompletion(student);
        const ratingScore = calculateStudentRating(student);

        // Enhanced student response
        const enhancedStudent = {
            ...student,
            profile_completion_percentage: profileCompletion,
            rating_score: ratingScore,
            trade_category: getTradeCategory(student.trade, student.class),
            subjects: studentSubjects,
            academic_performance: {
                core_subjects: coreSubjects,
                additional_subjects: additionalSubjects,
                grand_total_marks: grandTotalMarks,
                grand_total_max: grandTotalMax,
                additional_marks: additionalMarks,
                additional_max: additionalMax,
                overall_percentage: overallPercentage,
                overall_grade: overallGrade,
                promotion_status: promotionStatus,
                failed_core_subjects: failedCoreSubjects.length
            }
        };

        console.log(`✅ Successfully prepared student data for: ${student.name}`);
        console.log(`📊 Academic performance: ${enhancedStudent.academic_performance.overall_percentage}% (${enhancedStudent.academic_performance.promotion_status})`);

        res.json({
            success: true,
            student: enhancedStudent
        });

    } catch (error) {
        console.error('❌ Error fetching student data:', error);
        console.error('❌ Error details:', error.message);
        console.error('❌ Stack trace:', error.stack);
        res.status(500).json({
            success: false,
            message: 'Error fetching student data: ' + error.message
        });
    }
});

// Update single student (read-only for principal, but keeping for consistency)
router.put('/students/:id', async (req, res) => {
    try {
        const db = require('../config/database');
        const studentId = req.params.id;
        const updateData = req.body;

        // Remove empty values
        Object.keys(updateData).forEach(key => {
            if (updateData[key] === '' || updateData[key] === null) {
                delete updateData[key];
            }
        });

        if (Object.keys(updateData).length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No data to update'
            });
        }

        const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
        const updateValues = Object.values(updateData);
        updateValues.push(studentId);

        await db.query(
            `UPDATE students SET ${updateFields}, updated_at = NOW() WHERE id = ? AND is_active = 1`,
            updateValues
        );

        res.json({
            success: true,
            message: 'Student updated successfully'
        });

    } catch (error) {
        console.error('Error updating student:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating student'
        });
    }
});

// Export students data route
router.get('/students/export', async (req, res) => {
    try {
        const db = require('../config/database');

        // Get filter parameters (same as students route)
        const filters = {
            class: req.query.class || '',
            section: req.query.section || '',
            session: req.query.session || '',
            gender: req.query.gender || '',
            stream: req.query.stream || '',
            bpl: req.query.bpl || '',
            disability: req.query.disability || '',
            search: req.query.search || ''
        };

        // Build WHERE clause
        let whereConditions = ['s.is_active = 1'];
        let queryParams = [];

        if (filters.class) {
            whereConditions.push('s.class = ?');
            queryParams.push(filters.class);
        }
        if (filters.section) {
            whereConditions.push('s.section = ?');
            queryParams.push(filters.section);
        }
        if (filters.session) {
            whereConditions.push('s.session = ?');
            queryParams.push(filters.session);
        }
        if (filters.gender) {
            whereConditions.push('s.gender = ?');
            queryParams.push(filters.gender);
        }
        if (filters.stream) {
            whereConditions.push('s.stream = ?');
            queryParams.push(filters.stream);
        }
        if (filters.bpl) {
            whereConditions.push('s.bpl = ?');
            queryParams.push(filters.bpl);
        }
        if (filters.disability) {
            whereConditions.push('s.disability = ?');
            queryParams.push(filters.disability);
        }
        if (filters.search) {
            whereConditions.push('(s.name LIKE ? OR s.student_id LIKE ? OR s.father_name LIKE ? OR s.roll_no LIKE ?)');
            const searchTerm = `%${filters.search}%`;
            queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get all students matching filters (no pagination for export)
        const [students] = await db.query(
            `SELECT
                s.id, s.student_id, s.udise_code, s.name, s.father_name, s.mother_name,
                s.dob, s.gender, s.class, s.section, s.stream, s.trade, s.caste_category_name,
                s.bpl, s.disability, s.religion_name, s.medium_name, s.height, s.weight,
                s.admission_no, s.admission_date, s.state_name, s.district_name, s.cur_address,
                s.village_ward, s.gram_panchayat, s.pin_code, s.roll_no, s.contact_no,
                s.ifsc_code, s.bank_name, s.account_holder, s.account_holder_name,
                s.account_holder_code, s.session, s.created_at, s.updated_at
             FROM students s
             WHERE ${whereClause}
             ORDER BY s.class, s.section, s.roll_no, s.name`,
            queryParams
        );

        // Prepare CSV content
        const headers = [
            'S.No', 'Student ID', 'UdiseCode', 'Name', 'Father Name', 'Mother Name', 'DOB', 'Gender',
            'Class', 'Section', 'Stream', 'Trade', 'Caste Category', 'BPL', 'Disability',
            'Religion', 'Medium', 'Height', 'Weight', 'Admission No', 'Admission Date',
            'State', 'District', 'Address', 'Village/Ward', 'Gram Panchayat', 'Pin Code',
            'Roll No', 'Contact No', 'IFSC Code', 'Bank Name', 'Account Holder',
            'Account Holder Name', 'Account Holder Code', 'Session'
        ];

        let csvContent = headers.join(',') + '\n';

        students.forEach((student, index) => {
            const row = [
                index + 1,
                student.student_id || '',
                student.udise_code || '',
                student.name || '',
                student.father_name || '',
                student.mother_name || '',
                student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : '',
                student.gender || '',
                student.class || '',
                student.section || '',
                student.stream || '',
                student.trade || '',
                student.caste_category_name || '',
                student.bpl || '',
                student.disability || '',
                student.religion_name || '',
                student.medium_name || '',
                student.height || '',
                student.weight || '',
                student.admission_no || '',
                student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : '',
                student.state_name || '',
                student.district_name || '',
                student.cur_address || '',
                student.village_ward || '',
                student.gram_panchayat || '',
                student.pin_code || '',
                student.roll_no || '',
                student.contact_no || '',
                student.ifsc_code || '',
                student.bank_name || '',
                student.account_holder || '',
                student.account_holder_name || '',
                student.account_holder_code || '',
                student.session || ''
            ];

            // Escape commas and quotes in data
            const escapedRow = row.map(field => {
                const fieldStr = String(field);
                if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
                    return '"' + fieldStr.replace(/"/g, '""') + '"';
                }
                return fieldStr;
            });

            csvContent += escapedRow.join(',') + '\n';
        });

        // Set headers for CSV download
        const timestamp = new Date().toISOString().split('T')[0];
        const filename = `students_export_${timestamp}.csv`;

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.send(csvContent);

    } catch (error) {
        console.error('Error exporting students:', error);
        res.status(500).json({
            success: false,
            message: 'Error exporting student data'
        });
    }
});

/**
 * Calculate student profile completion percentage
 */
function calculateStudentProfileCompletion(student) {
    let totalFields = 0;
    let completedFields = 0;

    // Basic information fields (weight: 40%)
    const basicFields = [
        'name', 'father_name', 'mother_name', 'dob', 'gender', 'student_id',
        'class', 'section', 'roll_no', 'contact_no'
    ];

    basicFields.forEach(field => {
        totalFields++;
        if (student[field] && student[field].toString().trim() !== '') {
            completedFields++;
        }
    });

    // Academic information (weight: 30%)
    const academicFields = [
        'session', 'stream', 'trade', 'medium_name', 'admission_no', 'admission_date'
    ];

    academicFields.forEach(field => {
        totalFields++;
        if (student[field] && student[field].toString().trim() !== '') {
            completedFields++;
        }
    });

    // Address information (weight: 20%)
    const addressFields = [
        'cur_address', 'village_ward', 'pin_code', 'district_name', 'state_name'
    ];

    addressFields.forEach(field => {
        totalFields++;
        if (student[field] && student[field].toString().trim() !== '') {
            completedFields++;
        }
    });

    // Additional information (weight: 10%)
    const additionalFields = [
        'religion_name', 'caste_category_name', 'height', 'weight'
    ];

    additionalFields.forEach(field => {
        totalFields++;
        if (student[field] && student[field].toString().trim() !== '') {
            completedFields++;
        }
    });

    const percentage = Math.round((completedFields / totalFields) * 100);
    return Math.min(percentage, 100); // Cap at 100%
}

/**
 * Calculate student rating score based on profile data and academic status
 */
function calculateStudentRating(student) {
    let score = 0;

    // Basic information completeness (30% weight)
    const basicFields = ['name', 'father_name', 'dob', 'gender', 'student_id'];
    const basicCompletion = basicFields.filter(field =>
        student[field] && student[field].toString().trim() !== ''
    ).length;
    score += (basicCompletion / basicFields.length) * 30;

    // Academic information (25% weight)
    const academicFields = ['class', 'section', 'roll_no', 'session'];
    const academicCompletion = academicFields.filter(field =>
        student[field] && student[field].toString().trim() !== ''
    ).length;
    score += (academicCompletion / academicFields.length) * 25;

    // Contact and address information (20% weight)
    const contactFields = ['contact_no', 'cur_address', 'pin_code'];
    const contactCompletion = contactFields.filter(field =>
        student[field] && student[field].toString().trim() !== ''
    ).length;
    score += (contactCompletion / contactFields.length) * 20;

    // Administrative completeness (15% weight)
    const adminFields = ['admission_no', 'udise_code'];
    const adminCompletion = adminFields.filter(field =>
        student[field] && student[field].toString().trim() !== ''
    ).length;
    score += (adminCompletion / adminFields.length) * 15;

    // Additional information (10% weight)
    const additionalFields = ['religion_name', 'caste_category_name'];
    const additionalCompletion = additionalFields.filter(field =>
        student[field] && student[field].toString().trim() !== ''
    ).length;
    score += (additionalCompletion / additionalFields.length) * 10;

    return Math.round(Math.min(score, 100)); // Cap at 100%
}

module.exports = router;
