/**
 * Principal Routes - Clean Version
 * Handles all routes for the Principal dashboard and functionality
 */

const express = require('express');
const router = express.Router();
const principalController = require('../controllers/principal-controller');
const { checkAuthenticated } = require('../middleware/auth');

// Middleware to check if user is principal
const checkPrincipal = (req, res, next) => {
  if (req.user && req.user.role === 'principal') {
    next();
  } else {
    res.status(403).render('error', { 
      message: 'Access denied. Principal access required.',
      layout: 'layouts/main'
    });
  }
};

// Apply authentication and principal check to all routes
router.use(checkAuthenticated);
router.use(checkPrincipal);

// Middleware to ensure principal layout is used
router.use((req, res, next) => {
  res.locals.layout = 'layouts/principal';
  next();
});

// Dashboard routes
router.get('/', principalController.getDashboard);
router.get('/dashboard', principalController.getDashboard);

// Academic routes
router.get('/academic-progress', principalController.getAcademicProgress);

// Teacher management routes
router.get('/teacher-details', principalController.getTeacherManagement);
router.get('/teacher-timetables', principalController.getTeacherTimetables);
router.get('/teachers/:id', principalController.getTeacherDetails);

// Student management routes
router.get('/student-analytics', principalController.getStudentAnalytics);
router.get('/students', principalController.getStudents);
router.get('/student-trade-analysis', principalController.getStudentTradeAnalysis);

// Infrastructure management routes
router.get('/infrastructure', principalController.getInfrastructure);

// Reports routes
router.get('/reports', principalController.getReports);

// Profile routes
router.get('/profile', principalController.getProfile);
router.post('/profile/update', principalController.updateProfile);

// API routes for AJAX requests (simple implementations)
router.get('/api/dashboard-stats', async (req, res) => {
  try {
    const db = require('../config/database');
    const [stats] = await db.query(`
      SELECT
        (SELECT COUNT(*) FROM users WHERE role = 'teacher' AND is_active = 1) as total_teachers,
        (SELECT COUNT(*) FROM students WHERE is_active = 1) as total_students
    `);
    res.json({ success: true, stats: stats[0] || {} });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

router.get('/api/academic-progress', async (req, res) => {
  try {
    res.json({ success: true, progress: { completion_rate: 85 } });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

router.get('/api/teacher-performance', async (req, res) => {
  try {
    res.json({ success: true, performance: { average_rating: 4.2 } });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Student details route with new schema support
router.get('/students/:id', async (req, res) => {
    try {
        const db = require('../config/database');
        const studentId = req.params.id;

        console.log(`🔍 Fetching student data for ID: ${studentId}`);

        const [students] = await db.query(
            'SELECT * FROM students WHERE id = ? AND is_active = 1',
            [studentId]
        );

        if (students.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Student not found'
            });
        }

        const student = students[0];

        // Get student's subjects using new schema if available, fallback to old
        let studentSubjects = [];
        try {
            const academicSession = student.academic_session || student.session || '2023-2024';

            // Try new schema first
            const [newSchemaResults] = await db.query(`
                SELECT
                    sub.id as subject_id,
                    sub.code as subject_code,
                    sub.name as subject_name,
                    sub.stream,
                    ts.subject_classification,
                    ts.is_compulsory,
                    ts.include_in_grand_total,
                    COALESCE(ssm.theory_marks, 0) as theory_marks,
                    COALESCE(ssm.practical_marks, 0) as practical_marks,
                    COALESCE(ssm.internal_marks, 0) as internal_marks,
                    COALESCE(ssm.total_marks, 0) as total_marks,
                    COALESCE(ssm.max_marks, 100) as max_marks,
                    ssm.academic_session
                FROM students s
                LEFT JOIN trades t ON s.trade_id = t.id
                LEFT JOIN trade_subjects ts ON t.id = ts.trade_id AND ts.academic_session = ?
                LEFT JOIN subjects sub ON ts.subject_id = sub.id
                LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id AND sub.id = ssm.subject_id AND ssm.academic_session = ?
                WHERE s.id = ? AND sub.id IS NOT NULL
                ORDER BY ts.display_order, sub.code
            `, [academicSession, academicSession, studentId]);

            if (newSchemaResults.length > 0) {
                studentSubjects = newSchemaResults;
                console.log(`✅ Using new schema: Found ${studentSubjects.length} subjects`);
            } else {
                // Fallback to old schema
                const [oldSchemaResults] = await db.query(`
                    SELECT
                        sub.id as subject_id,
                        sub.code as subject_code,
                        sub.name as subject_name,
                        sub.stream,
                        'Legacy' as subject_classification,
                        true as is_compulsory,
                        true as include_in_grand_total,
                        COALESCE(ssm.theory_marks, 0) as theory_marks,
                        COALESCE(ssm.practical_marks, 0) as practical_marks,
                        COALESCE(ssm.internal_marks, 0) as internal_marks,
                        COALESCE(ssm.total_marks, 0) as total_marks,
                        COALESCE(ssm.max_marks, 100) as max_marks,
                        ssm.academic_session
                    FROM student_subject_marks ssm
                    JOIN subjects sub ON ssm.subject_id = sub.id
                    WHERE ssm.student_id = ? AND ssm.academic_session = ?
                    ORDER BY sub.code
                `, [studentId, academicSession]);

                studentSubjects = oldSchemaResults;
                console.log(`⚠️ Using legacy schema: Found ${studentSubjects.length} subjects`);
            }

        } catch (subjectError) {
            console.error('Error fetching student subjects:', subjectError);
            studentSubjects = [];
        }

        // Process subjects and calculate performance
        const processedSubjects = studentSubjects.map(subject => {
            const totalMarks = subject.total_marks || 0;
            const maxMarks = subject.max_marks || 100;
            const percentage = maxMarks > 0 ? Math.round((totalMarks / maxMarks) * 100 * 100) / 100 : 0;

            let grade = 'F';
            if (percentage >= 90) grade = 'A+';
            else if (percentage >= 80) grade = 'A';
            else if (percentage >= 70) grade = 'B+';
            else if (percentage >= 60) grade = 'B';
            else if (percentage >= 50) grade = 'C+';
            else if (percentage >= 40) grade = 'C';
            else if (percentage >= 33) grade = 'D';

            const resultStatus = percentage >= 33 ? 'PASS' : 'FAIL';

            return {
                ...subject,
                percentage,
                grade,
                result_status: resultStatus
            };
        });

        // Calculate overall performance
        const coreSubjects = processedSubjects.filter(s => s.include_in_grand_total);
        const grandTotalMarks = coreSubjects.reduce((sum, s) => sum + (s.total_marks || 0), 0);
        const grandTotalMax = coreSubjects.reduce((sum, s) => sum + (s.max_marks || 0), 0);
        const overallPercentage = grandTotalMax > 0 ? Math.round((grandTotalMarks / grandTotalMax) * 100 * 100) / 100 : 0;

        let overallGrade = 'F';
        if (overallPercentage >= 90) overallGrade = 'A+';
        else if (overallPercentage >= 80) overallGrade = 'A';
        else if (overallPercentage >= 70) overallGrade = 'B+';
        else if (overallPercentage >= 60) overallGrade = 'B';
        else if (overallPercentage >= 50) overallGrade = 'C+';
        else if (overallPercentage >= 40) overallGrade = 'C';
        else if (overallPercentage >= 33) overallGrade = 'D';

        const failedCoreSubjects = coreSubjects.filter(s => s.result_status === 'FAIL');
        const promotionStatus = failedCoreSubjects.length >= 2 ? 'FAIL' : 'PASS';

        const enhancedStudent = {
            ...student,
            subjects: processedSubjects,
            academic_performance: {
                core_subjects: coreSubjects,
                additional_subjects: processedSubjects.filter(s => !s.include_in_grand_total),
                grand_total_marks: grandTotalMarks,
                grand_total_max: grandTotalMax,
                overall_percentage: overallPercentage,
                overall_grade: overallGrade,
                promotion_status: promotionStatus,
                failed_core_subjects: failedCoreSubjects.length
            }
        };

        res.json({
            success: true,
            student: enhancedStudent
        });

    } catch (error) {
        console.error('❌ Error fetching student data:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching student data: ' + error.message
        });
    }
});

// Database test route (for development only)
router.get('/api/test-db', async (req, res) => {
  try {
    const db = require('../config/database');
    const [result] = await db.query('SELECT 1 as test');
    res.json({
      success: true,
      message: 'Database connection successful',
      result: result[0]
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Database connection failed',
      error: error.message
    });
  }
});

module.exports = router;
