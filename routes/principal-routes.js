/**
 * Principal Routes - Clean Version
 * Handles all routes for the Principal dashboard and functionality
 */

const express = require('express');
const router = express.Router();
const principalController = require('../controllers/principal-controller');
const { checkAuthenticated } = require('../middleware/auth');

// Middleware to check if user is principal
const checkPrincipal = (req, res, next) => {
  console.log('🔍 Principal check:', {
    sessionRole: req.session.userRole,
    localsUser: res.locals.user?.role,
    sessionUserId: req.session.userId,
    path: req.path
  });

  // Check session role first (set by demo login)
  if (req.session.userRole === 'principal') {
    console.log('✅ Principal access granted via session role');
    next();
  }
  // Check res.locals.user (set by auth middleware)
  else if (res.locals.user && res.locals.user.role === 'principal') {
    console.log('✅ Principal access granted via locals user');
    next();
  }
  else {
    console.log('❌ Principal access denied');
    res.status(403).render('error', {
      title: 'Access Denied',
      message: 'Access denied. Principal access required.',
      error: { status: 403 },
      layout: 'layouts/main'
    });
  }
};

// Apply authentication and principal check to all routes
router.use(checkAuthenticated);
router.use(checkPrincipal);

// Middleware to ensure principal layout is used
router.use((req, res, next) => {
  res.locals.layout = 'layouts/principal';
  next();
});

// Dashboard routes
router.get('/', principalController.getDashboard);
router.get('/dashboard', principalController.getDashboard);

// Academic routes
router.get('/academic-progress', principalController.getAcademicProgress);

// Teacher management routes
router.get('/teacher-details', principalController.getTeacherManagement);
router.get('/teacher-timetables', principalController.getTeacherTimetables);
router.get('/teachers/:id', principalController.getTeacherDetails);

// Student management routes
router.get('/student-analytics', principalController.getStudentAnalytics);
router.get('/students', principalController.getStudents);
router.get('/student-trade-analysis', principalController.getStudentTradeAnalysis);

// Infrastructure management routes
router.get('/infrastructure', principalController.getInfrastructure);

// Reports routes
router.get('/reports', principalController.getReports);

// Profile routes
router.get('/profile', principalController.getProfile);
router.post('/profile/update', principalController.updateProfile);

// API routes for AJAX requests (simple implementations)
router.get('/api/dashboard-stats', async (req, res) => {
  try {
    const db = require('../config/database');
    const [stats] = await db.query(`
      SELECT
        (SELECT COUNT(*) FROM users WHERE role = 'teacher' AND is_active = 1) as total_teachers,
        (SELECT COUNT(*) FROM students WHERE is_active = 1) as total_students
    `);
    res.json({ success: true, stats: stats[0] || {} });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

router.get('/api/academic-progress', async (req, res) => {
  try {
    res.json({ success: true, progress: { completion_rate: 85 } });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

router.get('/api/teacher-performance', async (req, res) => {
  try {
    res.json({ success: true, performance: { average_rating: 4.2 } });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Student details route with new schema support
router.get('/students/:id', async (req, res) => {
    try {
        const db = require('../config/database');
        const studentId = req.params.id;

        console.log(`🔍 Fetching student data for ID: ${studentId}`);

        const [students] = await db.query(
            'SELECT * FROM students WHERE id = ? AND is_active = 1',
            [studentId]
        );

        if (students.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Student not found'
            });
        }

        const student = students[0];

        // Get student's subjects using new schema if available, fallback to old
        let studentSubjects = [];
        try {
            const academicSession = student.academic_session || student.session || '2023-2024';

            // Try new schema first
            const [newSchemaResults] = await db.query(`
                SELECT
                    sub.id as subject_id,
                    sub.code as subject_code,
                    sub.name as subject_name,
                    sub.stream,
                    ts.subject_classification,
                    ts.is_compulsory,
                    ts.include_in_grand_total,
                    COALESCE(ssm.theory_marks, 0) as theory_marks,
                    COALESCE(ssm.practical_marks, 0) as practical_marks,
                    COALESCE(ssm.internal_marks, 0) as internal_marks,
                    COALESCE(ssm.total_marks, 0) as total_marks,
                    COALESCE(ssm.max_marks, 100) as max_marks,
                    ssm.academic_session
                FROM students s
                LEFT JOIN trades t ON s.trade_id = t.id
                LEFT JOIN trade_subjects ts ON t.id = ts.trade_id AND ts.academic_session = ?
                LEFT JOIN subjects sub ON ts.subject_id = sub.id
                LEFT JOIN student_subject_marks ssm ON s.id = ssm.student_id AND sub.id = ssm.subject_id AND ssm.academic_session = ?
                WHERE s.id = ? AND sub.id IS NOT NULL
                ORDER BY ts.display_order, sub.code
            `, [academicSession, academicSession, studentId]);

            if (newSchemaResults.length > 0) {
                studentSubjects = newSchemaResults;
                console.log(`✅ Using new schema: Found ${studentSubjects.length} subjects`);
            } else {
                // Fallback to old schema
                const [oldSchemaResults] = await db.query(`
                    SELECT
                        sub.id as subject_id,
                        sub.code as subject_code,
                        sub.name as subject_name,
                        sub.stream,
                        'Legacy' as subject_classification,
                        true as is_compulsory,
                        true as include_in_grand_total,
                        COALESCE(ssm.theory_marks, 0) as theory_marks,
                        COALESCE(ssm.practical_marks, 0) as practical_marks,
                        COALESCE(ssm.internal_marks, 0) as internal_marks,
                        COALESCE(ssm.total_marks, 0) as total_marks,
                        COALESCE(ssm.max_marks, 100) as max_marks,
                        ssm.academic_session
                    FROM student_subject_marks ssm
                    JOIN subjects sub ON ssm.subject_id = sub.id
                    WHERE ssm.student_id = ? AND ssm.academic_session = ?
                    ORDER BY sub.code
                `, [studentId, academicSession]);

                studentSubjects = oldSchemaResults;
                console.log(`⚠️ Using legacy schema: Found ${studentSubjects.length} subjects`);
            }

        } catch (subjectError) {
            console.error('Error fetching student subjects:', subjectError);
            studentSubjects = [];
        }

        // Process subjects and calculate performance
        const processedSubjects = studentSubjects.map(subject => {
            const totalMarks = subject.total_marks || 0;
            const maxMarks = subject.max_marks || 100;
            const percentage = maxMarks > 0 ? Math.round((totalMarks / maxMarks) * 100 * 100) / 100 : 0;

            let grade = 'F';
            if (percentage >= 90) grade = 'A+';
            else if (percentage >= 80) grade = 'A';
            else if (percentage >= 70) grade = 'B+';
            else if (percentage >= 60) grade = 'B';
            else if (percentage >= 50) grade = 'C+';
            else if (percentage >= 40) grade = 'C';
            else if (percentage >= 33) grade = 'D';

            const resultStatus = percentage >= 33 ? 'PASS' : 'FAIL';

            return {
                ...subject,
                percentage,
                grade,
                result_status: resultStatus
            };
        });

        // Calculate overall performance
        const coreSubjects = processedSubjects.filter(s => s.include_in_grand_total);
        const grandTotalMarks = coreSubjects.reduce((sum, s) => sum + (s.total_marks || 0), 0);
        const grandTotalMax = coreSubjects.reduce((sum, s) => sum + (s.max_marks || 0), 0);
        const overallPercentage = grandTotalMax > 0 ? Math.round((grandTotalMarks / grandTotalMax) * 100 * 100) / 100 : 0;

        let overallGrade = 'F';
        if (overallPercentage >= 90) overallGrade = 'A+';
        else if (overallPercentage >= 80) overallGrade = 'A';
        else if (overallPercentage >= 70) overallGrade = 'B+';
        else if (overallPercentage >= 60) overallGrade = 'B';
        else if (overallPercentage >= 50) overallGrade = 'C+';
        else if (overallPercentage >= 40) overallGrade = 'C';
        else if (overallPercentage >= 33) overallGrade = 'D';

        const failedCoreSubjects = coreSubjects.filter(s => s.result_status === 'FAIL');
        const promotionStatus = failedCoreSubjects.length >= 2 ? 'FAIL' : 'PASS';

        const enhancedStudent = {
            ...student,
            subjects: processedSubjects,
            academic_performance: {
                core_subjects: coreSubjects,
                additional_subjects: processedSubjects.filter(s => !s.include_in_grand_total),
                grand_total_marks: grandTotalMarks,
                grand_total_max: grandTotalMax,
                overall_percentage: overallPercentage,
                overall_grade: overallGrade,
                promotion_status: promotionStatus,
                failed_core_subjects: failedCoreSubjects.length
            }
        };

        res.json({
            success: true,
            student: enhancedStudent
        });

    } catch (error) {
        console.error('❌ Error fetching student data:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching student data: ' + error.message
        });
    }
});

// Database test route (for development only)
router.get('/api/test-db', async (req, res) => {
  try {
    const db = require('../config/database');
    const [result] = await db.query('SELECT 1 as test');
    res.json({
      success: true,
      message: 'Database connection successful',
      result: result[0]
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Database connection failed',
      error: error.message
    });
  }
});

// Session debug route (for development only)
router.get('/api/debug-session', (req, res) => {
  res.json({
    success: true,
    session: {
      userId: req.session.userId,
      userRole: req.session.userRole,
      username: req.session.username,
      userEmail: req.session.userEmail
    },
    locals: {
      user: res.locals.user
    },
    message: 'Session debug info'
  });
});

// Classroom details API endpoint
router.get('/api/classroom/:id', async (req, res) => {
  try {
    const db = require('../config/database');
    const classroomId = req.params.id;

    console.log(`🏫 Fetching classroom details for ID: ${classroomId}`);

    // Get classroom details with room information
    const [classroomData] = await db.query(`
      SELECT
        r.id as room_id,
        r.room_number,
        r.room_name,
        r.capacity,
        r.building,
        r.floor,
        r.facilities,
        c.id as classroom_id,
        c.session,
        cl.grade,
        cl.section,
        t.name as trade_name,
        u.name as incharge_name,
        u.email as incharge_email,
        COUNT(DISTINCT sc.student_id) as student_count,
        ROUND((COUNT(DISTINCT sc.student_id) / r.capacity) * 100, 1) as utilization_percentage
      FROM rooms r
      LEFT JOIN classrooms c ON r.id = c.room_id AND c.is_active = 1
      LEFT JOIN classes cl ON c.class_id = cl.id
      LEFT JOIN trades t ON c.trade_id = t.id
      LEFT JOIN users u ON c.incharge = u.id
      LEFT JOIN student_classrooms sc ON c.id = sc.classroom_id AND sc.status = 'active'
      WHERE r.id = ?
      GROUP BY r.id, r.room_number, r.room_name, r.capacity, r.building, r.floor, r.facilities, c.id, c.session, cl.grade, cl.section, t.name, u.name, u.email
    `, [classroomId]);

    if (classroomData.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Classroom not found'
      });
    }

    const classroom = classroomData[0];

    // Get equipment in this room
    const [equipment] = await db.query(`
      SELECT
        item_id,
        name,
        model,
        serial_number,
        status,
        purchase_date,
        warranty_expiry,
        created_at
      FROM inventory_items
      WHERE room_id = ?
      ORDER BY name
    `, [classroomId]);

    // Get students in this classroom
    const [students] = await db.query(`
      SELECT
        s.id,
        s.student_id,
        s.name,
        s.class,
        s.section,
        s.roll_no,
        s.gender
      FROM students s
      JOIN student_classrooms sc ON s.id = sc.student_id
      WHERE sc.classroom_id = ? AND sc.status = 'active'
      ORDER BY s.roll_no, s.name
      LIMIT 20
    `, [classroom.classroom_id || 0]);

    const response = {
      success: true,
      data: {
        classroom: {
          ...classroom,
          equipment: equipment || [],
          students: students || [],
          equipment_count: equipment ? equipment.length : 0,
          student_list_truncated: students && students.length >= 20
        }
      }
    };

    console.log(`✅ Classroom data loaded: ${classroom.room_number}`);
    res.json(response);

  } catch (error) {
    console.error('❌ Error fetching classroom data:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching classroom data',
      error: error.message
    });
  }
});

module.exports = router;
