-- Schema Setup for Student-Trade-Subject System
-- Run these queries in your MySQL database to set up the new schema

-- 1. Create TRADES table
CREATE TABLE IF NOT EXISTS trades (
  id INT AUTO_INCREMENT PRIMARY KEY,
  trade_code VARCHAR(20) UNIQUE NOT NULL,
  trade_name VARCHAR(100) NOT NULL,
  trade_category VARCHAR(50) NOT NULL,
  applicable_classes VARCHAR(50) NOT NULL DEFAULT '11,12',
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_trade_code (trade_code),
  INDEX idx_trade_category (trade_category)
) ENGINE=InnoDB;

-- 2. Insert basic trade data
INSERT IGNORE INTO trades (trade_code, trade_name, trade_category, applicable_classes, description) VALUES
('MED', 'Medical', 'Medical', '11,12', 'Medical stream with Biology as core subject'),
('NM', 'Non Medical', 'Non Medical', '11,12', 'Non Medical stream with Physics, Chemistry, Mathematics'),
('COM', 'Commerce', 'Commerce', '11,12', 'Commerce stream with business subjects'),
('HUM', 'Humanities', 'Humanities', '11,12', 'Humanities stream with arts subjects'),
('GEN', 'General', 'General', '10', 'General education for class 10');

-- 3. Create TRADE_SUBJECTS table
CREATE TABLE IF NOT EXISTS trade_subjects (
  id INT AUTO_INCREMENT PRIMARY KEY,
  trade_id INT NOT NULL,
  subject_id INT NOT NULL,
  subject_classification VARCHAR(100) NOT NULL,
  is_compulsory BOOLEAN DEFAULT TRUE,
  include_in_grand_total BOOLEAN DEFAULT TRUE,
  display_order INT DEFAULT 0,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY unique_trade_subject_session (trade_id, subject_id, academic_session),
  INDEX idx_trade_id (trade_id),
  INDEX idx_subject_id (subject_id),
  INDEX idx_academic_session (academic_session)
) ENGINE=InnoDB;

-- 4. Create STUDENT_ELECTIVE_OPTIONAL table
CREATE TABLE IF NOT EXISTS student_elective_optional (
  id INT AUTO_INCREMENT PRIMARY KEY,
  student_id INT NOT NULL,
  subject_id INT NOT NULL,
  selection_type VARCHAR(50) NOT NULL,
  academic_session VARCHAR(20) NOT NULL DEFAULT '2023-2024',
  selected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY unique_student_subject_session (student_id, subject_id, academic_session),
  INDEX idx_student_id (student_id),
  INDEX idx_subject_id (subject_id),
  INDEX idx_selection_type (selection_type)
) ENGINE=InnoDB;

-- 5. Add trade_id column to students table
ALTER TABLE students ADD COLUMN IF NOT EXISTS trade_id INT NULL AFTER section;
ALTER TABLE students ADD INDEX IF NOT EXISTS idx_trade_id (trade_id);

-- 6. Migrate existing student trade data
UPDATE students s 
JOIN trades t ON t.trade_code = 'MED'
SET s.trade_id = t.id 
WHERE s.trade = 'Biology' AND s.is_active = 1;

UPDATE students s 
JOIN trades t ON t.trade_code = 'NM'
SET s.trade_id = t.id 
WHERE s.trade IN ('Physics', 'Chemistry', 'Mathematics') AND s.is_active = 1;

UPDATE students s 
JOIN trades t ON t.trade_code = 'COM'
SET s.trade_id = t.id 
WHERE s.trade IN ('Commerce', 'Economics', 'Accountancy') AND s.is_active = 1;

UPDATE students s 
JOIN trades t ON t.trade_code = 'HUM'
SET s.trade_id = t.id 
WHERE s.trade IN ('History', 'Geography', 'Political Science', 'Psychology', 'Sociology', 'Philosophy') AND s.is_active = 1;

UPDATE students s 
JOIN trades t ON t.trade_code = 'GEN'
SET s.trade_id = t.id 
WHERE s.class = '10' AND s.is_active = 1;

-- 7. Populate trade_subjects with basic mappings (adjust subject codes as needed)
-- Medical Trade Subjects
INSERT IGNORE INTO trade_subjects (trade_id, subject_id, subject_classification, is_compulsory, include_in_grand_total, display_order, academic_session) 
SELECT 
  t.id,
  s.id,
  CASE 
    WHEN s.code IN ('1', '2') THEN 'Compulsory Language'
    WHEN s.code = '54' THEN 'Core Medical'
    WHEN s.code IN ('28', '52') THEN 'Core Science'
    ELSE 'Additional Compulsory'
  END,
  CASE WHEN s.code IN ('1', '2', '54', '28', '52') THEN TRUE ELSE FALSE END,
  CASE WHEN s.code IN ('1', '2', '54', '28', '52') THEN TRUE ELSE FALSE END,
  CASE 
    WHEN s.code = '1' THEN 1
    WHEN s.code = '2' THEN 2
    WHEN s.code = '28' THEN 3
    WHEN s.code = '52' THEN 4
    WHEN s.code = '54' THEN 5
    ELSE 10
  END,
  '2023-2024'
FROM trades t
CROSS JOIN subjects s
WHERE t.trade_code = 'MED' 
  AND s.code IN ('1', '2', '28', '52', '54');

-- Non Medical Trade Subjects  
INSERT IGNORE INTO trade_subjects (trade_id, subject_id, subject_classification, is_compulsory, include_in_grand_total, display_order, academic_session)
SELECT 
  t.id,
  s.id,
  CASE 
    WHEN s.code IN ('1', '2') THEN 'Compulsory Language'
    WHEN s.code IN ('28', '52', '53') THEN 'Core Science'
    ELSE 'Additional Compulsory'
  END,
  CASE WHEN s.code IN ('1', '2', '28', '52', '53') THEN TRUE ELSE FALSE END,
  CASE WHEN s.code IN ('1', '2', '28', '52', '53') THEN TRUE ELSE FALSE END,
  CASE 
    WHEN s.code = '1' THEN 1
    WHEN s.code = '2' THEN 2
    WHEN s.code = '28' THEN 3
    WHEN s.code = '52' THEN 4
    WHEN s.code = '53' THEN 5
    ELSE 10
  END,
  '2023-2024'
FROM trades t
CROSS JOIN subjects s
WHERE t.trade_code = 'NM' 
  AND s.code IN ('1', '2', '28', '52', '53');

-- Commerce Trade Subjects
INSERT IGNORE INTO trade_subjects (trade_id, subject_id, subject_classification, is_compulsory, include_in_grand_total, display_order, academic_session)
SELECT 
  t.id,
  s.id,
  CASE 
    WHEN s.code IN ('1', '2') THEN 'Compulsory Language'
    WHEN s.code IN ('141', '142', '26') THEN 'Core Commerce'
    ELSE 'Additional Compulsory'
  END,
  CASE WHEN s.code IN ('1', '2', '141', '142', '26') THEN TRUE ELSE FALSE END,
  CASE WHEN s.code IN ('1', '2', '141', '142', '26') THEN TRUE ELSE FALSE END,
  CASE 
    WHEN s.code = '1' THEN 1
    WHEN s.code = '2' THEN 2
    WHEN s.code = '141' THEN 3
    WHEN s.code = '142' THEN 4
    WHEN s.code = '26' THEN 5
    ELSE 10
  END,
  '2023-2024'
FROM trades t
CROSS JOIN subjects s
WHERE t.trade_code = 'COM' 
  AND s.code IN ('1', '2', '141', '142', '26');

-- 8. Verification queries (run these to check if setup worked)
-- SELECT COUNT(*) as trades_count FROM trades;
-- SELECT COUNT(*) as trade_subjects_count FROM trade_subjects;
-- SELECT COUNT(*) as students_with_trade_id FROM students WHERE trade_id IS NOT NULL AND is_active = 1;

-- 9. Sample query to test the new schema
-- SELECT 
--   s.name as student_name,
--   s.class,
--   t.trade_name,
--   t.trade_category,
--   sub.code as subject_code,
--   sub.name as subject_name,
--   ts.subject_classification,
--   ts.is_compulsory,
--   ts.include_in_grand_total
-- FROM students s
-- LEFT JOIN trades t ON s.trade_id = t.id
-- LEFT JOIN trade_subjects ts ON t.id = ts.trade_id
-- LEFT JOIN subjects sub ON ts.subject_id = sub.id
-- WHERE s.is_active = 1
-- ORDER BY s.class, s.name, ts.display_order
-- LIMIT 20;
