/**
 * Test script to verify all principal routes are working
 * Run with: node test-principal-routes.js
 */

const http = require('http');

const routes = [
  '/principal/dashboard',
  '/principal/academic-progress',
  '/principal/teacher-details',
  '/principal/teacher-timetables',
  '/principal/student-analytics',
  '/principal/students',
  '/principal/student-trade-analysis',
  '/principal/infrastructure',
  '/principal/reports',
  '/principal/profile'
];

const apiRoutes = [
  '/principal/api/dashboard-stats',
  '/principal/api/academic-progress',
  '/principal/api/teacher-performance',
  '/principal/api/test-db'
];

function testRoute(path) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3018,
      path: path,
      method: 'GET',
      headers: {
        'User-Agent': 'Route-Tester/1.0'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          path: path,
          status: res.statusCode,
          success: res.statusCode < 400,
          contentType: res.headers['content-type'],
          hasContent: data.length > 0
        });
      });
    });

    req.on('error', (err) => {
      resolve({
        path: path,
        status: 'ERROR',
        success: false,
        error: err.message
      });
    });

    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        path: path,
        status: 'TIMEOUT',
        success: false,
        error: 'Request timeout'
      });
    });

    req.end();
  });
}

async function testAllRoutes() {
  console.log('🧪 Testing Principal Routes...\n');
  
  console.log('📄 Testing Page Routes:');
  console.log('========================');
  
  for (const route of routes) {
    const result = await testRoute(route);
    const status = result.success ? '✅' : '❌';
    const statusCode = result.status;
    const contentInfo = result.hasContent ? '(has content)' : '(empty)';
    
    console.log(`${status} ${route} - ${statusCode} ${contentInfo}`);
    
    if (!result.success && result.error) {
      console.log(`   Error: ${result.error}`);
    }
  }
  
  console.log('\n🔌 Testing API Routes:');
  console.log('======================');
  
  for (const route of apiRoutes) {
    const result = await testRoute(route);
    const status = result.success ? '✅' : '❌';
    const statusCode = result.status;
    const isJson = result.contentType && result.contentType.includes('application/json');
    const contentInfo = isJson ? '(JSON)' : '(HTML/Other)';
    
    console.log(`${status} ${route} - ${statusCode} ${contentInfo}`);
    
    if (!result.success && result.error) {
      console.log(`   Error: ${result.error}`);
    }
  }
  
  console.log('\n📊 Summary:');
  console.log('===========');
  
  const allRoutes = [...routes, ...apiRoutes];
  const results = await Promise.all(allRoutes.map(testRoute));
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`Total Routes: ${allRoutes.length}`);
  console.log(`✅ Successful: ${successful}`);
  console.log(`❌ Failed: ${failed}`);
  
  if (failed === 0) {
    console.log('\n🎉 All principal routes are working correctly!');
  } else {
    console.log('\n⚠️  Some routes need attention.');
  }
}

// Run the tests
testAllRoutes().catch(console.error);
