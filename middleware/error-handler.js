/**
 * Comprehensive Error Handler Middleware
 * Ensures all error responses include required variables for templates
 */

const errorHandler = (err, req, res, next) => {
    // Set default error properties
    const status = err.status || err.statusCode || 500;
    const message = err.message || 'Internal Server Error';
    
    // Log the error for debugging
    console.error('Error Handler:', {
        path: req.path,
        method: req.method,
        status: status,
        message: message,
        stack: err.stack
    });

    // Check if it's an API request
    const isApiRequest = req.path.startsWith('/api/') || 
                        req.path.includes('/procurement/') ||
                        req.headers.accept?.includes('application/json') ||
                        req.xhr;

    if (isApiRequest) {
        // Return JSON for API requests
        return res.status(status).json({
            success: false,
            message: message,
            error: {
                status: status,
                details: process.env.NODE_ENV === 'development' ? err.stack : undefined
            }
        });
    }

    // For HTML requests, render the error page with all required variables
    const errorData = {
        title: `Error ${status}`,
        message: message,
        error: {
            status: status,
            stack: process.env.NODE_ENV === 'development' ? err.stack : ''
        }
    };

    // Determine the appropriate layout based on the request path
    let layout = 'layouts/main';
    if (req.path.startsWith('/admin/')) {
        layout = 'layouts/admin';
    } else if (req.path.startsWith('/principal/')) {
        layout = 'layouts/principal';
    } else if (req.path.startsWith('/teacher/')) {
        layout = 'layouts/teacher';
    } else if (req.path.startsWith('/student/')) {
        layout = 'layouts/student';
    } else if (req.path.startsWith('/it-admin/')) {
        layout = 'layouts/it-admin';
    }

    // Add layout to error data
    errorData.layout = layout;

    // Render the error page
    res.status(status).render('error', errorData);
};

// 404 Handler
const notFoundHandler = (req, res, next) => {
    // Check if it's an API request
    const isApiRequest = req.path.startsWith('/api/') || 
                        req.path.includes('/procurement/') ||
                        req.headers.accept?.includes('application/json') ||
                        req.xhr;

    if (isApiRequest) {
        return res.status(404).json({
            success: false,
            message: 'Endpoint not found',
            error: { status: 404 }
        });
    }

    // For HTML requests, render 404 page
    const errorData = {
        title: 'Page Not Found',
        message: 'The page you are looking for could not be found.',
        error: {
            status: 404,
            stack: ''
        },
        layout: 'layouts/main'
    };

    res.status(404).render('error', errorData);
};

module.exports = {
    errorHandler,
    notFoundHandler
};
