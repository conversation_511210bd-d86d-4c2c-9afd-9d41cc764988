/**
 * Score Distribution Page JavaScript
 * Handles functionality specific to the score distribution analysis page
 */

// Debug: Check if script is loading
console.log('Score distribution script loading...');

// JavaScript function for client-side color generation
function getColorForRange(min) {
    if (min >= 90) return 'linear-gradient(135deg, #10b981, #059669)';
    if (min >= 80) return 'linear-gradient(135deg, #3b82f6, #1d4ed8)';
    if (min >= 70) return 'linear-gradient(135deg, #8b5cf6, #7c3aed)';
    if (min >= 60) return 'linear-gradient(135deg, #06b6d4, #0891b2)';
    if (min >= 50) return 'linear-gradient(135deg, #f59e0b, #d97706)';
    if (min >= 40) return 'linear-gradient(135deg, #f97316, #ea580c)';
    return 'linear-gradient(135deg, #ef4444, #dc2626)';
}

function applyFilters() {
    const trade = document.getElementById('tradeFilter').value;
    const classLevel = document.getElementById('classFilter').value;
    const section = document.getElementById('sectionFilter').value;

    const url = new URL(window.location);
    url.searchParams.set('trade', trade);
    url.searchParams.set('class', classLevel);
    url.searchParams.set('section', section);

    window.location.href = url.toString();
}
// Make function globally accessible immediately
window.applyFilters = applyFilters;

function toggleRangeDetails(index) {
    const details = document.getElementById(`range-details-${index}`);
    const chevron = document.getElementById(`chevron-${index}`);

    if (details.classList.contains('hidden')) {
        details.classList.remove('hidden');
        chevron.classList.add('fa-chevron-up');
        chevron.classList.remove('fa-chevron-down');
    } else {
        details.classList.add('hidden');
        chevron.classList.add('fa-chevron-down');
        chevron.classList.remove('fa-chevron-up');
    }
}
window.toggleRangeDetails = toggleRangeDetails;

function showRangeDetails(index) {
    toggleRangeDetails(index);
}
window.showRangeDetails = showRangeDetails;

function customizeRanges() {
    console.log('customizeRanges function called');
    document.getElementById('customRangeModal').classList.remove('hidden');

    // Add a small delay to ensure modal is visible before loading ranges
    setTimeout(() => {
        loadCurrentRanges();
    }, 100);
}
// Make function globally accessible immediately
window.customizeRanges = customizeRanges;
console.log('customizeRanges assigned to window:', typeof window.customizeRanges);

function closeCustomRangeModal() {
    document.getElementById('customRangeModal').classList.add('hidden');
}
window.closeCustomRangeModal = closeCustomRangeModal;

function addCustomRange() {
    addRangeInput();
}
window.addCustomRange = addCustomRange;

function removeRange(button) {
    button.closest('.flex').remove();
}
window.removeRange = removeRange;

function resetToDefault() {
    const defaultRanges = [
        { min: 90, max: 100, label: '90-100 (Excellent)' },
        { min: 80, max: 89, label: '80-89 (Very Good)' },
        { min: 70, max: 79, label: '70-79 (Good)' },
        { min: 60, max: 69, label: '60-69 (Above Average)' },
        { min: 50, max: 59, label: '50-59 (Average)' },
        { min: 40, max: 49, label: '40-49 (Below Average)' },
        { min: 0, max: 39, label: 'Below 40 (Poor)' }
    ];

    const container = document.getElementById('customRangeInputs');
    container.innerHTML = '';
    defaultRanges.forEach((range, index) => {
        addRangeInput(range.min, range.max, range.label, index);
    });
}
window.resetToDefault = resetToDefault;

function resetToDefaultRanges() {
    // Show loading state
    const resetBtn = document.querySelector('button[onclick="resetToDefaultRanges()"]');
    if (resetBtn) {
        const originalText = resetBtn.innerHTML;
        resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Resetting...';
        resetBtn.disabled = true;
    }

    // Send reset request to server
    fetch('/exam-results/reset-to-default-ranges', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove custom ranges from URL and reload
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.delete('customRanges');
            window.location.href = currentUrl.toString();
        } else {
            alert('Error resetting to default ranges: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error resetting to default ranges. Please try again.');
    })
    .finally(() => {
        // Restore button state
        if (resetBtn) {
            resetBtn.innerHTML = originalText;
            resetBtn.disabled = false;
        }
    });
}
window.resetToDefaultRanges = resetToDefaultRanges;

function applyCustomRanges() {
    try {
        console.log('=== Starting applyCustomRanges ===');

        // Collect all range inputs from the modal
        const container = document.getElementById('customRangeInputs');
        const rangeInputs = container.querySelectorAll('.flex.items-center.space-x-4');

        console.log(`Found ${rangeInputs.length} range inputs`);

        const customRanges = [];
        let hasErrors = false;

        rangeInputs.forEach((rangeDiv, index) => {
            const labelInput = rangeDiv.querySelector('.range-label');
            const minInput = rangeDiv.querySelector('.range-min');
            const maxInput = rangeDiv.querySelector('.range-max');

            // Check if all inputs were found
            if (!labelInput || !minInput || !maxInput) {
                console.error(`Missing inputs for range ${index + 1}:`, {
                    labelInput: !!labelInput,
                    minInput: !!minInput,
                    maxInput: !!maxInput
                });
                alert(`Error: Missing input fields for range ${index + 1}`);
                hasErrors = true;
                return;
            }

            const label = labelInput.value.trim();
            const minValue = minInput.value.trim();
            const maxValue = maxInput.value.trim();

            // Parse values as numbers
            const min = parseFloat(minValue);
            const max = parseFloat(maxValue);

            // Debug logging
            console.log(`Range ${index + 1}:`, { label, minValue, maxValue, min, max });

            // Validation
            if (!label) {
                alert(`Please enter a label for range ${index + 1}`);
                hasErrors = true;
                return;
            }

            if (minValue === '' || maxValue === '' || isNaN(min) || isNaN(max)) {
                alert(`Please enter valid numbers for range ${index + 1}. Min: "${minValue}", Max: "${maxValue}"`);
                hasErrors = true;
                return;
            }

            if (min > max) {
                alert(`Min value (${min}) must be less than or equal to max value (${max}) for range ${index + 1}`);
                hasErrors = true;
                return;
            }

            if (min < 0 || max > 100 || min > 100 || max < 0) {
                alert(`Range values must be between 0 and 100 for range ${index + 1}. Got Min: ${min}, Max: ${max}`);
                hasErrors = true;
                return;
            }

            customRanges.push({ min, max, label });
        });

        if (hasErrors || customRanges.length === 0) {
            return;
        }

        // Sort ranges by min value
        customRanges.sort((a, b) => a.min - b.min);

        // Check for overlapping ranges (allow adjacent ranges where max of one equals min of next)
        for (let i = 0; i < customRanges.length - 1; i++) {
            if (customRanges[i].max > customRanges[i + 1].min) {
                alert(`Ranges overlap between "${customRanges[i].label}" (${customRanges[i].min}-${customRanges[i].max}) and "${customRanges[i + 1].label}" (${customRanges[i + 1].min}-${customRanges[i + 1].max})`);
                return;
            }
        }

        // Show loading state
        const applyBtn = document.querySelector('button[onclick="applyCustomRanges()"]');
        const originalText = applyBtn.innerHTML;
        applyBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Applying...';
        applyBtn.disabled = true;

        // Send custom ranges to server
        const currentUrl = new URL(window.location);
        const params = new URLSearchParams(currentUrl.search);

        fetch('/exam-results/apply-custom-ranges', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                ranges: customRanges,
                trade: params.get('trade') || 'all',
                class: params.get('class') || 'all',
                section: params.get('section') || 'all'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal and reload page to show new ranges from database
                closeCustomRangeModal();

                // Remove any custom ranges from URL since they're now in database
                currentUrl.searchParams.delete('customRanges');
                window.location.href = currentUrl.toString();
            } else {
                alert('Error applying custom ranges: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error applying custom ranges. Please try again.');
        })
        .finally(() => {
            // Restore button state
            applyBtn.innerHTML = originalText;
            applyBtn.disabled = false;
        });

    } catch (error) {
        console.error('Error in applyCustomRanges:', error);
        alert('Error applying custom ranges. Please try again.');
    }
}
window.applyCustomRanges = applyCustomRanges;

function addRangeInput(min = 0, max = 100, label = '', index = null) {
    console.log(`addRangeInput called with: min=${min}, max=${max}, label="${label}", index=${index}`);

    const container = document.getElementById('customRangeInputs');
    const rangeIndex = index !== null ? index : container.children.length;

    // Ensure min and max are numbers
    const minValue = Number(min) || 0;
    const maxValue = Number(max) || 100;

    console.log(`Processed values: minValue=${minValue}, maxValue=${maxValue}`);

    const rangeDiv = document.createElement('div');
    rangeDiv.className = 'flex items-center space-x-4 p-4 border border-gray-200 rounded-lg';
    rangeDiv.innerHTML = `
        <div class="flex-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">Label</label>
            <input type="text" value="${label}" class="range-label w-full border border-gray-300 rounded px-3 py-2" placeholder="e.g., Excellent">
        </div>
        <div class="w-24">
            <label class="block text-sm font-medium text-gray-700 mb-1">Min %</label>
            <input type="number" value="${minValue}" min="0" max="100" step="1" class="range-min w-full border border-gray-300 rounded px-3 py-2">
        </div>
        <div class="w-24">
            <label class="block text-sm font-medium text-gray-700 mb-1">Max %</label>
            <input type="number" value="${maxValue}" min="0" max="100" step="1" class="range-max w-full border border-gray-300 rounded px-3 py-2">
        </div>
        <button onclick="removeRange(this)" class="text-red-600 hover:text-red-800 mt-6">
            <i class="fas fa-trash"></i>
        </button>
    `;

    container.appendChild(rangeDiv);
}

// Store distribution data globally for use in functions
window.scoreDistributionData = null;

function loadCurrentRanges() {
    const container = document.getElementById('customRangeInputs');
    try {
        console.log('Loading current ranges...');
        console.log('window.scoreDistributionData:', window.scoreDistributionData);
        console.log('window.isCustomRanges:', window.isCustomRanges);
        console.log('window.customRangesData:', window.customRangesData);

        // Use custom ranges data if available, otherwise use distribution data
        let currentRanges;
        if (window.isCustomRanges && window.customRangesData) {
            currentRanges = window.customRangesData;
            console.log('Using custom ranges data');
        } else {
            currentRanges = window.scoreDistributionData || [];
            console.log('Using distribution data');
        }

        container.innerHTML = '';
        if (currentRanges && currentRanges.length > 0) {
            console.log('Loading ranges:', currentRanges);
            currentRanges.forEach((range, index) => {
                console.log(`Adding range ${index}:`, range);
                addRangeInput(range.min || 0, range.max || 100, range.label || 'Unknown Range', index);
            });
        } else {
            console.log('No current ranges found, loading defaults');
            // Load default ranges if no current ranges
            resetToDefault();
        }
    } catch (error) {
        console.error('Error loading current ranges:', error);
        resetToDefault();
    }
}

// Initialize chart function using Apache ECharts
function initializeDistributionChart(distributionData) {
    try {
        const chartContainer = document.getElementById('distributionChart');

        if (!chartContainer) {
            console.error('Chart container not found');
            return;
        }

        if (!distributionData || distributionData.length === 0) {
            console.warn('No distribution data available for chart');
            chartContainer.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500"><p>No data available for chart</p></div>';
            return;
        }

        // Initialize ECharts instance
        const chart = echarts.init(chartContainer);

        // Filter out ranges with zero students for cleaner visualization
        const filteredData = distributionData.filter(d => d.count > 0);

        // Prepare data for ECharts
        const chartData = filteredData.map(d => ({
            name: d.label,
            value: d.count,
            percentage: d.percentage,
            min: d.min,
            max: d.max,
            students: d.students || []
        }));

        // Color mapping function
        function getChartColor(min) {
            if (min >= 90) return '#10b981'; // Green
            if (min >= 80) return '#3b82f6'; // Blue
            if (min >= 70) return '#8b5cf6'; // Purple
            if (min >= 60) return '#06b6d4'; // Cyan
            if (min >= 50) return '#f59e0b'; // Amber
            if (min >= 40) return '#f97316'; // Orange
            return '#ef4444'; // Red
        }

        // ECharts configuration
        const option = {
            title: {
                text: 'Score Distribution',
                subtext: `Total Students: ${distributionData.reduce((sum, d) => sum + d.count, 0)}`,
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: '#374151'
                },
                subtextStyle: {
                    fontSize: 12,
                    color: '#6b7280'
                }
            },
            tooltip: {
                trigger: 'item',
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                borderColor: '#3b82f6',
                borderWidth: 1,
                textStyle: {
                    color: '#ffffff'
                },
                formatter: function(params) {
                    const data = params.data;
                    return `
                        <div style="padding: 8px;">
                            <div style="font-weight: bold; margin-bottom: 4px;">${data.name}</div>
                            <div>Students: <span style="color: #60a5fa;">${data.value}</span></div>
                            <div>Percentage: <span style="color: #34d399;">${data.percentage.toFixed(1)}%</span></div>
                            <div style="margin-top: 4px; font-size: 11px; color: #d1d5db;">
                                Range: ${data.min}% - ${data.max}%
                            </div>
                        </div>
                    `;
                }
            },
            legend: {
                type: 'scroll',
                orient: 'horizontal',
                bottom: 10,
                left: 'center',
                textStyle: {
                    fontSize: 11,
                    color: '#374151'
                },
                pageIconColor: '#3b82f6',
                pageIconInactiveColor: '#d1d5db',
                pageTextStyle: {
                    color: '#6b7280'
                }
            },
            series: [{
                name: 'Score Distribution',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['50%', '45%'],
                avoidLabelOverlap: true,
                itemStyle: {
                    borderRadius: 8,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: true,
                    position: 'outside',
                    formatter: function(params) {
                        return `${params.data.value}\n(${params.data.percentage.toFixed(1)}%)`;
                    },
                    fontSize: 11,
                    color: '#374151'
                },
                labelLine: {
                    show: true,
                    length: 15,
                    length2: 10
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    },
                    label: {
                        fontSize: 12,
                        fontWeight: 'bold'
                    }
                },
                data: chartData.map(d => ({
                    ...d,
                    itemStyle: {
                        color: getChartColor(d.min)
                    }
                }))
            }]
        };

        // Set chart option
        chart.setOption(option);

        // Handle window resize
        window.addEventListener('resize', function() {
            chart.resize();
        });

        // Add click event for interactivity
        chart.on('click', function(params) {
            const data = params.data;
            if (data.students && data.students.length > 0) {
                console.log(`Clicked on ${data.name}:`, data.students);
                // You can add custom behavior here, like showing a modal with student details
                showRangeStudents(data);
            }
        });

        console.log('ECharts distribution chart initialized successfully');

    } catch (error) {
        console.error('Error initializing distribution chart:', error);
    }
}

// Helper function to show students in a range when chart segment is clicked
function showRangeStudents(rangeData) {
    try {
        const students = rangeData.students || [];
        if (students.length === 0) {
            alert(`No students found in range: ${rangeData.name}`);
            return;
        }

        // Create a simple modal to show students
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        modal.innerHTML = `
            <div class="bg-white rounded-lg max-w-2xl w-full max-h-96 overflow-hidden">
                <div class="p-4 border-b bg-gradient-to-r from-blue-600 to-green-600 text-white">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-bold">${rangeData.name}</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-white hover:text-gray-200">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <p class="text-sm opacity-90 mt-1">${students.length} students in this range</p>
                </div>
                <div class="p-4 overflow-y-auto max-h-80">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        ${students.map(student => `
                            <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-800">${student.name || 'Unknown'}</p>
                                        <p class="text-sm text-gray-600">${student.roll_number || 'N/A'}</p>
                                        <p class="text-xs text-gray-500">${student.class || 'N/A'}-${student.section || 'N/A'} | ${student.trade || 'N/A'}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-bold text-lg ${(student.percentage || 0) >= 75 ? 'text-green-600' : (student.percentage || 0) >= 50 ? 'text-yellow-600' : 'text-red-600'}">
                                            ${(student.percentage || 0).toFixed ? (student.percentage || 0).toFixed(1) : (student.percentage || 0)}%
                                        </p>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.remove();
            }
        });

    } catch (error) {
        console.error('Error showing range students:', error);
        alert('Error displaying student details');
    }
}

// Main initialization function for score distribution page
function initializeScoreDistribution() {
    // Debug: Verify all functions are loaded
    console.log('Functions check:', {
        customizeRanges: typeof window.customizeRanges,
        applyFilters: typeof window.applyFilters,
        toggleRangeDetails: typeof window.toggleRangeDetails,
        closeCustomRangeModal: typeof window.closeCustomRangeModal
    });
}

// Also define functions outside DOMContentLoaded as backup
if (typeof window.customizeRanges === 'undefined') {
    window.customizeRanges = function() {
        console.log('Backup customizeRanges called');
        document.getElementById('customRangeModal').classList.remove('hidden');
        if (typeof loadCurrentRanges === 'function') {
            loadCurrentRanges();
        }
    };
}

// Test function to verify validation logic
function testValidation() {
    console.log('Testing validation logic:');

    // Test cases
    const testCases = [
        { min: '90', max: '100', expected: 'valid' },
        { min: '80', max: '89', expected: 'valid' },
        { min: '50', max: '50', expected: 'valid' },  // Equal values should be valid
        { min: '60', max: '50', expected: 'invalid' }, // Min > Max should be invalid
        { min: '', max: '100', expected: 'invalid' },  // Empty values should be invalid
        { min: 'abc', max: '100', expected: 'invalid' }, // Non-numeric should be invalid
    ];

    testCases.forEach((test, index) => {
        const min = parseFloat(test.min);
        const max = parseFloat(test.max);
        const isValid = !isNaN(min) && !isNaN(max) && min <= max && min >= 0 && max <= 100;
        const result = isValid ? 'valid' : 'invalid';
        console.log(`Test ${index + 1}: min="${test.min}", max="${test.max}" -> ${result} (expected: ${test.expected})`);
    });
}

// Call test function
testValidation();

// Debug function to test current modal state
window.debugRanges = function() {
    console.log('=== Debug Ranges ===');
    const container = document.getElementById('customRangeInputs');
    if (!container) {
        console.log('Container not found');
        return;
    }

    const rangeInputs = container.querySelectorAll('.flex.items-center.space-x-4');
    console.log(`Found ${rangeInputs.length} range inputs`);

    rangeInputs.forEach((rangeDiv, index) => {
        const labelInput = rangeDiv.querySelector('.range-label');
        const minInput = rangeDiv.querySelector('.range-min');
        const maxInput = rangeDiv.querySelector('.range-max');

        console.log(`Range ${index + 1}:`, {
            label: labelInput ? labelInput.value : 'NO LABEL INPUT',
            min: minInput ? minInput.value : 'NO MIN INPUT',
            max: maxInput ? maxInput.value : 'NO MAX INPUT'
        });
    });
};

console.log('Score distribution script loaded completely');

// Export functions for external access
window.ScoreDistribution = {
    customizeRanges,
    applyFilters,
    toggleRangeDetails,
    showRangeDetails,
    closeCustomRangeModal,
    addCustomRange,
    removeRange,
    resetToDefault,
    applyCustomRanges,
    loadCurrentRanges,
    initializeDistributionChart,
    initializeScoreDistribution,
    showRangeStudents
};
