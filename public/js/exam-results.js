/**
 * Exam Results System JavaScript
 * Handles all functionality for the exam results management system
 */

// Mobile menu toggle
function toggleMobileMenu() {
    const sidebar = document.querySelector('.exam-sidebar');
    const overlay = document.querySelector('.mobile-menu-overlay');

    sidebar.classList.toggle('open');
    overlay.classList.toggle('open');
}

// Make function globally accessible
window.toggleMobileMenu = toggleMobileMenu;

// Session selector handling
function initializeSessionSelector() {
    const sessionSelector = document.getElementById('sessionSelector');

    if (sessionSelector) {
        sessionSelector.addEventListener('change', function() {
            const selectedSession = this.value;

            // Show loading indicator
            const originalText = this.options[this.selectedIndex].text;
            this.options[this.selectedIndex].text = 'Loading...';
            this.disabled = true;

            // Send AJAX request to update session
            fetch('/exam-results/change-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ session: selectedSession })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the current page to reflect new session data
                    window.location.reload();
                } else {
                    alert('Error changing session: ' + (data.error || 'Unknown error'));
                    this.options[this.selectedIndex].text = originalText;
                    this.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error changing session');
                this.options[this.selectedIndex].text = originalText;
                this.disabled = false;
            });
        });
    }
}

// Grade badge helper
function getGradeBadgeClass(grade) {
    const gradeClasses = {
        'A+': 'grade-a-plus',
        'A': 'grade-a',
        'B+': 'grade-b-plus',
        'B': 'grade-b',
        'C+': 'grade-c-plus',
        'C': 'grade-c',
        'D': 'grade-d',
        'F': 'grade-f'
    };
    return gradeClasses[grade] || 'grade-f';
}

// Auto-refresh functionality
function startAutoRefresh(interval = 300000) { // 5 minutes default
    setInterval(() => {
        // Only refresh if user is active
        if (document.visibilityState === 'visible') {
            console.log('Auto-refreshing data...');
            // Add specific refresh logic here if needed
        }
    }, interval);
}

// Initialize grade badges
function initializeGradeBadges() {
    document.querySelectorAll('.grade-badge').forEach(badge => {
        const grade = badge.textContent.trim();
        badge.classList.add(getGradeBadgeClass(grade));
    });
}

// Main initialization function
function initializeExamResultsSystem() {
    console.log('Exam Results System initialized');
    
    // Initialize all components
    initializeSessionSelector();
    initializeGradeBadges();
    
    // Optional: Start auto-refresh if needed
    // startAutoRefresh();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', initializeExamResultsSystem);

// Students page specific functionality
function initializeStudentsPage() {
    console.log('Initializing Students page functionality');

    // Utility functions
    function getGradeBadgeClass(grade) {
        const gradeClasses = {
            'A+': 'grade-a-plus',
            'A': 'grade-a',
            'B+': 'grade-b-plus',
            'B': 'grade-b',
            'C+': 'grade-c-plus',
            'C': 'grade-c',
            'D': 'grade-d',
            'F': 'grade-f'
        };
        return gradeClasses[grade] || 'grade-f';
    }

    function showToast(message, type) {
        // Simple toast notification
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    // Apply Filters button
    $(document).on('click', '#applyFiltersBtn', function(e) {
        e.preventDefault();
        try {
            const trade = $('#tradeFilter').val();
            const classLevel = $('#classFilter').val();
            const section = $('#sectionFilter').val();

            const url = new URL(window.location);
            url.searchParams.set('trade', trade);
            url.searchParams.set('class', classLevel);
            url.searchParams.set('section', section);

            window.location.href = url.toString();
        } catch (error) {
            console.error('Error in applyFilters:', error);
            alert('Error applying filters. Please try again.');
        }
    });

    // Trade tab switching
    $(document).on('click', '.trade-tab', function(e) {
        e.preventDefault();
        try {
            const trade = $(this).data('trade');
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('trade', trade);
            window.location.href = currentUrl.toString();
        } catch (error) {
            console.error('Error in switchTrade:', error);
            alert('Error switching trade. Please try again.');
        }
    });

    // View Student Details button
    $(document).on('click', '.view-student-details-btn', function(e) {
        e.preventDefault();
        const studentId = $(this).data('student-id');

        try {
            console.log('Viewing student details for ID:', studentId);
            console.log('Student data available:', !!window.studentResultsData);
            console.log('Student data is array:', Array.isArray(window.studentResultsData));
            console.log('Student data length:', window.studentResultsData ? window.studentResultsData.length : 'N/A');

            // Check if student data is available
            if (!window.studentResultsData || !Array.isArray(window.studentResultsData)) {
                console.error('Student data not available');
                alert('Student data not available. Please refresh the page.');
                return;
            }

            if (window.studentResultsData.length === 0) {
                console.error('Student data array is empty');
                alert('No student data found. Please check if data is loaded.');
                return;
            }

            openStudentModal(studentId);
        } catch (error) {
            console.error('Error in viewStudentDetails:', error);
            alert('Error viewing student details. Please try again.');
        }
    });

    // Function to open student modal
    function openStudentModal(studentId) {
        try {
            // Find student data from the current results
            const studentData = window.studentResultsData.find(s => s.student_info.student_id == studentId);

            if (!studentData) {
                console.error('Student not found with ID:', studentId);
                alert('Student data not found');
                return;
            }

            const content = `
                <div class="space-y-6">
                    <div class="bg-gradient-to-r from-blue-600 to-green-600 text-white p-4 rounded-lg">
                        <h3 class="text-lg font-bold">${studentData.student_info.student_name}</h3>
                        <p>Student ID: ${studentData.student_info.student_id} | Roll: ${studentData.student_info.roll_number}</p>
                        <p>Class: ${studentData.student_info.class}-${studentData.student_info.section} | Trade: ${studentData.student_info.trade_full_name || studentData.student_info.trade}</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-green-50 p-4 rounded-lg text-center">
                            <p class="text-2xl font-bold text-green-600">${Number(studentData.overall_percentage || 0).toFixed(1)}%</p>
                            <p class="text-sm text-gray-600">Overall Percentage</p>
                        </div>
                        <div class="bg-blue-50 p-4 rounded-lg text-center">
                            <p class="text-2xl font-bold text-blue-600">${studentData.overall_grade}</p>
                            <p class="text-sm text-gray-600">Overall Grade</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg text-center">
                            <p class="text-2xl font-bold text-purple-600">${studentData.promotion_status}</p>
                            <p class="text-sm text-gray-600">Status</p>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-lg font-bold mb-4">Subject-wise Performance</h4>
                        <div class="overflow-x-auto">
                            <table class="w-full border border-gray-200 rounded-lg">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left">Subject</th>
                                        <th class="px-4 py-2 text-center">Category</th>
                                        <th class="px-4 py-2 text-center">Theory</th>
                                        <th class="px-4 py-2 text-center">Practical</th>
                                        <th class="px-4 py-2 text-center">CCE</th>
                                        <th class="px-4 py-2 text-center">Total</th>
                                        <th class="px-4 py-2 text-center">%</th>
                                        <th class="px-4 py-2 text-center">Grade</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${studentData.subjects.map(subject => `
                                        <tr class="border-t">
                                            <td class="px-4 py-2 font-medium">${subject.subject_name}</td>
                                            <td class="px-4 py-2 text-center">
                                                <span class="px-2 py-1 rounded text-xs ${subject.include_in_grand_total ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                                    ${subject.include_in_grand_total ? 'Core' : 'Additional'}
                                                </span>
                                            </td>
                                            <td class="px-4 py-2 text-center">${Number(subject.theory_marks || 0).toFixed(1)}</td>
                                            <td class="px-4 py-2 text-center">${Number(subject.practical_marks || 0).toFixed(1)}</td>
                                            <td class="px-4 py-2 text-center">${Number(subject.internal_marks || 0).toFixed(1)}</td>
                                            <td class="px-4 py-2 text-center font-bold">${Number(subject.total_marks || 0).toFixed(1)}</td>
                                            <td class="px-4 py-2 text-center">${Number(subject.percentage || 0).toFixed(1)}%</td>
                                            <td class="px-4 py-2 text-center">
                                                <span class="grade-badge ${getGradeBadgeClass(subject.grade)}">${subject.grade}</span>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            $('#studentDetailsContent').html(content);
            $('#studentDetailsModal').removeClass('hidden');
        } catch (error) {
            console.error('Error opening student modal:', error);
            alert('Error viewing student details. Please try again.');
        }
    }

    // Generate Score Card button
    $(document).on('click', '.generate-scorecard-btn', function(e) {
        e.preventDefault();
        const studentId = $(this).data('student-id');
        const $button = $(this);
        const originalContent = $button.html();

        $button.html('<div class="loading-spinner"></div>');
        $button.prop('disabled', true);

        fetch(`/exam-results/student/${studentId}/scorecard`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.open(data.url, '_blank');
                    showToast('Score card generated successfully!', 'success');
                } else {
                    showToast('Error generating score card: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Error generating score card', 'error');
            })
            .finally(() => {
                $button.html(originalContent);
                $button.prop('disabled', false);
            });
    });

    // Close Student Modal button
    $(document).on('click', '#closeStudentModalBtn', function(e) {
        e.preventDefault();
        $('#studentDetailsModal').addClass('hidden');
    });

    console.log('✅ All exam results students event handlers bound with jQuery');
}

// Export functions for global access
window.ExamResults = {
    toggleMobileMenu,
    getGradeBadgeClass,
    startAutoRefresh,
    initializeSessionSelector,
    initializeGradeBadges,
    initializeExamResultsSystem,
    initializeStudentsPage
};

console.log('Exam Results JavaScript module loaded');
