/**
 * Exam Results System JavaScript
 * Handles all functionality for the exam results management system
 */

// Mobile menu toggle
function toggleMobileMenu() {
    const sidebar = document.querySelector('.exam-sidebar');
    const overlay = document.querySelector('.mobile-menu-overlay');

    sidebar.classList.toggle('open');
    overlay.classList.toggle('open');
}

// Make function globally accessible
window.toggleMobileMenu = toggleMobileMenu;

// Session selector handling
function initializeSessionSelector() {
    const sessionSelector = document.getElementById('sessionSelector');

    if (sessionSelector) {
        sessionSelector.addEventListener('change', function() {
            const selectedSession = this.value;

            // Show loading indicator
            const originalText = this.options[this.selectedIndex].text;
            this.options[this.selectedIndex].text = 'Loading...';
            this.disabled = true;

            // Send AJAX request to update session
            fetch('/exam-results/change-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ session: selectedSession })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the current page to reflect new session data
                    window.location.reload();
                } else {
                    alert('Error changing session: ' + (data.error || 'Unknown error'));
                    this.options[this.selectedIndex].text = originalText;
                    this.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error changing session');
                this.options[this.selectedIndex].text = originalText;
                this.disabled = false;
            });
        });
    }
}

// Grade badge helper
function getGradeBadgeClass(grade) {
    const gradeClasses = {
        'A+': 'grade-a-plus',
        'A': 'grade-a',
        'B+': 'grade-b-plus',
        'B': 'grade-b',
        'C+': 'grade-c-plus',
        'C': 'grade-c',
        'D': 'grade-d',
        'F': 'grade-f'
    };
    return gradeClasses[grade] || 'grade-f';
}

// Auto-refresh functionality
function startAutoRefresh(interval = 300000) { // 5 minutes default
    setInterval(() => {
        // Only refresh if user is active
        if (document.visibilityState === 'visible') {
            console.log('Auto-refreshing data...');
            // Add specific refresh logic here if needed
        }
    }, interval);
}

// Initialize grade badges
function initializeGradeBadges() {
    document.querySelectorAll('.grade-badge').forEach(badge => {
        const grade = badge.textContent.trim();
        badge.classList.add(getGradeBadgeClass(grade));
    });
}

// Main initialization function
function initializeExamResultsSystem() {
    console.log('Exam Results System initialized');
    
    // Initialize all components
    initializeSessionSelector();
    initializeGradeBadges();
    
    // Optional: Start auto-refresh if needed
    // startAutoRefresh();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', initializeExamResultsSystem);

// Export functions for global access
window.ExamResults = {
    toggleMobileMenu,
    getGradeBadgeClass,
    startAutoRefresh,
    initializeSessionSelector,
    initializeGradeBadges,
    initializeExamResultsSystem
};

console.log('Exam Results JavaScript module loaded');
