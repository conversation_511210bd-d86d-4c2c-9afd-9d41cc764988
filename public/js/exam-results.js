/**
 * Exam Results System JavaScript
 * Handles all functionality for the exam results management system
 */

// Mobile menu toggle
function toggleMobileMenu() {
    const sidebar = document.querySelector('.exam-sidebar');
    const overlay = document.querySelector('.mobile-menu-overlay');

    sidebar.classList.toggle('open');
    overlay.classList.toggle('open');
}

// Make function globally accessible
window.toggleMobileMenu = toggleMobileMenu;

// Session selector handling
function initializeSessionSelector() {
    const sessionSelector = document.getElementById('sessionSelector');

    if (sessionSelector) {
        sessionSelector.addEventListener('change', function() {
            const selectedSession = this.value;

            // Show loading indicator
            const originalText = this.options[this.selectedIndex].text;
            this.options[this.selectedIndex].text = 'Loading...';
            this.disabled = true;

            // Send AJAX request to update session
            fetch('/exam-results/change-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ session: selectedSession })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the current page to reflect new session data
                    window.location.reload();
                } else {
                    alert('Error changing session: ' + (data.error || 'Unknown error'));
                    this.options[this.selectedIndex].text = originalText;
                    this.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error changing session');
                this.options[this.selectedIndex].text = originalText;
                this.disabled = false;
            });
        });
    }
}

// Grade badge helper
function getGradeBadgeClass(grade) {
    const gradeClasses = {
        'A+': 'grade-a-plus',
        'A': 'grade-a',
        'B+': 'grade-b-plus',
        'B': 'grade-b',
        'C+': 'grade-c-plus',
        'C': 'grade-c',
        'D': 'grade-d',
        'F': 'grade-f'
    };
    return gradeClasses[grade] || 'grade-f';
}

// Auto-refresh functionality
function startAutoRefresh(interval = 300000) { // 5 minutes default
    setInterval(() => {
        // Only refresh if user is active
        if (document.visibilityState === 'visible') {
            console.log('Auto-refreshing data...');
            // Add specific refresh logic here if needed
        }
    }, interval);
}

// Initialize grade badges
function initializeGradeBadges() {
    document.querySelectorAll('.grade-badge').forEach(badge => {
        const grade = badge.textContent.trim();
        badge.classList.add(getGradeBadgeClass(grade));
    });
}

// Main initialization function
function initializeExamResultsSystem() {
    console.log('Exam Results System initialized');
    
    // Initialize all components
    initializeSessionSelector();
    initializeGradeBadges();
    
    // Optional: Start auto-refresh if needed
    // startAutoRefresh();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', initializeExamResultsSystem);

// Students page specific functionality
function initializeStudentsPage() {
    console.log('Initializing Students page functionality');

    // Utility functions
    function getGradeBadgeClass(grade) {
        const gradeClasses = {
            'A+': 'grade-a-plus',
            'A': 'grade-a',
            'B+': 'grade-b-plus',
            'B': 'grade-b',
            'C+': 'grade-c-plus',
            'C': 'grade-c',
            'D': 'grade-d',
            'F': 'grade-f'
        };
        return gradeClasses[grade] || 'grade-f';
    }

    function showToast(message, type) {
        // Simple toast notification
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    // Apply Filters button
    $(document).on('click', '#applyFiltersBtn', function(e) {
        e.preventDefault();
        try {
            const trade = $('#tradeFilter').val();
            const classLevel = $('#classFilter').val();
            const section = $('#sectionFilter').val();

            const url = new URL(window.location);
            url.searchParams.set('trade', trade);
            url.searchParams.set('class', classLevel);
            url.searchParams.set('section', section);

            window.location.href = url.toString();
        } catch (error) {
            console.error('Error in applyFilters:', error);
            alert('Error applying filters. Please try again.');
        }
    });

    // Trade tab switching
    $(document).on('click', '.trade-tab', function(e) {
        e.preventDefault();
        try {
            const trade = $(this).data('trade');
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('trade', trade);
            window.location.href = currentUrl.toString();
        } catch (error) {
            console.error('Error in switchTrade:', error);
            alert('Error switching trade. Please try again.');
        }
    });

    // View Student Details button
    $(document).on('click', '.view-student-details-btn', function(e) {
        e.preventDefault();
        const studentId = $(this).data('student-id');

        try {
            console.log('🔍 Viewing student details for ID:', studentId);
            console.log('📊 Student data available:', !!window.studentResultsData);
            console.log('📊 Student data is array:', Array.isArray(window.studentResultsData));
            console.log('📊 Student data length:', window.studentResultsData ? window.studentResultsData.length : 'N/A');

            // Check if student data is available
            if (!window.studentResultsData || !Array.isArray(window.studentResultsData)) {
                console.error('❌ Student data not available');
                alert('Student data not available. Please refresh the page.');
                return;
            }

            if (window.studentResultsData.length === 0) {
                console.error('❌ Student data array is empty');
                alert('No student data found. Please check if data is loaded.');
                return;
            }

            console.log('🎯 Opening modal for student ID:', studentId);
            openStudentModal(studentId);
        } catch (error) {
            console.error('❌ Error in viewStudentDetails:', error);
            console.error('❌ Error stack:', error.stack);
            alert('Error viewing student details. Please try again.');
        }
    });

    // Function to open student modal
    function openStudentModal(studentId) {
        try {
            console.log('🔍 Searching for student with ID:', studentId);
            console.log('📊 Available students:', window.studentResultsData.map(s => ({
                id: s.student_info.student_id,
                name: s.student_info.student_name
            })));

            // Find student data from the current results
            const studentData = window.studentResultsData.find(s => s.student_info.student_id == studentId);

            if (!studentData) {
                console.error('❌ Student not found with ID:', studentId);
                console.error('❌ Available student IDs:', window.studentResultsData.map(s => s.student_info.student_id));
                alert('Student data not found');
                return;
            }

            console.log('✅ Found student data:', studentData.student_info.student_name);
            console.log('📚 Student subjects:', studentData.subjects.length);

            // Calculate pass/fail status
            const coreSubjects = studentData.subjects.filter(s => s.include_in_grand_total);
            const failedCoreSubjects = coreSubjects.filter(s => s.result_status === 'FAIL');
            const overallResult = failedCoreSubjects.length >= 2 ? 'FAIL' : 'PASS';
            const resultColor = overallResult === 'PASS' ? 'text-green-600' : 'text-red-600';
            const resultBgColor = overallResult === 'PASS' ? 'bg-green-50' : 'bg-red-50';

            const content = `
                <div class="space-y-6">
                    <!-- Student Header -->
                    <div class="bg-gradient-to-r from-blue-600 to-green-600 text-white p-6 rounded-lg">
                        <h3 class="text-xl font-bold mb-2">${studentData.student_info.student_name}</h3>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <p><strong>Student ID:</strong> ${studentData.student_info.student_id}</p>
                                <p><strong>Roll Number:</strong> ${studentData.student_info.roll_number || 'N/A'}</p>
                            </div>
                            <div>
                                <p><strong>Class:</strong> ${studentData.student_info.class}-${studentData.student_info.section}</p>
                                <p><strong>Trade:</strong> ${studentData.student_info.trade_full_name || studentData.student_info.trade}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Overall Performance Summary -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg text-center">
                            <p class="text-2xl font-bold text-blue-600">${Number(studentData.overall_percentage || 0).toFixed(1)}%</p>
                            <p class="text-sm text-gray-600">Grand Total %</p>
                            <p class="text-xs text-gray-500">(Core subjects only)</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg text-center">
                            <p class="text-2xl font-bold text-purple-600">${studentData.overall_grade || 'N/A'}</p>
                            <p class="text-sm text-gray-600">Overall Grade</p>
                        </div>
                        <div class="${resultBgColor} p-4 rounded-lg text-center">
                            <p class="text-2xl font-bold ${resultColor}">${overallResult}</p>
                            <p class="text-sm text-gray-600">Final Result</p>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg text-center">
                            <p class="text-2xl font-bold text-yellow-600">${failedCoreSubjects.length}</p>
                            <p class="text-sm text-gray-600">Failed Core Subjects</p>
                        </div>
                    </div>

                    <!-- Subject-wise Performance -->
                    <div>
                        <h4 class="text-lg font-bold mb-4 flex items-center">
                            <span class="mr-2">📊</span>
                            Subject-wise Performance
                        </h4>

                        <!-- Core Subjects -->
                        <div class="mb-6">
                            <h5 class="text-md font-semibold mb-3 text-green-700 flex items-center">
                                <span class="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                                Core Subjects (Included in Grand Total)
                            </h5>
                            <div class="overflow-x-auto">
                                <table class="w-full border border-gray-200 rounded-lg">
                                    <thead class="bg-green-50">
                                        <tr>
                                            <th class="px-3 py-2 text-left text-xs font-medium">Subject</th>
                                            <th class="px-3 py-2 text-center text-xs font-medium">Theory</th>
                                            <th class="px-3 py-2 text-center text-xs font-medium">Practical</th>
                                            <th class="px-3 py-2 text-center text-xs font-medium">CCE</th>
                                            <th class="px-3 py-2 text-center text-xs font-medium">Total</th>
                                            <th class="px-3 py-2 text-center text-xs font-medium">%</th>
                                            <th class="px-3 py-2 text-center text-xs font-medium">Grade</th>
                                            <th class="px-3 py-2 text-center text-xs font-medium">Result</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${coreSubjects.map(subject => {
                                            const subjectResultColor = subject.result_status === 'PASS' ? 'text-green-600' : 'text-red-600';
                                            const subjectResultBg = subject.result_status === 'PASS' ? 'bg-green-100' : 'bg-red-100';
                                            return `
                                                <tr class="border-t hover:bg-gray-50">
                                                    <td class="px-3 py-2 font-medium text-sm">${subject.subject_name}</td>
                                                    <td class="px-3 py-2 text-center text-sm">${Number(subject.theory_marks || 0).toFixed(1)}</td>
                                                    <td class="px-3 py-2 text-center text-sm">${Number(subject.practical_marks || 0).toFixed(1)}</td>
                                                    <td class="px-3 py-2 text-center text-sm">${Number(subject.internal_marks || 0).toFixed(1)}</td>
                                                    <td class="px-3 py-2 text-center font-bold text-sm">${Number(subject.total_marks || 0).toFixed(1)}</td>
                                                    <td class="px-3 py-2 text-center text-sm">${Number(subject.percentage || 0).toFixed(1)}%</td>
                                                    <td class="px-3 py-2 text-center">
                                                        <span class="grade-badge ${getGradeBadgeClass(subject.grade)} text-xs">${subject.grade}</span>
                                                    </td>
                                                    <td class="px-3 py-2 text-center">
                                                        <span class="px-2 py-1 rounded text-xs font-medium ${subjectResultBg} ${subjectResultColor}">
                                                            ${subject.result_status}
                                                        </span>
                                                    </td>
                                                </tr>
                                            `;
                                        }).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Additional Subjects -->
                        ${studentData.subjects.filter(s => !s.include_in_grand_total).length > 0 ? `
                            <div>
                                <h5 class="text-md font-semibold mb-3 text-gray-700 flex items-center">
                                    <span class="w-3 h-3 bg-gray-400 rounded-full mr-2"></span>
                                    Additional Subjects (Not included in Grand Total)
                                </h5>
                                <div class="overflow-x-auto">
                                    <table class="w-full border border-gray-200 rounded-lg">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-3 py-2 text-left text-xs font-medium">Subject</th>
                                                <th class="px-3 py-2 text-center text-xs font-medium">Theory</th>
                                                <th class="px-3 py-2 text-center text-xs font-medium">Practical</th>
                                                <th class="px-3 py-2 text-center text-xs font-medium">CCE</th>
                                                <th class="px-3 py-2 text-center text-xs font-medium">Total</th>
                                                <th class="px-3 py-2 text-center text-xs font-medium">%</th>
                                                <th class="px-3 py-2 text-center text-xs font-medium">Grade</th>
                                                <th class="px-3 py-2 text-center text-xs font-medium">Result</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${studentData.subjects.filter(s => !s.include_in_grand_total).map(subject => {
                                                const subjectResultColor = subject.result_status === 'PASS' ? 'text-green-600' : 'text-red-600';
                                                const subjectResultBg = subject.result_status === 'PASS' ? 'bg-green-100' : 'bg-red-100';
                                                return `
                                                    <tr class="border-t hover:bg-gray-50">
                                                        <td class="px-3 py-2 font-medium text-sm">${subject.subject_name}</td>
                                                        <td class="px-3 py-2 text-center text-sm">${Number(subject.theory_marks || 0).toFixed(1)}</td>
                                                        <td class="px-3 py-2 text-center text-sm">${Number(subject.practical_marks || 0).toFixed(1)}</td>
                                                        <td class="px-3 py-2 text-center text-sm">${Number(subject.internal_marks || 0).toFixed(1)}</td>
                                                        <td class="px-3 py-2 text-center font-bold text-sm">${Number(subject.total_marks || 0).toFixed(1)}</td>
                                                        <td class="px-3 py-2 text-center text-sm">${Number(subject.percentage || 0).toFixed(1)}%</td>
                                                        <td class="px-3 py-2 text-center">
                                                            <span class="grade-badge ${getGradeBadgeClass(subject.grade)} text-xs">${subject.grade}</span>
                                                        </td>
                                                        <td class="px-3 py-2 text-center">
                                                            <span class="px-2 py-1 rounded text-xs font-medium ${subjectResultBg} ${subjectResultColor}">
                                                                ${subject.result_status}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                `;
                                            }).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        ` : ''}

                        <!-- Pass/Fail Criteria Information -->
                        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                            <h6 class="font-semibold text-blue-800 mb-2">📋 Pass/Fail Criteria:</h6>
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• <strong>Individual Subject:</strong> Pass if meets minimum passing criteria for that subject</li>
                                <li>• <strong>Overall Result:</strong> FAIL if student fails in 2 or more core subjects, otherwise PASS</li>
                                <li>• <strong>Grand Total:</strong> Calculated from core subjects only (excludes additional subjects)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;

            // Check if modal elements exist
            const modalElement = $('#studentDetailsModal');
            const contentElement = $('#studentDetailsContent');

            console.log('🔍 Modal element found:', modalElement.length > 0);
            console.log('🔍 Content element found:', contentElement.length > 0);

            if (modalElement.length === 0) {
                console.error('❌ Modal element not found');
                alert('Modal element not found. Please refresh the page.');
                return;
            }

            if (contentElement.length === 0) {
                console.error('❌ Content element not found');
                alert('Content element not found. Please refresh the page.');
                return;
            }

            console.log('📝 Setting modal content...');
            contentElement.html(content);

            console.log('👁️ Showing modal...');
            modalElement.removeClass('hidden');

            console.log('✅ Modal should now be visible');
        } catch (error) {
            console.error('❌ Error opening student modal:', error);
            console.error('❌ Error stack:', error.stack);
            alert('Error viewing student details. Please try again.');
        }
    }

    // Generate Score Card button
    $(document).on('click', '.generate-scorecard-btn', function(e) {
        e.preventDefault();
        const studentId = $(this).data('student-id');
        const $button = $(this);
        const originalContent = $button.html();

        $button.html('<div class="loading-spinner"></div>');
        $button.prop('disabled', true);

        fetch(`/exam-results/student/${studentId}/scorecard`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.open(data.url, '_blank');
                    showToast('Score card generated successfully!', 'success');
                } else {
                    showToast('Error generating score card: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Error generating score card', 'error');
            })
            .finally(() => {
                $button.html(originalContent);
                $button.prop('disabled', false);
            });
    });

    // Close Student Modal button
    $(document).on('click', '#closeStudentModalBtn', function(e) {
        e.preventDefault();
        $('#studentDetailsModal').addClass('hidden');
    });

    console.log('✅ All exam results students event handlers bound with jQuery');
}

// Export functions for global access
window.ExamResults = {
    toggleMobileMenu,
    getGradeBadgeClass,
    startAutoRefresh,
    initializeSessionSelector,
    initializeGradeBadges,
    initializeExamResultsSystem,
    initializeStudentsPage
};

console.log('Exam Results JavaScript module loaded');
