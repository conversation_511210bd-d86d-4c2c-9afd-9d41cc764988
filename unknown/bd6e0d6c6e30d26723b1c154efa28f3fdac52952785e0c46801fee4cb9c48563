-- =====================================================================================
-- TEST LINKED ACADEMIC SYSTEM
-- =====================================================================================
-- This script tests the linked academic system and demonstrates the relationships
-- =====================================================================================

-- Test 1: Show the linked curriculum structure
SELECT 'LINKED CURRICULUM STRUCTURE:' AS info;
SELECT 
    t.trade_code,
    t.trade_name,
    c.class_level,
    s.subject_code,
    s.subject_name,
    c.subject_type,
    s.include_in_grand_total,
    CASE 
        WHEN c.subject_type = 'compulsory_core' THEN 'Core Subject (Counts in Total)'
        WHEN c.subject_type = 'selective_option' THEN 'Choose One (Counts in Total)'
        WHEN c.subject_type = 'compulsory_language' THEN 'Language (Counts in Total)'
        WHEN c.subject_type = 'additional_compulsory' THEN 'Additional Compulsory (NOT in Total)'
        WHEN c.subject_type = 'optional' THEN 'Optional (NOT in Total)'
        ELSE 'Other'
    END AS subject_type_description
FROM trades t
JOIN curriculum c ON t.id = c.trade_id
JOIN subjects s ON c.subject_id = s.id
WHERE c.academic_session = '2023-2024'
ORDER BY t.trade_code, c.class_level, s.subject_order;

-- Test 2: Show class structure with all links
SELECT 'CLASS STRUCTURE WITH LINKS:' AS info;
SELECT 
    cs.id AS class_section_id,
    cs.class_level,
    t.trade_code,
    t.trade_name,
    sec.section_code,
    sec.section_name,
    cs.academic_session,
    cs.total_students,
    cs.is_active
FROM class_sections cs
JOIN trades t ON cs.trade_id = t.id
JOIN sections sec ON cs.section_id = sec.id
WHERE cs.academic_session = '2023-2024'
ORDER BY cs.class_level, t.trade_code, sec.section_code;

-- Test 3: Show subjects that count vs don't count in grand total
SELECT 'SUBJECTS THAT COUNT IN GRAND TOTAL:' AS info;
SELECT subject_code, subject_name, subject_category, include_in_grand_total
FROM subjects 
WHERE include_in_grand_total = TRUE
ORDER BY subject_order;

SELECT 'SUBJECTS THAT DO NOT COUNT IN GRAND TOTAL:' AS info;
SELECT subject_code, subject_name, subject_category, include_in_grand_total
FROM subjects 
WHERE include_in_grand_total = FALSE
ORDER BY subject_order;

-- Test 4: Show Medical trade curriculum specifically
SELECT 'MEDICAL TRADE CURRICULUM:' AS info;
SELECT 
    c.class_level,
    s.subject_code,
    s.subject_name,
    c.subject_type,
    s.include_in_grand_total,
    CASE 
        WHEN s.include_in_grand_total = TRUE THEN 'COUNTS IN TOTAL'
        ELSE 'NOT IN TOTAL'
    END AS grade_calculation_status
FROM trades t
JOIN curriculum c ON t.id = c.trade_id
JOIN subjects s ON c.subject_id = s.id
WHERE t.trade_code = 'MED' AND c.academic_session = '2023-2024'
ORDER BY c.class_level, s.subject_order;

-- Test 5: Show Non-Medical trade curriculum specifically
SELECT 'NON-MEDICAL TRADE CURRICULUM:' AS info;
SELECT 
    c.class_level,
    s.subject_code,
    s.subject_name,
    c.subject_type,
    s.include_in_grand_total,
    CASE 
        WHEN s.include_in_grand_total = TRUE THEN 'COUNTS IN TOTAL'
        ELSE 'NOT IN TOTAL'
    END AS grade_calculation_status
FROM trades t
JOIN curriculum c ON t.id = c.trade_id
JOIN subjects s ON c.subject_id = s.id
WHERE t.trade_code = 'NON_MED' AND c.academic_session = '2023-2024'
ORDER BY c.class_level, s.subject_order;

-- Test 6: Show Commerce trade curriculum specifically
SELECT 'COMMERCE TRADE CURRICULUM:' AS info;
SELECT 
    c.class_level,
    s.subject_code,
    s.subject_name,
    c.subject_type,
    s.include_in_grand_total,
    CASE 
        WHEN s.include_in_grand_total = TRUE THEN 'COUNTS IN TOTAL'
        ELSE 'NOT IN TOTAL'
    END AS grade_calculation_status
FROM trades t
JOIN curriculum c ON t.id = c.trade_id
JOIN subjects s ON c.subject_id = s.id
WHERE t.trade_code = 'COMM' AND c.academic_session = '2023-2024'
ORDER BY c.class_level, s.subject_order;

-- Test 7: Verify foreign key relationships
SELECT 'FOREIGN KEY RELATIONSHIPS VERIFICATION:' AS info;
SELECT 
    'trades -> class_sections' AS relationship,
    COUNT(*) AS linked_records
FROM trades t
JOIN class_sections cs ON t.id = cs.trade_id

UNION ALL

SELECT 
    'sections -> class_sections' AS relationship,
    COUNT(*) AS linked_records
FROM sections s
JOIN class_sections cs ON s.id = cs.section_id

UNION ALL

SELECT 
    'trades -> curriculum' AS relationship,
    COUNT(*) AS linked_records
FROM trades t
JOIN curriculum c ON t.id = c.trade_id

UNION ALL

SELECT 
    'subjects -> curriculum' AS relationship,
    COUNT(*) AS linked_records
FROM subjects s
JOIN curriculum c ON s.id = c.subject_id;

-- Test 8: Show summary statistics
SELECT 'SYSTEM SUMMARY STATISTICS:' AS info;
SELECT 
    'Total Trades' AS metric,
    COUNT(*) AS count
FROM trades

UNION ALL

SELECT 
    'Total Subjects' AS metric,
    COUNT(*) AS count
FROM subjects

UNION ALL

SELECT 
    'Total Class Sections' AS metric,
    COUNT(*) AS count
FROM class_sections

UNION ALL

SELECT 
    'Total Curriculum Entries' AS metric,
    COUNT(*) AS count
FROM curriculum

UNION ALL

SELECT 
    'Subjects Counting in Grand Total' AS metric,
    COUNT(*) AS count
FROM subjects
WHERE include_in_grand_total = TRUE

UNION ALL

SELECT 
    'Additional/Optional Subjects' AS metric,
    COUNT(*) AS count
FROM subjects
WHERE include_in_grand_total = FALSE;
